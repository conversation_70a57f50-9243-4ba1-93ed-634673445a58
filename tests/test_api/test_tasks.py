"""API integration tests for task management endpoints.

This module contains comprehensive tests for the task management API
including CRUD operations, time tracking, dependencies, and analytics.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.hours_8760.models.task import Task, TimeEntry, TaskStatus, TaskPriority
from src.hours_8760.models.user import User
from src.hours_8760.models.life_area import LifeArea
from src.hours_8760.models.goal import MajorGoal


class TestTaskAPI:
    """Test suite for task management API endpoints."""

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession) -> User:
        """Create a test user."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_life_area(self, db_session: AsyncSession, test_user: User) -> LifeArea:
        """Create a test life area."""
        life_area = LifeArea(
            name="Health & Fitness",
            description="Physical and mental health",
            user_id=test_user.id
        )
        db_session.add(life_area)
        await db_session.commit()
        await db_session.refresh(life_area)
        return life_area

    @pytest.fixture
    async def test_goal(self, db_session: AsyncSession, test_life_area: LifeArea) -> MajorGoal:
        """Create a test goal."""
        goal = MajorGoal(
            title="Get Fit",
            description="Improve overall fitness",
            life_area_id=test_life_area.id,
            target_date=datetime.utcnow() + timedelta(days=90)
        )
        db_session.add(goal)
        await db_session.commit()
        await db_session.refresh(goal)
        return goal

    @pytest.fixture
    async def test_task(self, db_session: AsyncSession, test_goal: MajorGoal) -> Task:
        """Create a test task."""
        task = Task(
            title="Morning Workout",
            description="30-minute morning exercise routine",
            status=TaskStatus.TODO,
            priority=TaskPriority.HIGH,
            goal_id=test_goal.id,
            life_area_id=test_goal.life_area_id,
            estimated_hours=0.5,
            due_date=datetime.utcnow() + timedelta(days=1)
        )
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        return task

    async def test_get_tasks_empty(self, client: AsyncClient, auth_headers: dict):
        """Test getting tasks when none exist."""
        response = await client.get("/api/v1/tasks/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["tasks"] == []
        assert data["total"] == 0
        assert data["page"] == 1
        assert data["per_page"] == 20
        assert data["has_next"] is False
        assert data["has_prev"] is False

    async def test_create_task(self, client: AsyncClient, auth_headers: dict, test_goal: MajorGoal):
        """Test creating a new task."""
        task_data = {
            "title": "Read Health Book",
            "description": "Read a book about nutrition",
            "status": "todo",
            "priority": "medium",
            "goal_id": str(test_goal.id),
            "life_area_id": str(test_goal.life_area_id),
            "estimated_hours": 2.0,
            "due_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
            "tags": ["reading", "health"]
        }
        
        response = await client.post("/api/v1/tasks/", json=task_data, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == task_data["title"]
        assert data["description"] == task_data["description"]
        assert data["status"] == task_data["status"]
        assert data["priority"] == task_data["priority"]
        assert data["goal_id"] == task_data["goal_id"]
        assert data["life_area_id"] == task_data["life_area_id"]
        assert data["estimated_hours"] == task_data["estimated_hours"]
        assert data["actual_hours"] == 0.0
        assert data["is_completed"] is False
        assert data["is_overdue"] is False
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

    async def test_create_task_validation_error(self, client: AsyncClient, auth_headers: dict):
        """Test creating a task with validation errors."""
        task_data = {
            "title": "",  # Empty title should fail validation
            "status": "invalid_status",  # Invalid status
            "priority": "invalid_priority"  # Invalid priority
        }
        
        response = await client.post("/api/v1/tasks/", json=task_data, headers=auth_headers)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data

    async def test_get_task_by_id(self, client: AsyncClient, auth_headers: dict, test_task: Task):
        """Test getting a specific task by ID."""
        response = await client.get(f"/api/v1/tasks/{test_task.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(test_task.id)
        assert data["title"] == test_task.title
        assert data["description"] == test_task.description
        assert data["status"] == test_task.status.value
        assert data["priority"] == test_task.priority.value

    async def test_get_task_not_found(self, client: AsyncClient, auth_headers: dict):
        """Test getting a non-existent task."""
        fake_id = uuid4()
        response = await client.get(f"/api/v1/tasks/{fake_id}", headers=auth_headers)
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Task not found"

    async def test_update_task(self, client: AsyncClient, auth_headers: dict, test_task: Task):
        """Test updating an existing task."""
        update_data = {
            "title": "Updated Morning Workout",
            "status": "in_progress",
            "priority": "low",
            "estimated_hours": 1.0
        }
        
        response = await client.put(
            f"/api/v1/tasks/{test_task.id}", 
            json=update_data, 
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["status"] == update_data["status"]
        assert data["priority"] == update_data["priority"]
        assert data["estimated_hours"] == update_data["estimated_hours"]

    async def test_update_task_not_found(self, client: AsyncClient, auth_headers: dict):
        """Test updating a non-existent task."""
        fake_id = uuid4()
        update_data = {"title": "Updated Title"}
        
        response = await client.put(
            f"/api/v1/tasks/{fake_id}", 
            json=update_data, 
            headers=auth_headers
        )
        
        assert response.status_code == 404

    async def test_delete_task(self, client: AsyncClient, auth_headers: dict, test_task: Task):
        """Test deleting a task."""
        response = await client.delete(f"/api/v1/tasks/{test_task.id}", headers=auth_headers)
        
        assert response.status_code == 204
        
        # Verify task is deleted
        get_response = await client.get(f"/api/v1/tasks/{test_task.id}", headers=auth_headers)
        assert get_response.status_code == 404

    async def test_delete_task_not_found(self, client: AsyncClient, auth_headers: dict):
        """Test deleting a non-existent task."""
        fake_id = uuid4()
        response = await client.delete(f"/api/v1/tasks/{fake_id}", headers=auth_headers)
        
        assert response.status_code == 404

    async def test_get_tasks_with_filters(
        self, 
        client: AsyncClient, 
        auth_headers: dict, 
        db_session: AsyncSession,
        test_goal: MajorGoal
    ):
        """Test getting tasks with various filters."""
        # Create multiple tasks with different properties
        tasks = [
            Task(
                title="High Priority Task",
                status=TaskStatus.TODO,
                priority=TaskPriority.HIGH,
                goal_id=test_goal.id,
                life_area_id=test_goal.life_area_id
            ),
            Task(
                title="In Progress Task",
                status=TaskStatus.IN_PROGRESS,
                priority=TaskPriority.MEDIUM,
                goal_id=test_goal.id,
                life_area_id=test_goal.life_area_id
            ),
            Task(
                title="Completed Task",
                status=TaskStatus.DONE,
                priority=TaskPriority.LOW,
                goal_id=test_goal.id,
                life_area_id=test_goal.life_area_id,
                completed_at=datetime.utcnow()
            )
        ]
        
        for task in tasks:
            db_session.add(task)
        await db_session.commit()
        
        # Test status filter
        response = await client.get(
            "/api/v1/tasks/?status=todo&status=in_progress", 
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["tasks"]) == 2
        
        # Test priority filter
        response = await client.get(
            "/api/v1/tasks/?priority=high", 
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["tasks"]) == 1
        assert data["tasks"][0]["priority"] == "high"
        
        # Test completion filter
        response = await client.get(
            "/api/v1/tasks/?is_completed=true", 
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["tasks"]) == 1
        assert data["tasks"][0]["status"] == "done"
        
        # Test search filter
        response = await client.get(
            "/api/v1/tasks/?search=High", 
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["tasks"]) == 1
        assert "High" in data["tasks"][0]["title"]

    async def test_get_tasks_pagination(
        self, 
        client: AsyncClient, 
        auth_headers: dict, 
        db_session: AsyncSession,
        test_goal: MajorGoal
    ):
        """Test task list pagination."""
        # Create 25 tasks
        tasks = []
        for i in range(25):
            task = Task(
                title=f"Task {i+1}",
                status=TaskStatus.TODO,
                priority=TaskPriority.MEDIUM,
                goal_id=test_goal.id,
                life_area_id=test_goal.life_area_id
            )
            tasks.append(task)
            db_session.add(task)
        
        await db_session.commit()
        
        # Test first page
        response = await client.get("/api/v1/tasks/?page=1&per_page=10", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["tasks"]) == 10
        assert data["total"] == 25
        assert data["page"] == 1
        assert data["per_page"] == 10
        assert data["has_next"] is True
        assert data["has_prev"] is False
        
        # Test second page
        response = await client.get("/api/v1/tasks/?page=2&per_page=10", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["tasks"]) == 10
        assert data["page"] == 2
        assert data["has_next"] is True
        assert data["has_prev"] is True
        
        # Test last page
        response = await client.get("/api/v1/tasks/?page=3&per_page=10", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["tasks"]) == 5
        assert data["page"] == 3
        assert data["has_next"] is False
        assert data["has_prev"] is True


class TestTimeTrackingAPI:
    """Test suite for time tracking API endpoints."""

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession) -> User:
        """Create a test user."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_task(self, db_session: AsyncSession, test_user: User) -> Task:
        """Create a test task."""
        task = Task(
            title="Test Task",
            description="Test task for time tracking",
            status=TaskStatus.TODO,
            priority=TaskPriority.MEDIUM,
            estimated_hours=2.0
        )
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        return task

    async def test_start_time_tracking(self, client: AsyncClient, auth_headers: dict, test_task: Task):
        """Test starting time tracking for a task."""
        time_entry_data = {
            "task_id": str(test_task.id),
            "start_time": datetime.utcnow().isoformat(),
            "notes": "Starting work on this task"
        }

        response = await client.post(
            "/api/v1/tasks/time-entries",
            json=time_entry_data,
            headers=auth_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["task_id"] == time_entry_data["task_id"]
        assert data["notes"] == time_entry_data["notes"]
        assert data["is_running"] is True
        assert data["end_time"] is None
        assert "id" in data

    async def test_start_time_tracking_task_not_found(self, client: AsyncClient, auth_headers: dict):
        """Test starting time tracking for non-existent task."""
        fake_task_id = uuid4()
        time_entry_data = {
            "task_id": str(fake_task_id),
            "start_time": datetime.utcnow().isoformat()
        }

        response = await client.post(
            "/api/v1/tasks/time-entries",
            json=time_entry_data,
            headers=auth_headers
        )

        assert response.status_code == 404

    async def test_stop_time_tracking(
        self,
        client: AsyncClient,
        auth_headers: dict,
        db_session: AsyncSession,
        test_task: Task
    ):
        """Test stopping time tracking."""
        # Create a running time entry
        start_time = datetime.utcnow() - timedelta(hours=1)
        time_entry = TimeEntry(
            task_id=test_task.id,
            start_time=start_time,
            notes="Working on task"
        )
        db_session.add(time_entry)
        await db_session.commit()
        await db_session.refresh(time_entry)

        # Stop the time entry
        end_time = datetime.utcnow()
        update_data = {
            "end_time": end_time.isoformat(),
            "notes": "Completed work session"
        }

        response = await client.put(
            f"/api/v1/tasks/time-entries/{time_entry.id}",
            json=update_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["is_running"] is False
        assert data["end_time"] is not None
        assert data["notes"] == update_data["notes"]
        assert data["duration_minutes"] > 0

    async def test_stop_time_tracking_not_found(self, client: AsyncClient, auth_headers: dict):
        """Test stopping non-existent time entry."""
        fake_id = uuid4()
        update_data = {"end_time": datetime.utcnow().isoformat()}

        response = await client.put(
            f"/api/v1/tasks/time-entries/{fake_id}",
            json=update_data,
            headers=auth_headers
        )

        assert response.status_code == 404


class TestTaskDependenciesAPI:
    """Test suite for task dependencies API endpoints."""

    @pytest.fixture
    async def test_tasks(self, db_session: AsyncSession) -> tuple[Task, Task]:
        """Create two test tasks for dependency testing."""
        task1 = Task(
            title="Task 1",
            description="First task",
            status=TaskStatus.TODO,
            priority=TaskPriority.MEDIUM
        )
        task2 = Task(
            title="Task 2",
            description="Second task",
            status=TaskStatus.TODO,
            priority=TaskPriority.MEDIUM
        )

        db_session.add(task1)
        db_session.add(task2)
        await db_session.commit()
        await db_session.refresh(task1)
        await db_session.refresh(task2)

        return task1, task2

    async def test_add_task_dependency(
        self,
        client: AsyncClient,
        auth_headers: dict,
        test_tasks: tuple[Task, Task]
    ):
        """Test adding a dependency between tasks."""
        task1, task2 = test_tasks

        dependency_data = {
            "target_task_id": str(task2.id),
            "dependency_type": "depends_on",
            "description": "Task 1 depends on Task 2"
        }

        response = await client.post(
            f"/api/v1/tasks/{task1.id}/dependencies",
            json=dependency_data,
            headers=auth_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["message"] == "Dependency added successfully"

    async def test_add_circular_dependency(
        self,
        client: AsyncClient,
        auth_headers: dict,
        test_tasks: tuple[Task, Task]
    ):
        """Test preventing circular dependencies."""
        task1, task2 = test_tasks

        # Add dependency: task1 depends on task2
        dependency_data = {
            "target_task_id": str(task2.id),
            "dependency_type": "depends_on"
        }

        response = await client.post(
            f"/api/v1/tasks/{task1.id}/dependencies",
            json=dependency_data,
            headers=auth_headers
        )
        assert response.status_code == 201

        # Try to add reverse dependency: task2 depends on task1 (should fail)
        reverse_dependency_data = {
            "target_task_id": str(task1.id),
            "dependency_type": "depends_on"
        }

        response = await client.post(
            f"/api/v1/tasks/{task2.id}/dependencies",
            json=reverse_dependency_data,
            headers=auth_headers
        )

        assert response.status_code == 400
        data = response.json()
        assert "circular dependency" in data["detail"].lower()


class TestTaskAnalyticsAPI:
    """Test suite for task analytics API endpoints."""

    @pytest.fixture
    async def test_tasks_with_data(
        self,
        db_session: AsyncSession,
        test_user: User
    ) -> list[Task]:
        """Create test tasks with various statuses and time entries."""
        tasks = []

        # Create tasks with different statuses
        for i, status in enumerate([TaskStatus.TODO, TaskStatus.IN_PROGRESS, TaskStatus.DONE]):
            task = Task(
                title=f"Task {i+1}",
                description=f"Test task {i+1}",
                status=status,
                priority=TaskPriority.MEDIUM,
                estimated_hours=2.0,
                actual_hours=1.5 if status == TaskStatus.DONE else 0.0,
                completed_at=datetime.utcnow() if status == TaskStatus.DONE else None
            )
            tasks.append(task)
            db_session.add(task)

        await db_session.commit()

        # Add time entries for completed task
        if tasks:
            time_entry = TimeEntry(
                task_id=tasks[-1].id,  # Last task (completed)
                start_time=datetime.utcnow() - timedelta(hours=2),
                end_time=datetime.utcnow() - timedelta(hours=0.5),
                duration_minutes=90,
                notes="Completed work"
            )
            db_session.add(time_entry)
            await db_session.commit()

        return tasks

    async def test_get_task_analytics(
        self,
        client: AsyncClient,
        auth_headers: dict,
        test_tasks_with_data: list[Task]
    ):
        """Test getting task analytics."""
        response = await client.get("/api/v1/tasks/analytics", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()

        # Check statistics
        assert "statistics" in data
        stats = data["statistics"]
        assert stats["total_tasks"] == 3
        assert stats["completed_tasks"] == 1
        assert stats["in_progress_tasks"] == 1
        assert stats["completion_rate"] == pytest.approx(1/3, rel=1e-2)

        # Check tasks by status
        assert "tasks_by_status" in data
        status_data = data["tasks_by_status"]
        assert status_data["todo"] == 1
        assert status_data["in_progress"] == 1
        assert status_data["done"] == 1

        # Check tasks by priority
        assert "tasks_by_priority" in data
        priority_data = data["tasks_by_priority"]
        assert priority_data["medium"] == 3

    async def test_get_task_analytics_with_date_range(
        self,
        client: AsyncClient,
        auth_headers: dict,
        test_tasks_with_data: list[Task]
    ):
        """Test getting task analytics with date range."""
        start_date = (datetime.utcnow() - timedelta(days=7)).isoformat()
        end_date = datetime.utcnow().isoformat()

        response = await client.get(
            f"/api/v1/tasks/analytics?start_date={start_date}&end_date={end_date}",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert "statistics" in data
        assert "tasks_by_status" in data
        assert "tasks_by_priority" in data
