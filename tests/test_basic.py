"""Basic tests to verify the test setup works."""

import pytest
import os


def test_environment_setup():
    """Test that the testing environment is properly configured."""
    assert os.getenv("TESTING") == "true"


def test_basic_imports():
    """Test that basic imports work."""
    from src.hours_8760.core.config import settings
    assert settings is not None


def test_basic_math():
    """Test that pytest is working correctly."""
    assert 1 + 1 == 2


@pytest.mark.asyncio
async def test_async_functionality():
    """Test that async functionality works."""
    async def async_function():
        return "async works"
    
    result = await async_function()
    assert result == "async works"
