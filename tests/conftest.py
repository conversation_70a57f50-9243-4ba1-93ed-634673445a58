"""Pytest configuration and fixtures."""

import asyncio
import os
from typing import AsyncGenerator, Generator

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from src.hours_8760.core.config import settings
# from src.hours_8760.core.database import get_db
# from src.hours_8760.models.base import Base
# from src.hours_8760.main import app

# Set testing environment
os.environ["TESTING"] = "true"

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine
engine = create_async_engine(
    TEST_DATABASE_URL,
    echo=False,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
)
TestSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a new database session for a test."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with TestSessionLocal() as session:
        yield session
        await session.rollback()
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture(scope="function")
def override_get_db(db_session: AsyncSession) -> None:
    """Override the get_db dependency."""
    async def _override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = _override_get_db


@pytest.fixture(scope="function")
def client(override_get_db) -> TestClient:
    """Create a test client."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="function")
async def async_client(override_get_db) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


# Task-specific fixtures
@pytest.fixture
async def test_user(db_session: AsyncSession):
    """Create a test user for authentication."""
    from src.hours_8760.models.user import User

    user = User(
        email="<EMAIL>",
        username="testuser",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "secret"
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user) -> dict:
    """Create authentication headers for test requests."""
    from src.hours_8760.core.security import create_access_token

    access_token = create_access_token(data={"sub": str(test_user.id)})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def sample_task_data() -> dict:
    """Sample task data for testing."""
    return {
        "title": "Sample Task",
        "description": "This is a sample task for testing",
        "status": "todo",
        "priority": "medium",
        "estimated_hours": 2.0,
        "tags": ["test", "sample"]
    }


class TestDataFactory:
    """Factory class for creating test data."""

    @staticmethod
    async def create_task(db_session: AsyncSession, **kwargs):
        """Create a test task with optional overrides."""
        from src.hours_8760.models.task import Task, TaskStatus, TaskPriority

        defaults = {
            "title": "Factory Task",
            "description": "Task created by factory",
            "status": TaskStatus.TODO,
            "priority": TaskPriority.MEDIUM,
            "estimated_hours": 1.0
        }
        defaults.update(kwargs)

        task = Task(**defaults)
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        return task


@pytest.fixture
def test_factory() -> TestDataFactory:
    """Provide test data factory."""
    return TestDataFactory()
