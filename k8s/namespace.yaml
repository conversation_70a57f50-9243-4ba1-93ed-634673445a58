apiVersion: v1
kind: Namespace
metadata:
  name: 8760-hours
  labels:
    name: 8760-hours
    app: 8760-hours-platform
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: 8760-hours-config
  namespace: 8760-hours
data:
  ENVIRONMENT: "production"
  DEBUG: "false"
  LOG_LEVEL: "INFO"
  PROJECT_NAME: "8760 Hours Life Planning Platform"
  API_VERSION: "v1"
  API_PREFIX: "/api/v1"
  DATABASE_BACKEND: "postgresql"
  POSTGRES_DB: "8760hours"
  POSTGRES_HOST: "postgres-service"
  POSTGRES_PORT: "5432"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  JWT_ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "30"
  REFRESH_TOKEN_EXPIRE_DAYS: "7"
  RATE_LIMIT_PER_MINUTE: "100"
  RATE_LIMIT_PER_HOUR: "2000"
  RATE_LIMIT_PER_DAY: "20000"
  CACHE_TTL_SECONDS: "300"
  ANALYTICS_CACHE_TTL_SECONDS: "600"
  MAX_REQUEST_SIZE_MB: "10"
  DATABASE_POOL_SIZE: "20"
  DATABASE_MAX_OVERFLOW: "30"
  MAX_LOGIN_ATTEMPTS: "5"
  LOCKOUT_DURATION_MINUTES: "15"
  PASSWORD_MIN_LENGTH: "8"
  REQUIRE_EMAIL_VERIFICATION: "true"
  SESSION_TIMEOUT_MINUTES: "60"
  UPLOAD_DIR: "/app/uploads"
  MAX_FILE_SIZE: "10485760"
  ENABLE_DOCS: "false"
  ENABLE_REDOC: "false"
  PROMETHEUS_ENABLED: "true"
  METRICS_PORT: "8001"
---
apiVersion: v1
kind: Secret
metadata:
  name: 8760-hours-secrets
  namespace: 8760-hours
type: Opaque
stringData:
  SECRET_KEY: "CHANGE_THIS_IN_PRODUCTION"
  JWT_SECRET_KEY: "CHANGE_THIS_JWT_SECRET_IN_PRODUCTION"
  POSTGRES_USER: "postgres"
  POSTGRES_PASSWORD: "CHANGE_THIS_PASSWORD_IN_PRODUCTION"
  DATABASE_URL: "postgresql+asyncpg://postgres:CHANGE_THIS_PASSWORD_IN_PRODUCTION@postgres-service:5432/8760hours"
  REDIS_URL: "redis://redis-service:6379/0"
  SMTP_USER: "<EMAIL>"
  SMTP_PASSWORD: "your-app-password"
  EMAILS_FROM_EMAIL: "<EMAIL>"
  SENTRY_DSN: "https://your-sentry-dsn-here"
