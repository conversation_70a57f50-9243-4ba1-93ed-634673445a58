#!/usr/bin/env node

/**
 * Translation Validation Script
 * 
 * This script checks for missing translation keys across all language files
 * by comparing them against the English base translation file.
 * 
 * Usage:
 *   node scripts/check-translations.js --lang=es    # Check specific language
 *   node scripts/check-translations.js --all       # Check all languages
 *   node scripts/check-translations.js --missing   # Show only missing keys
 */

const fs = require('fs');
const path = require('path');

// Configuration
const MESSAGES_DIR = path.join(__dirname, '../frontend/src/i18n/messages');
const BASE_LANG = 'en';
const SUPPORTED_LANGUAGES = ['en', 'es', 'fr', 'de', 'ja', 'zh', 'af', 'zu', 'l33t', 'upgoer'];

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function loadTranslationFile(lang) {
  const filePath = path.join(MESSAGES_DIR, `${lang}.json`);
  
  if (!fs.existsSync(filePath)) {
    console.error(colorize(`❌ Translation file not found: ${filePath}`, 'red'));
    return null;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(colorize(`❌ Error parsing ${lang}.json: ${error.message}`, 'red'));
    return null;
  }
}

function getAllKeys(obj, prefix = '') {
  let keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys = keys.concat(getAllKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

function getValueByPath(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

function checkTranslation(baseLang, targetLang, showMissingOnly = false) {
  console.log(colorize(`\n🔍 Checking ${targetLang.toUpperCase()} translations...`, 'cyan'));
  
  const baseTranslations = loadTranslationFile(baseLang);
  const targetTranslations = loadTranslationFile(targetLang);
  
  if (!baseTranslations || !targetTranslations) {
    return { missing: [], total: 0, coverage: 0 };
  }
  
  const baseKeys = getAllKeys(baseTranslations);
  const targetKeys = getAllKeys(targetTranslations);
  
  const missingKeys = [];
  const extraKeys = [];
  
  // Check for missing keys
  for (const key of baseKeys) {
    const targetValue = getValueByPath(targetTranslations, key);
    if (targetValue === undefined) {
      missingKeys.push(key);
    }
  }
  
  // Check for extra keys (not in base)
  for (const key of targetKeys) {
    const baseValue = getValueByPath(baseTranslations, key);
    if (baseValue === undefined) {
      extraKeys.push(key);
    }
  }
  
  const coverage = ((baseKeys.length - missingKeys.length) / baseKeys.length * 100).toFixed(1);
  
  // Report results
  if (!showMissingOnly) {
    console.log(`📊 Total keys in base (${baseLang}): ${colorize(baseKeys.length, 'blue')}`);
    console.log(`📊 Total keys in ${targetLang}: ${colorize(targetKeys.length, 'blue')}`);
    console.log(`📊 Coverage: ${colorize(`${coverage}%`, coverage >= 95 ? 'green' : coverage >= 80 ? 'yellow' : 'red')}`);
  }
  
  if (missingKeys.length > 0) {
    console.log(colorize(`\n❌ Missing keys (${missingKeys.length}):`, 'red'));
    missingKeys.forEach(key => {
      console.log(`  - ${key}`);
    });
  } else if (!showMissingOnly) {
    console.log(colorize('\n✅ No missing keys found!', 'green'));
  }
  
  if (extraKeys.length > 0 && !showMissingOnly) {
    console.log(colorize(`\n⚠️  Extra keys not in base (${extraKeys.length}):`, 'yellow'));
    extraKeys.forEach(key => {
      console.log(`  + ${key}`);
    });
  }
  
  return {
    missing: missingKeys,
    extra: extraKeys,
    total: baseKeys.length,
    coverage: parseFloat(coverage)
  };
}

function generateReport(results) {
  console.log(colorize('\n📋 TRANSLATION SUMMARY REPORT', 'magenta'));
  console.log('='.repeat(50));
  
  let totalMissing = 0;
  let totalKeys = 0;
  
  for (const [lang, result] of Object.entries(results)) {
    if (lang === BASE_LANG) continue;
    
    totalMissing += result.missing.length;
    totalKeys += result.total;
    
    const status = result.missing.length === 0 ? '✅' : 
                  result.coverage >= 95 ? '⚠️' : '❌';
    
    console.log(`${status} ${lang.toUpperCase().padEnd(8)} ${result.coverage.toString().padStart(5)}% (${result.missing.length} missing)`);
  }
  
  const overallCoverage = ((totalKeys - totalMissing) / totalKeys * 100).toFixed(1);
  console.log('='.repeat(50));
  console.log(`📊 Overall Coverage: ${colorize(`${overallCoverage}%`, overallCoverage >= 95 ? 'green' : 'yellow')}`);
  console.log(`📊 Total Missing: ${colorize(totalMissing, totalMissing === 0 ? 'green' : 'red')}`);
}

function main() {
  const args = process.argv.slice(2);
  const langArg = args.find(arg => arg.startsWith('--lang='));
  const showAll = args.includes('--all');
  const showMissingOnly = args.includes('--missing');
  
  console.log(colorize('🌐 Translation Validation Tool', 'cyan'));
  console.log(colorize('================================', 'cyan'));
  
  if (langArg) {
    // Check specific language
    const targetLang = langArg.split('=')[1];
    if (!SUPPORTED_LANGUAGES.includes(targetLang)) {
      console.error(colorize(`❌ Unsupported language: ${targetLang}`, 'red'));
      console.log(`Supported languages: ${SUPPORTED_LANGUAGES.join(', ')}`);
      process.exit(1);
    }
    
    checkTranslation(BASE_LANG, targetLang, showMissingOnly);
  } else if (showAll) {
    // Check all languages
    const results = {};
    
    for (const lang of SUPPORTED_LANGUAGES) {
      if (lang === BASE_LANG) continue;
      results[lang] = checkTranslation(BASE_LANG, lang, showMissingOnly);
    }
    
    if (!showMissingOnly) {
      generateReport(results);
    }
  } else {
    // Show usage
    console.log('Usage:');
    console.log('  node scripts/check-translations.js --lang=es    # Check specific language');
    console.log('  node scripts/check-translations.js --all       # Check all languages');
    console.log('  node scripts/check-translations.js --missing   # Show only missing keys');
    console.log('');
    console.log(`Supported languages: ${SUPPORTED_LANGUAGES.join(', ')}`);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  checkTranslation,
  getAllKeys,
  loadTranslationFile
};
