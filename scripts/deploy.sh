#!/bin/bash

# 8760 Hours Backend Deployment Script
# This script handles the deployment of the FastAPI backend

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${ENVIRONMENT:-production}
COMPOSE_FILE="docker-compose.backend.yml"
SERVICE_NAME="8760-hours"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        log_warning ".env file not found, creating from .env.example"
        if [ -f .env.example ]; then
            cp .env.example .env
            log_info "Please update .env file with production values"
        else
            log_error ".env.example file not found"
            exit 1
        fi
    fi
    
    log_success "Requirements check passed"
}

backup_database() {
    log_info "Creating database backup..."
    
    # Create backup directory
    mkdir -p backups
    
    # Generate backup filename with timestamp
    BACKUP_FILE="backups/8760hours_$(date +%Y%m%d_%H%M%S).sql"
    
    # Create database backup
    docker-compose -f $COMPOSE_FILE exec -T database pg_dump -U postgres 8760hours > $BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        log_success "Database backup created: $BACKUP_FILE"
    else
        log_warning "Database backup failed (this is normal for first deployment)"
    fi
}

build_images() {
    log_info "Building Docker images..."
    
    # Build the backend image
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    if [ $? -eq 0 ]; then
        log_success "Docker images built successfully"
    else
        log_error "Failed to build Docker images"
        exit 1
    fi
}

run_migrations() {
    log_info "Running database migrations..."
    
    # Run migrations
    docker-compose -f $COMPOSE_FILE run --rm migrations
    
    if [ $? -eq 0 ]; then
        log_success "Database migrations completed"
    else
        log_error "Database migrations failed"
        exit 1
    fi
}

deploy_services() {
    log_info "Deploying services..."
    
    # Start services
    docker-compose -f $COMPOSE_FILE up -d
    
    if [ $? -eq 0 ]; then
        log_success "Services deployed successfully"
    else
        log_error "Failed to deploy services"
        exit 1
    fi
}

health_check() {
    log_info "Performing health checks..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check API health
    if curl -f http://api.8760.localhost/health &> /dev/null; then
        log_success "API health check passed"
    else
        log_warning "API health check failed - checking container logs"
        docker-compose -f $COMPOSE_FILE logs api
    fi
    
    # Check database connectivity
    if docker-compose -f $COMPOSE_FILE exec -T database pg_isready -U postgres -d 8760hours &> /dev/null; then
        log_success "Database health check passed"
    else
        log_error "Database health check failed"
    fi
    
    # Check Redis connectivity
    if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli ping &> /dev/null; then
        log_success "Redis health check passed"
    else
        log_error "Redis health check failed"
    fi
}

show_status() {
    log_info "Service status:"
    docker-compose -f $COMPOSE_FILE ps
    
    log_info "Service URLs:"
    echo "  API: http://api.8760.localhost"
    echo "  Metrics: http://metrics.8760.localhost"
    echo "  API Docs: http://api.8760.localhost/docs"
}

cleanup_old_images() {
    log_info "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    log_success "Cleanup completed"
}

# Main deployment process
main() {
    log_info "Starting deployment of 8760 Hours Backend ($ENVIRONMENT)"
    
    # Check requirements
    check_requirements
    
    # Create database backup (if not first deployment)
    backup_database
    
    # Build Docker images
    build_images
    
    # Run database migrations
    run_migrations
    
    # Deploy services
    deploy_services
    
    # Perform health checks
    health_check
    
    # Show status
    show_status
    
    # Cleanup
    cleanup_old_images
    
    log_success "Deployment completed successfully!"
    log_info "Monitor logs with: docker-compose -f $COMPOSE_FILE logs -f"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        show_status
        ;;
    "logs")
        docker-compose -f $COMPOSE_FILE logs -f "${2:-api}"
        ;;
    "stop")
        log_info "Stopping services..."
        docker-compose -f $COMPOSE_FILE down
        log_success "Services stopped"
        ;;
    "restart")
        log_info "Restarting services..."
        docker-compose -f $COMPOSE_FILE restart
        log_success "Services restarted"
        ;;
    "backup")
        backup_database
        ;;
    "migrate")
        run_migrations
        ;;
    "health")
        health_check
        ;;
    *)
        echo "Usage: $0 {deploy|status|logs|stop|restart|backup|migrate|health}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment (default)"
        echo "  status  - Show service status"
        echo "  logs    - Show service logs"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  backup  - Create database backup"
        echo "  migrate - Run database migrations"
        echo "  health  - Perform health checks"
        exit 1
        ;;
esac
