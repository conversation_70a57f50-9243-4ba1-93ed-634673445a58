"""API dependencies for dependency injection.

This module contains FastAPI dependencies for authentication,
database sessions, and other common requirements.
"""

from typing import Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from src.hours_8760.core.database import get_db
from src.hours_8760.core.security import verify_token
from src.hours_8760.models.user import User
from src.hours_8760.services.user_service import UserService

# Security scheme for JWT tokens
security = HTTPBearer()


async def get_user_service(db: AsyncSession = Depends(get_db)) -> UserService:
    """Get user service dependency.

    Args:
        db: Database session.

    Returns:
        UserService: User service instance.
    """
    return UserService(db)


async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UUID:
    """Get current user ID from JW<PERSON> token.

    Args:
        credentials: HTTP authorization credentials.

    Returns:
        UUID: User ID from token.

    Raises:
        HTTPException: If token is invalid or expired.
    """
    token = credentials.credentials
    user_id_str = verify_token(token)

    if user_id_str is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        return UUID(user_id_str)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token format",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(
    user_id: UUID = Depends(get_current_user_id),
    user_service: UserService = Depends(get_user_service)
) -> User:
    """Get current user from JWT token.

    Args:
        user_id: User ID from token.
        user_service: User service instance.

    Returns:
        User: Current user object.

    Raises:
        HTTPException: If user not found.
    """
    user = await user_service.get_user_by_id(user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(
        HTTPBearer(auto_error=False)
    ),
    user_service: UserService = Depends(get_user_service)
) -> Optional[User]:
    """Get current user from JWT token (optional).

    Args:
        credentials: Optional HTTP authorization credentials.
        user_service: User service instance.

    Returns:
        Optional[User]: User object if valid token, None otherwise.
    """
    if credentials is None:
        return None

    token = credentials.credentials
    user_id_str = verify_token(token)

    if user_id_str is None:
        return None

    try:
        user_id = UUID(user_id_str)
        return await user_service.get_user_by_id(user_id)
    except ValueError:
        return None
DatabaseSession = Depends(get_db)
