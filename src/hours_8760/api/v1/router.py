"""Main API v1 router configuration.

This module sets up the main API router and includes all endpoint routers.
"""

from fastapi import APIRouter

from src.hours_8760.core.config import settings
from src.hours_8760.utils.service_url_manager import get_url_manager

# Create main API router
api_router = APIRouter(prefix=settings.API_PREFIX)

# Health check endpoint
@api_router.get("/health", tags=["health"])
async def health_check():
    """Health check endpoint."""
    try:
        url_manager = get_url_manager()
        service_urls = url_manager.get_all_service_urls()
    except Exception:
        service_urls = {}

    return {
        "status": "healthy",
        "version": settings.API_VERSION,
        "environment": settings.ENVIRONMENT,
        "services": service_urls
    }

# Service discovery endpoint
@api_router.get("/services", tags=["services"])
async def get_services():
    """Get all service URLs for current environment."""
    try:
        url_manager = get_url_manager()
        return {
            "environment": url_manager.environment,
            "services": url_manager.get_all_service_urls(),
            "health_checks": url_manager.health_check_urls(),
            "available_environments": url_manager.list_environments()
        }
    except Exception as e:
        return {"error": str(e), "services": {}}

# Include authentication endpoints
from src.hours_8760.api.v1.endpoints import auth
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# Include life areas endpoints
from src.hours_8760.api.v1.endpoints import life_areas
api_router.include_router(life_areas.router, prefix="/life-areas", tags=["life-areas"])

# Include planning sessions endpoints
from src.hours_8760.api.v1.endpoints import planning_sessions
api_router.include_router(planning_sessions.router, prefix="/planning-sessions", tags=["planning-sessions"])

# Include goals endpoints
from src.hours_8760.api.v1.endpoints import goals
api_router.include_router(goals.router, prefix="/goals", tags=["goals"])

# Include reviews endpoints
from src.hours_8760.api.v1.endpoints import reviews
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])

# Include other routers here as they are created
