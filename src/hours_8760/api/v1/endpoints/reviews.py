"""Review API endpoints for managing review sessions and progress tracking.

This module provides API endpoints for review management including
CRUD operations for review sessions, progress entries, and analytics.
"""

from datetime import date
from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query

from src.hours_8760.api.v1.dependencies import get_current_user, get_db
from src.hours_8760.models.user import User
from src.hours_8760.schemas.review import (
    ReviewSessionCreate,
    ReviewSessionUpdate,
    ReviewSessionResponse,
    ProgressEntryCreate,
    ProgressEntryUpdate,
    ProgressEntryResponse,
    ProgressAnalytics
)
from src.hours_8760.schemas.base import SuccessResponse
from src.hours_8760.services.review_service import ReviewService
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


async def get_review_service(db: AsyncSession = Depends(get_db)) -> ReviewService:
    """Get review service dependency."""
    return ReviewService(db)


# Review Sessions endpoints
@router.get("/sessions", response_model=List[ReviewSessionResponse])
async def get_review_sessions(
    review_type: Optional[str] = Query(None, description="Filter by review type (monthly, quarterly, annual)"),
    year: Optional[int] = Query(None, description="Filter by year"),
    planning_session_id: Optional[UUID] = Query(None, description="Filter by planning session"),
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Get current user's review sessions.
    
    Args:
        review_type: Optional type filter.
        year: Optional year filter.
        planning_session_id: Optional planning session filter.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        List of user's review sessions.
    """
    sessions = await review_service.get_user_review_sessions(
        user_id=current_user.id,
        review_type=review_type,
        year=year,
        planning_session_id=planning_session_id
    )
    return [ReviewSessionResponse.model_validate(session) for session in sessions]


@router.get("/sessions/{review_id}", response_model=ReviewSessionResponse)
async def get_review_session(
    review_id: UUID,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Get a specific review session by ID.
    
    Args:
        review_id: Review session's unique identifier.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Review session data.
        
    Raises:
        HTTPException: If review session not found.
    """
    session = await review_service.get_review_session_by_id(review_id, current_user.id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Review session not found"
        )
    
    return ReviewSessionResponse.model_validate(session)


@router.post("/sessions", response_model=ReviewSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_review_session(
    session_data: ReviewSessionCreate,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Create a new review session.
    
    Args:
        session_data: Review session creation data.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Created review session data.
        
    Raises:
        HTTPException: If review session already exists for the criteria.
    """
    try:
        session = await review_service.create_review_session(current_user.id, session_data)
        return ReviewSessionResponse.model_validate(session)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/sessions/{review_id}", response_model=ReviewSessionResponse)
async def update_review_session(
    review_id: UUID,
    session_data: ReviewSessionUpdate,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Update a review session.
    
    Args:
        review_id: Review session's unique identifier.
        session_data: Updated review session data.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Updated review session data.
        
    Raises:
        HTTPException: If review session not found.
    """
    session = await review_service.update_review_session(
        review_id, current_user.id, session_data
    )
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Review session not found"
        )
    
    return ReviewSessionResponse.model_validate(session)


@router.delete("/sessions/{review_id}", response_model=SuccessResponse)
async def delete_review_session(
    review_id: UUID,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Delete a review session.
    
    Args:
        review_id: Review session's unique identifier.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If review session not found.
    """
    success = await review_service.delete_review_session(review_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Review session not found"
        )
    
    return SuccessResponse(
        message="Review session successfully deleted",
        data={"review_id": str(review_id)}
    )


# Progress Entries endpoints
@router.get("/progress", response_model=List[ProgressEntryResponse])
async def get_progress_entries(
    major_goal_id: Optional[UUID] = Query(None, description="Filter by major goal"),
    sub_project_id: Optional[UUID] = Query(None, description="Filter by sub-project"),
    start_date: Optional[date] = Query(None, description="Filter by start date"),
    end_date: Optional[date] = Query(None, description="Filter by end date"),
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Get current user's progress entries.
    
    Args:
        major_goal_id: Optional major goal filter.
        sub_project_id: Optional sub-project filter.
        start_date: Optional start date filter.
        end_date: Optional end date filter.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        List of user's progress entries.
    """
    entries = await review_service.get_user_progress_entries(
        user_id=current_user.id,
        major_goal_id=major_goal_id,
        sub_project_id=sub_project_id,
        start_date=start_date,
        end_date=end_date
    )
    return [ProgressEntryResponse.model_validate(entry) for entry in entries]


@router.get("/progress/{entry_id}", response_model=ProgressEntryResponse)
async def get_progress_entry(
    entry_id: UUID,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Get a specific progress entry by ID.
    
    Args:
        entry_id: Progress entry's unique identifier.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Progress entry data.
        
    Raises:
        HTTPException: If progress entry not found.
    """
    entry = await review_service.get_progress_entry_by_id(entry_id, current_user.id)
    if not entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Progress entry not found"
        )
    
    return ProgressEntryResponse.model_validate(entry)


@router.post("/progress", response_model=ProgressEntryResponse, status_code=status.HTTP_201_CREATED)
async def create_progress_entry(
    entry_data: ProgressEntryCreate,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Create a new progress entry.
    
    Args:
        entry_data: Progress entry creation data.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Created progress entry data.
    """
    entry = await review_service.create_progress_entry(current_user.id, entry_data)
    return ProgressEntryResponse.model_validate(entry)


@router.put("/progress/{entry_id}", response_model=ProgressEntryResponse)
async def update_progress_entry(
    entry_id: UUID,
    entry_data: ProgressEntryUpdate,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Update a progress entry.
    
    Args:
        entry_id: Progress entry's unique identifier.
        entry_data: Updated progress entry data.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Updated progress entry data.
        
    Raises:
        HTTPException: If progress entry not found.
    """
    entry = await review_service.update_progress_entry(
        entry_id, current_user.id, entry_data
    )
    if not entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Progress entry not found"
        )
    
    return ProgressEntryResponse.model_validate(entry)


@router.delete("/progress/{entry_id}", response_model=SuccessResponse)
async def delete_progress_entry(
    entry_id: UUID,
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Delete a progress entry.
    
    Args:
        entry_id: Progress entry's unique identifier.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If progress entry not found.
    """
    success = await review_service.delete_progress_entry(entry_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Progress entry not found"
        )
    
    return SuccessResponse(
        message="Progress entry successfully deleted",
        data={"entry_id": str(entry_id)}
    )


# Analytics endpoints
@router.get("/analytics/progress", response_model=ProgressAnalytics)
async def get_progress_analytics(
    start_date: date = Query(..., description="Start date for analysis"),
    end_date: date = Query(..., description="End date for analysis"),
    current_user: User = Depends(get_current_user),
    review_service: ReviewService = Depends(get_review_service)
) -> Any:
    """Get progress analytics for a date range.
    
    Args:
        start_date: Start date for analysis.
        end_date: End date for analysis.
        current_user: Current authenticated user.
        review_service: Review service instance.
        
    Returns:
        Progress analytics data.
    """
    analytics_data = await review_service.get_progress_analytics(
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date
    )
    
    return ProgressAnalytics(**analytics_data)
