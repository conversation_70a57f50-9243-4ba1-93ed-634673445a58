"""Authentication endpoints for user registration, login, and profile management.

This module provides API endpoints for user authentication including
registration, login, logout, password reset, and profile management.
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials

from src.hours_8760.api.v1.dependencies import (
    get_user_service,
    get_current_user,
    security
)
from src.hours_8760.core.config import settings
from src.hours_8760.core.security import create_access_token
from src.hours_8760.models.user import User
from src.hours_8760.schemas.user import (
    UserCreate,
    UserRegister,
    UserUpdate,
    UserResponse,
    UserLogin,
    Token,
    PasswordReset,
    PasswordResetConfirm
)
from src.hours_8760.schemas.base import SuccessResponse, ErrorResponse
from src.hours_8760.services.user_service import UserService

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """Register a new user.
    
    Args:
        user_data: User registration data.
        user_service: User service instance.
        
    Returns:
        Created user data.
        
    Raises:
        HTTPException: If email already exists.
    """
    try:
        user = await user_service.register_user(user_data)
        return UserResponse.model_validate(user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/login", response_model=Token)
async def login(
    login_data: UserLogin,
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """Authenticate user and return access token.
    
    Args:
        login_data: User login credentials.
        user_service: User service instance.
        
    Returns:
        Access token and token information.
        
    Raises:
        HTTPException: If credentials are invalid.
    """
    user = await user_service.authenticate_user(
        email=login_data.email,
        password=login_data.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token = user_service.create_access_token_for_user(user)
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/logout", response_model=SuccessResponse)
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Any:
    """Logout user (client-side token invalidation).
    
    Note: Since we're using stateless JWT tokens, logout is handled
    client-side by removing the token. This endpoint serves as a
    confirmation and could be extended to maintain a token blacklist.
    
    Args:
        credentials: HTTP authorization credentials.
        
    Returns:
        Success message.
    """
    return SuccessResponse(
        message="Successfully logged out",
        data={"detail": "Token should be removed from client storage"}
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user)
) -> Any:
    """Get current user's profile information.
    
    Args:
        current_user: Current authenticated user.
        
    Returns:
        Current user's profile data.
    """
    return UserResponse.model_validate(current_user)


@router.put("/me", response_model=UserResponse)
async def update_current_user_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """Update current user's profile information.
    
    Args:
        user_data: Updated user data.
        current_user: Current authenticated user.
        user_service: User service instance.
        
    Returns:
        Updated user profile data.
        
    Raises:
        HTTPException: If email already exists for another user.
    """
    try:
        updated_user = await user_service.update_user(current_user.id, user_data)
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        return UserResponse.model_validate(updated_user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/me", response_model=SuccessResponse)
async def delete_current_user_account(
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """Delete current user's account (soft delete).
    
    Args:
        current_user: Current authenticated user.
        user_service: User service instance.
        
    Returns:
        Success message.
    """
    success = await user_service.delete_user(current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return SuccessResponse(
        message="Account successfully deleted",
        data={"user_id": str(current_user.id)}
    )


@router.post("/verify-email/{user_id}", response_model=SuccessResponse)
async def verify_email(
    user_id: str,
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """Verify user's email address.
    
    Note: In a real application, this would typically be called
    with a verification token sent via email.
    
    Args:
        user_id: User's unique identifier.
        user_service: User service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If user not found.
    """
    try:
        from uuid import UUID
        user_uuid = UUID(user_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID format"
        )
    
    success = await user_service.verify_email(user_uuid)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return SuccessResponse(
        message="Email successfully verified",
        data={"user_id": user_id}
    )


@router.post("/password-reset", response_model=SuccessResponse)
async def request_password_reset(
    reset_data: PasswordReset,
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """Request password reset token.
    
    Args:
        reset_data: Password reset request data.
        user_service: User service instance.
        
    Returns:
        Success message with reset token (in real app, this would be sent via email).
    """
    # Check if user exists
    user = await user_service.get_user_by_email(reset_data.email)
    if not user:
        # Return success even if user doesn't exist for security
        return SuccessResponse(
            message="If an account with that email exists, a password reset link has been sent",
            data={}
        )
    
    reset_token = user_service.generate_reset_token(reset_data.email)
    
    # In a real application, you would send this token via email
    # For demo purposes, we return it in the response
    return SuccessResponse(
        message="Password reset token generated",
        data={"reset_token": reset_token, "note": "In production, this would be sent via email"}
    )


@router.post("/password-reset/confirm", response_model=SuccessResponse)
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """Confirm password reset with token.
    
    Args:
        reset_data: Password reset confirmation data.
        user_service: User service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If token is invalid or expired.
    """
    success = await user_service.reset_password(
        token=reset_data.token,
        new_password=reset_data.new_password
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    return SuccessResponse(
        message="Password successfully reset",
        data={}
    )
