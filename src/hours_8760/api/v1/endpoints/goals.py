"""Goals API endpoints for managing major goals and sub-projects.

This module provides API endpoints for goal management including
CRUD operations for major goals, sub-projects, and goal uncertainties.
"""

from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query

from src.hours_8760.api.v1.dependencies import get_current_user, get_db
from src.hours_8760.models.user import User
from src.hours_8760.schemas.goal import (
    MajorGoalCreate,
    MajorGoalUpdate,
    MajorGoalResponse,
    SubProjectCreate,
    SubProjectUpdate,
    SubProjectResponse,
    GoalUncertaintyCreate,
    GoalUncertaintyUpdate,
    GoalUncertaintyResponse,
    GoalsOverview
)
from src.hours_8760.schemas.base import SuccessResponse
from src.hours_8760.services.goal_service import GoalService
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


async def get_goal_service(db: AsyncSession = Depends(get_db)) -> GoalService:
    """Get goal service dependency."""
    return GoalService(db)


# Major Goals endpoints
@router.get("/", response_model=List[MajorGoalResponse])
async def get_goals(
    planning_session_id: Optional[UUID] = Query(None, description="Filter by planning session"),
    status: Optional[str] = Query(None, description="Filter by status"),
    life_area_id: Optional[UUID] = Query(None, description="Filter by life area"),
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Get current user's major goals.
    
    Args:
        planning_session_id: Optional planning session filter.
        status: Optional status filter.
        life_area_id: Optional life area filter.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        List of user's major goals.
    """
    goals = await goal_service.get_user_goals(
        user_id=current_user.id,
        planning_session_id=planning_session_id,
        status=status,
        life_area_id=life_area_id
    )
    return [MajorGoalResponse.model_validate(goal) for goal in goals]


@router.get("/overview", response_model=GoalsOverview)
async def get_goals_overview(
    planning_session_id: Optional[UUID] = Query(None, description="Filter by planning session"),
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Get goals overview dashboard.
    
    Args:
        planning_session_id: Optional planning session filter.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Goals overview data.
    """
    overview_data = await goal_service.get_goals_overview(
        user_id=current_user.id,
        planning_session_id=planning_session_id
    )
    
    return GoalsOverview(**overview_data)


@router.get("/{goal_id}", response_model=MajorGoalResponse)
async def get_goal(
    goal_id: UUID,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Get a specific major goal by ID.
    
    Args:
        goal_id: Goal's unique identifier.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Major goal data.
        
    Raises:
        HTTPException: If goal not found.
    """
    goal = await goal_service.get_goal_by_id(goal_id, current_user.id)
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Goal not found"
        )
    
    return MajorGoalResponse.model_validate(goal)


@router.post("/", response_model=MajorGoalResponse, status_code=status.HTTP_201_CREATED)
async def create_goal(
    goal_data: MajorGoalCreate,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Create a new major goal.
    
    Args:
        goal_data: Goal creation data.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Created goal data.
    """
    goal = await goal_service.create_goal(current_user.id, goal_data)
    return MajorGoalResponse.model_validate(goal)


@router.put("/{goal_id}", response_model=MajorGoalResponse)
async def update_goal(
    goal_id: UUID,
    goal_data: MajorGoalUpdate,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Update a major goal.
    
    Args:
        goal_id: Goal's unique identifier.
        goal_data: Updated goal data.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Updated goal data.
        
    Raises:
        HTTPException: If goal not found.
    """
    goal = await goal_service.update_goal(goal_id, current_user.id, goal_data)
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Goal not found"
        )
    
    return MajorGoalResponse.model_validate(goal)


@router.delete("/{goal_id}", response_model=SuccessResponse)
async def delete_goal(
    goal_id: UUID,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Delete a major goal.
    
    Args:
        goal_id: Goal's unique identifier.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If goal not found.
    """
    success = await goal_service.delete_goal(goal_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Goal not found"
        )
    
    return SuccessResponse(
        message="Goal successfully deleted",
        data={"goal_id": str(goal_id)}
    )


# Sub-Projects endpoints
@router.get("/{goal_id}/sub-projects", response_model=List[SubProjectResponse])
async def get_goal_sub_projects(
    goal_id: UUID,
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Get sub-projects for a major goal.
    
    Args:
        goal_id: Goal's unique identifier.
        status: Optional status filter.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        List of sub-projects for the goal.
    """
    sub_projects = await goal_service.get_goal_sub_projects(
        goal_id=goal_id,
        user_id=current_user.id,
        status=status
    )
    return [SubProjectResponse.model_validate(project) for project in sub_projects]


@router.post("/sub-projects", response_model=SubProjectResponse, status_code=status.HTTP_201_CREATED)
async def create_sub_project(
    project_data: SubProjectCreate,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Create a new sub-project.
    
    Args:
        project_data: Sub-project creation data.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Created sub-project data.
        
    Raises:
        HTTPException: If goal not found.
    """
    sub_project = await goal_service.create_sub_project(current_user.id, project_data)
    if not sub_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Goal not found"
        )
    
    return SubProjectResponse.model_validate(sub_project)


@router.get("/sub-projects/{project_id}", response_model=SubProjectResponse)
async def get_sub_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Get a specific sub-project by ID.
    
    Args:
        project_id: Sub-project's unique identifier.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Sub-project data.
        
    Raises:
        HTTPException: If sub-project not found.
    """
    sub_project = await goal_service.get_sub_project_by_id(project_id, current_user.id)
    if not sub_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sub-project not found"
        )
    
    return SubProjectResponse.model_validate(sub_project)


@router.put("/sub-projects/{project_id}", response_model=SubProjectResponse)
async def update_sub_project(
    project_id: UUID,
    project_data: SubProjectUpdate,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Update a sub-project.
    
    Args:
        project_id: Sub-project's unique identifier.
        project_data: Updated sub-project data.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Updated sub-project data.
        
    Raises:
        HTTPException: If sub-project not found.
    """
    sub_project = await goal_service.update_sub_project(
        project_id, current_user.id, project_data
    )
    if not sub_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sub-project not found"
        )
    
    return SubProjectResponse.model_validate(sub_project)


@router.delete("/sub-projects/{project_id}", response_model=SuccessResponse)
async def delete_sub_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    goal_service: GoalService = Depends(get_goal_service)
) -> Any:
    """Delete a sub-project.
    
    Args:
        project_id: Sub-project's unique identifier.
        current_user: Current authenticated user.
        goal_service: Goal service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If sub-project not found.
    """
    success = await goal_service.delete_sub_project(project_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sub-project not found"
        )
    
    return SuccessResponse(
        message="Sub-project successfully deleted",
        data={"project_id": str(project_id)}
    )
