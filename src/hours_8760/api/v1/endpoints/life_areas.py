"""Life Areas API endpoints for managing life areas and assessments.

This module provides API endpoints for life area management including
CRUD operations for life areas, assessments, and custom metrics.
"""

from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query

from src.hours_8760.api.v1.dependencies import get_current_user, get_db
from src.hours_8760.models.user import User
from src.hours_8760.schemas.life_area import (
    LifeAreaCreate,
    LifeAreaUpdate,
    LifeAreaResponse,
    LifeAreaAssessmentCreate,
    LifeAreaAssessmentUpdate,
    LifeAreaAssessmentResponse,
    LifeAreaMetricCreate,
    LifeAreaMetricUpdate,
    LifeAreaMetricResponse,
    AssessmentOverview
)
from src.hours_8760.schemas.base import SuccessResponse
from src.hours_8760.services.life_area_service import LifeAreaService
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


async def get_life_area_service(db: AsyncSession = Depends(get_db)) -> LifeAreaService:
    """Get life area service dependency."""
    return LifeAreaService(db)


# Life Areas endpoints
@router.get("/", response_model=List[LifeAreaResponse])
async def get_life_areas(
    active_only: bool = Query(True, description="Return only active life areas"),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Get all life areas.
    
    Args:
        active_only: Whether to return only active life areas.
        life_area_service: Life area service instance.
        
    Returns:
        List of life areas.
    """
    life_areas = await life_area_service.get_life_areas(active_only=active_only)
    return [LifeAreaResponse.model_validate(area) for area in life_areas]


@router.get("/{life_area_id}", response_model=LifeAreaResponse)
async def get_life_area(
    life_area_id: UUID,
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Get a specific life area by ID.
    
    Args:
        life_area_id: Life area's unique identifier.
        life_area_service: Life area service instance.
        
    Returns:
        Life area data.
        
    Raises:
        HTTPException: If life area not found.
    """
    life_area = await life_area_service.get_life_area_by_id(life_area_id)
    if not life_area:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Life area not found"
        )
    
    return LifeAreaResponse.model_validate(life_area)


@router.post("/", response_model=LifeAreaResponse, status_code=status.HTTP_201_CREATED)
async def create_life_area(
    life_area_data: LifeAreaCreate,
    life_area_service: LifeAreaService = Depends(get_life_area_service),
    current_user: User = Depends(get_current_user)  # Admin only in real app
) -> Any:
    """Create a new life area.
    
    Note: In a production app, this would be admin-only.
    
    Args:
        life_area_data: Life area creation data.
        life_area_service: Life area service instance.
        current_user: Current authenticated user.
        
    Returns:
        Created life area data.
    """
    life_area = await life_area_service.create_life_area(life_area_data)
    return LifeAreaResponse.model_validate(life_area)


@router.put("/{life_area_id}", response_model=LifeAreaResponse)
async def update_life_area(
    life_area_id: UUID,
    life_area_data: LifeAreaUpdate,
    life_area_service: LifeAreaService = Depends(get_life_area_service),
    current_user: User = Depends(get_current_user)  # Admin only in real app
) -> Any:
    """Update a life area.
    
    Note: In a production app, this would be admin-only.
    
    Args:
        life_area_id: Life area's unique identifier.
        life_area_data: Updated life area data.
        life_area_service: Life area service instance.
        current_user: Current authenticated user.
        
    Returns:
        Updated life area data.
        
    Raises:
        HTTPException: If life area not found.
    """
    life_area = await life_area_service.update_life_area(life_area_id, life_area_data)
    if not life_area:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Life area not found"
        )
    
    return LifeAreaResponse.model_validate(life_area)


@router.delete("/{life_area_id}", response_model=SuccessResponse)
async def delete_life_area(
    life_area_id: UUID,
    life_area_service: LifeAreaService = Depends(get_life_area_service),
    current_user: User = Depends(get_current_user)  # Admin only in real app
) -> Any:
    """Delete a life area.
    
    Note: In a production app, this would be admin-only.
    
    Args:
        life_area_id: Life area's unique identifier.
        life_area_service: Life area service instance.
        current_user: Current authenticated user.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If life area not found.
    """
    success = await life_area_service.delete_life_area(life_area_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Life area not found"
        )
    
    return SuccessResponse(
        message="Life area successfully deleted",
        data={"life_area_id": str(life_area_id)}
    )


# Life Area Assessments endpoints
@router.get("/assessments/", response_model=List[LifeAreaAssessmentResponse])
async def get_user_assessments(
    year: Optional[int] = Query(None, description="Filter by year"),
    quarter: Optional[int] = Query(None, ge=1, le=4, description="Filter by quarter (1-4)"),
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Get current user's life area assessments.
    
    Args:
        year: Optional year filter.
        quarter: Optional quarter filter.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.
        
    Returns:
        List of user's assessments.
    """
    assessments = await life_area_service.get_user_assessments(
        user_id=current_user.id,
        year=year,
        quarter=quarter
    )
    return [LifeAreaAssessmentResponse.model_validate(assessment) for assessment in assessments]


@router.get("/assessments/{assessment_id}", response_model=LifeAreaAssessmentResponse)
async def get_assessment(
    assessment_id: UUID,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Get a specific assessment by ID.
    
    Args:
        assessment_id: Assessment's unique identifier.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.
        
    Returns:
        Assessment data.
        
    Raises:
        HTTPException: If assessment not found.
    """
    assessment = await life_area_service.get_assessment_by_id(assessment_id, current_user.id)
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assessment not found"
        )
    
    return LifeAreaAssessmentResponse.model_validate(assessment)


@router.post("/assessments/", response_model=LifeAreaAssessmentResponse, status_code=status.HTTP_201_CREATED)
async def create_assessment(
    assessment_data: LifeAreaAssessmentCreate,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Create a new life area assessment.
    
    Args:
        assessment_data: Assessment creation data.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.
        
    Returns:
        Created assessment data.
        
    Raises:
        HTTPException: If assessment already exists for the same criteria.
    """
    try:
        assessment = await life_area_service.create_assessment(current_user.id, assessment_data)
        return LifeAreaAssessmentResponse.model_validate(assessment)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/assessments/{assessment_id}", response_model=LifeAreaAssessmentResponse)
async def update_assessment(
    assessment_id: UUID,
    assessment_data: LifeAreaAssessmentUpdate,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Update a life area assessment.
    
    Args:
        assessment_id: Assessment's unique identifier.
        assessment_data: Updated assessment data.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.
        
    Returns:
        Updated assessment data.
        
    Raises:
        HTTPException: If assessment not found.
    """
    assessment = await life_area_service.update_assessment(
        assessment_id, current_user.id, assessment_data
    )
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assessment not found"
        )
    
    return LifeAreaAssessmentResponse.model_validate(assessment)


@router.delete("/assessments/{assessment_id}", response_model=SuccessResponse)
async def delete_assessment(
    assessment_id: UUID,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Delete a life area assessment.
    
    Args:
        assessment_id: Assessment's unique identifier.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If assessment not found.
    """
    success = await life_area_service.delete_assessment(assessment_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assessment not found"
        )
    
    return SuccessResponse(
        message="Assessment successfully deleted",
        data={"assessment_id": str(assessment_id)}
    )


# Life Area Metrics endpoints
@router.get("/metrics/", response_model=List[LifeAreaMetricResponse])
async def get_user_metrics(
    life_area_id: Optional[UUID] = Query(None, description="Filter by life area"),
    active_only: bool = Query(True, description="Return only active metrics"),
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Get current user's life area metrics.

    Args:
        life_area_id: Optional life area filter.
        active_only: Whether to return only active metrics.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.

    Returns:
        List of user's metrics.
    """
    metrics = await life_area_service.get_user_metrics(
        user_id=current_user.id,
        life_area_id=life_area_id,
        active_only=active_only
    )
    return [LifeAreaMetricResponse.model_validate(metric) for metric in metrics]


@router.get("/metrics/{metric_id}", response_model=LifeAreaMetricResponse)
async def get_metric(
    metric_id: UUID,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Get a specific metric by ID.

    Args:
        metric_id: Metric's unique identifier.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.

    Returns:
        Metric data.

    Raises:
        HTTPException: If metric not found.
    """
    metric = await life_area_service.get_metric_by_id(metric_id, current_user.id)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Metric not found"
        )

    return LifeAreaMetricResponse.model_validate(metric)


@router.post("/metrics/", response_model=LifeAreaMetricResponse, status_code=status.HTTP_201_CREATED)
async def create_metric(
    metric_data: LifeAreaMetricCreate,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Create a new life area metric.

    Args:
        metric_data: Metric creation data.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.

    Returns:
        Created metric data.
    """
    metric = await life_area_service.create_metric(current_user.id, metric_data)
    return LifeAreaMetricResponse.model_validate(metric)


@router.put("/metrics/{metric_id}", response_model=LifeAreaMetricResponse)
async def update_metric(
    metric_id: UUID,
    metric_data: LifeAreaMetricUpdate,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Update a life area metric.

    Args:
        metric_id: Metric's unique identifier.
        metric_data: Updated metric data.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.

    Returns:
        Updated metric data.

    Raises:
        HTTPException: If metric not found.
    """
    metric = await life_area_service.update_metric(metric_id, current_user.id, metric_data)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Metric not found"
        )

    return LifeAreaMetricResponse.model_validate(metric)


@router.delete("/metrics/{metric_id}", response_model=SuccessResponse)
async def delete_metric(
    metric_id: UUID,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Delete a life area metric.

    Args:
        metric_id: Metric's unique identifier.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.

    Returns:
        Success message.

    Raises:
        HTTPException: If metric not found.
    """
    success = await life_area_service.delete_metric(metric_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Metric not found"
        )

    return SuccessResponse(
        message="Metric successfully deleted",
        data={"metric_id": str(metric_id)}
    )


# Assessment Overview endpoint
@router.get("/assessments/overview/{year}", response_model=AssessmentOverview)
async def get_assessment_overview(
    year: int,
    current_user: User = Depends(get_current_user),
    life_area_service: LifeAreaService = Depends(get_life_area_service)
) -> Any:
    """Get assessment overview for a specific year.

    Args:
        year: Year for the overview.
        current_user: Current authenticated user.
        life_area_service: Life area service instance.

    Returns:
        Assessment overview data.
    """
    overview_data = await life_area_service.get_assessment_overview(current_user.id, year)

    # Create life area summaries
    life_area_summaries = []
    for life_area in await life_area_service.get_life_areas():
        # Find the most recent assessment for this life area
        area_assessments = [a for a in overview_data["assessments"] if a.life_area_id == life_area.id]
        current_rating = None
        last_assessment_date = None

        if area_assessments:
            latest_assessment = max(area_assessments, key=lambda x: x.created_at)
            current_rating = latest_assessment.overall_rating
            last_assessment_date = latest_assessment.created_at.isoformat()

        # Get metrics count for this life area
        metrics = await life_area_service.get_user_metrics(
            user_id=current_user.id,
            life_area_id=life_area.id
        )

        life_area_summaries.append({
            "life_area": LifeAreaResponse.model_validate(life_area),
            "current_rating": current_rating,
            "rating_trend": "stable",  # TODO: Calculate trend
            "last_assessment_date": last_assessment_date,
            "metrics_count": len(metrics)
        })

    return AssessmentOverview(
        year=overview_data["year"],
        total_areas=overview_data["total_areas"],
        assessed_areas=overview_data["assessed_areas"],
        average_rating=overview_data["average_rating"],
        completion_percentage=overview_data["completion_percentage"],
        life_areas=life_area_summaries
    )
