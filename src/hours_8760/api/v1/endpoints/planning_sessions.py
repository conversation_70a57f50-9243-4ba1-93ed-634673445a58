"""Planning Sessions API endpoints for managing annual planning sessions.

This module provides API endpoints for planning session management including
CRUD operations for planning sessions, mind maps, and calendar events.
"""

from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query

from src.hours_8760.api.v1.dependencies import get_current_user, get_db
from src.hours_8760.models.user import User
from src.hours_8760.schemas.planning_session import (
    PlanningSessionCreate,
    PlanningSessionUpdate,
    PlanningSessionResponse,
    MindMapCreate,
    MindMapUpdate,
    MindMapResponse,
    CalendarEventCreate,
    CalendarEventUpdate,
    CalendarEventResponse,
    PlanningDashboard
)
from src.hours_8760.schemas.base import SuccessResponse
from src.hours_8760.services.planning_session_service import PlanningSessionService
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


async def get_planning_session_service(db: AsyncSession = Depends(get_db)) -> PlanningSessionService:
    """Get planning session service dependency."""
    return PlanningSessionService(db)


# Planning Sessions endpoints
@router.get("/", response_model=List[PlanningSessionResponse])
async def get_planning_sessions(
    year: Optional[int] = Query(None, description="Filter by year"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get current user's planning sessions.
    
    Args:
        year: Optional year filter.
        status: Optional status filter.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        List of user's planning sessions.
    """
    sessions = await planning_service.get_user_planning_sessions(
        user_id=current_user.id,
        year=year,
        status=status
    )
    return [PlanningSessionResponse.model_validate(session) for session in sessions]


@router.get("/current", response_model=PlanningSessionResponse)
async def get_current_planning_session(
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get current user's current planning session.
    
    Args:
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        Current planning session.
        
    Raises:
        HTTPException: If no current planning session found.
    """
    session = await planning_service.get_current_planning_session(current_user.id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No current planning session found"
        )
    
    return PlanningSessionResponse.model_validate(session)


@router.get("/dashboard", response_model=PlanningDashboard)
async def get_planning_dashboard(
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get planning dashboard overview.
    
    Args:
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        Planning dashboard data.
    """
    dashboard_data = await planning_service.get_planning_dashboard(current_user.id)
    
    return PlanningDashboard(
        current_session=PlanningSessionResponse.model_validate(dashboard_data["current_session"]) if dashboard_data["current_session"] else None,
        recent_sessions=[PlanningSessionResponse.model_validate(s) for s in dashboard_data["recent_sessions"]],
        upcoming_events=[CalendarEventResponse.model_validate(e) for e in dashboard_data["upcoming_events"]],
        progress_summary=dashboard_data["progress_summary"]
    )


@router.get("/{session_id}", response_model=PlanningSessionResponse)
async def get_planning_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get a specific planning session by ID.
    
    Args:
        session_id: Planning session's unique identifier.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        Planning session data.
        
    Raises:
        HTTPException: If planning session not found.
    """
    session = await planning_service.get_planning_session_by_id(session_id, current_user.id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Planning session not found"
        )
    
    return PlanningSessionResponse.model_validate(session)


@router.post("/", response_model=PlanningSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_planning_session(
    session_data: PlanningSessionCreate,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Create a new planning session.
    
    Args:
        session_data: Planning session creation data.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        Created planning session data.
        
    Raises:
        HTTPException: If planning session already exists for the year.
    """
    try:
        session = await planning_service.create_planning_session(current_user.id, session_data)
        return PlanningSessionResponse.model_validate(session)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{session_id}", response_model=PlanningSessionResponse)
async def update_planning_session(
    session_id: UUID,
    session_data: PlanningSessionUpdate,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Update a planning session.
    
    Args:
        session_id: Planning session's unique identifier.
        session_data: Updated planning session data.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        Updated planning session data.
        
    Raises:
        HTTPException: If planning session not found.
    """
    session = await planning_service.update_planning_session(
        session_id, current_user.id, session_data
    )
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Planning session not found"
        )
    
    return PlanningSessionResponse.model_validate(session)


@router.delete("/{session_id}", response_model=SuccessResponse)
async def delete_planning_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Delete a planning session.
    
    Args:
        session_id: Planning session's unique identifier.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If planning session not found.
    """
    success = await planning_service.delete_planning_session(session_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Planning session not found"
        )
    
    return SuccessResponse(
        message="Planning session successfully deleted",
        data={"session_id": str(session_id)}
    )


# Mind Maps endpoints
@router.get("/{session_id}/mind-maps", response_model=List[MindMapResponse])
async def get_session_mind_maps(
    session_id: UUID,
    mind_map_type: Optional[str] = Query(None, description="Filter by mind map type"),
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get mind maps for a planning session.
    
    Args:
        session_id: Planning session's unique identifier.
        mind_map_type: Optional type filter.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        List of mind maps for the session.
    """
    mind_maps = await planning_service.get_session_mind_maps(
        session_id=session_id,
        user_id=current_user.id,
        mind_map_type=mind_map_type
    )
    return [MindMapResponse.model_validate(mind_map) for mind_map in mind_maps]


@router.post("/mind-maps", response_model=MindMapResponse, status_code=status.HTTP_201_CREATED)
async def create_mind_map(
    mind_map_data: MindMapCreate,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Create a new mind map.
    
    Args:
        mind_map_data: Mind map creation data.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.
        
    Returns:
        Created mind map data.
    """
    mind_map = await planning_service.create_mind_map(current_user.id, mind_map_data)
    return MindMapResponse.model_validate(mind_map)


@router.get("/mind-maps/{mind_map_id}", response_model=MindMapResponse)
async def get_mind_map(
    mind_map_id: UUID,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get a specific mind map by ID.

    Args:
        mind_map_id: Mind map's unique identifier.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.

    Returns:
        Mind map data.

    Raises:
        HTTPException: If mind map not found.
    """
    mind_map = await planning_service.get_mind_map_by_id(mind_map_id, current_user.id)
    if not mind_map:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mind map not found"
        )

    return MindMapResponse.model_validate(mind_map)


@router.put("/mind-maps/{mind_map_id}", response_model=MindMapResponse)
async def update_mind_map(
    mind_map_id: UUID,
    mind_map_data: MindMapUpdate,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Update a mind map.

    Args:
        mind_map_id: Mind map's unique identifier.
        mind_map_data: Updated mind map data.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.

    Returns:
        Updated mind map data.

    Raises:
        HTTPException: If mind map not found.
    """
    mind_map = await planning_service.update_mind_map(
        mind_map_id, current_user.id, mind_map_data
    )
    if not mind_map:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mind map not found"
        )

    return MindMapResponse.model_validate(mind_map)


@router.delete("/mind-maps/{mind_map_id}", response_model=SuccessResponse)
async def delete_mind_map(
    mind_map_id: UUID,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Delete a mind map.

    Args:
        mind_map_id: Mind map's unique identifier.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.

    Returns:
        Success message.

    Raises:
        HTTPException: If mind map not found.
    """
    success = await planning_service.delete_mind_map(mind_map_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mind map not found"
        )

    return SuccessResponse(
        message="Mind map successfully deleted",
        data={"mind_map_id": str(mind_map_id)}
    )


# Calendar Events endpoints
@router.get("/{session_id}/calendar-events", response_model=List[CalendarEventResponse])
async def get_session_calendar_events(
    session_id: UUID,
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    upcoming_only: bool = Query(False, description="Return only upcoming events"),
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get calendar events for a planning session.

    Args:
        session_id: Planning session's unique identifier.
        event_type: Optional type filter.
        upcoming_only: Whether to return only upcoming events.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.

    Returns:
        List of calendar events for the session.
    """
    events = await planning_service.get_session_calendar_events(
        session_id=session_id,
        user_id=current_user.id,
        event_type=event_type,
        upcoming_only=upcoming_only
    )
    return [CalendarEventResponse.model_validate(event) for event in events]


@router.post("/calendar-events", response_model=CalendarEventResponse, status_code=status.HTTP_201_CREATED)
async def create_calendar_event(
    event_data: CalendarEventCreate,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Create a new calendar event.

    Args:
        event_data: Calendar event creation data.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.

    Returns:
        Created calendar event data.
    """
    event = await planning_service.create_calendar_event(current_user.id, event_data)
    return CalendarEventResponse.model_validate(event)


@router.get("/calendar-events/{event_id}", response_model=CalendarEventResponse)
async def get_calendar_event(
    event_id: UUID,
    current_user: User = Depends(get_current_user),
    planning_service: PlanningSessionService = Depends(get_planning_session_service)
) -> Any:
    """Get a specific calendar event by ID.

    Args:
        event_id: Calendar event's unique identifier.
        current_user: Current authenticated user.
        planning_service: Planning session service instance.

    Returns:
        Calendar event data.

    Raises:
        HTTPException: If calendar event not found.
    """
    event = await planning_service.get_calendar_event_by_id(event_id, current_user.id)
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Calendar event not found"
        )

    return CalendarEventResponse.model_validate(event)
