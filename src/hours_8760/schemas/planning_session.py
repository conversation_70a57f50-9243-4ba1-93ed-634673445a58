"""Planning Session schemas for API requests and responses.

This module contains Pydantic schemas for planning sessions,
mind maps, and calendar events.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import Field, field_validator

from src.hours_8760.schemas.base import BaseSchema, BaseResourceSchema


class PlanningSessionBase(BaseSchema):
    """Base planning session schema with common fields."""
    
    year: int = Field(..., ge=2000, le=3000, description="Year this planning session is for")
    status: str = Field(default='in_progress', description="Status of the planning session")
    yearly_theme: Optional[str] = Field(None, max_length=200, description="Overall theme or focus for the year")
    life_philosophy: Optional[str] = Field(None, description="User's life philosophy and core values")
    ideal_future_vision: Optional[str] = Field(None, description="Vision of ideal future state")
    major_focus_areas: Optional[List[UUID]] = Field(None, description="List of life area IDs that are major focus areas")
    
    @field_validator("status")
    @classmethod
    def validate_status(cls, v: str) -> str:
        """Validate planning session status."""
        allowed_statuses = ["in_progress", "completed", "archived"]
        if v not in allowed_statuses:
            raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v


class PlanningSessionCreate(PlanningSessionBase):
    """Schema for creating a new planning session."""
    pass


class PlanningSessionUpdate(BaseSchema):
    """Schema for updating a planning session."""
    
    status: Optional[str] = Field(None, description="Status of the planning session")
    yearly_theme: Optional[str] = Field(None, max_length=200, description="Overall theme or focus for the year")
    life_philosophy: Optional[str] = Field(None, description="User's life philosophy and core values")
    ideal_future_vision: Optional[str] = Field(None, description="Vision of ideal future state")
    major_focus_areas: Optional[List[UUID]] = Field(None, description="List of life area IDs that are major focus areas")
    
    @field_validator("status")
    @classmethod
    def validate_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate planning session status."""
        if v is not None:
            allowed_statuses = ["in_progress", "completed", "archived"]
            if v not in allowed_statuses:
                raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v


class PlanningSessionResponse(PlanningSessionBase, BaseResourceSchema):
    """Schema for planning session responses."""
    
    user_id: UUID = Field(..., description="ID of the user who owns this planning session")
    completed_at: Optional[datetime] = Field(None, description="Timestamp when the planning session was completed")
    goals_count: Optional[int] = Field(None, description="Number of major goals in this session")
    progress_percentage: Optional[float] = Field(None, description="Overall progress percentage")


# Mind Map Schemas
class MindMapBase(BaseSchema):
    """Base mind map schema with common fields."""
    
    title: str = Field(..., max_length=200, description="Title of the mind map")
    type: str = Field(..., description="Type of mind map")
    data: Dict[str, Any] = Field(..., description="Mind map structure and content as JSON")
    
    @field_validator("type")
    @classmethod
    def validate_type(cls, v: str) -> str:
        """Validate mind map type."""
        allowed_types = ["life_status", "future_vision", "goals", "custom"]
        if v not in allowed_types:
            raise ValueError(f"Mind map type must be one of: {', '.join(allowed_types)}")
        return v


class MindMapCreate(MindMapBase):
    """Schema for creating a new mind map."""
    
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this mind map belongs to")


class MindMapUpdate(BaseSchema):
    """Schema for updating a mind map."""
    
    title: Optional[str] = Field(None, max_length=200, description="Title of the mind map")
    type: Optional[str] = Field(None, description="Type of mind map")
    data: Optional[Dict[str, Any]] = Field(None, description="Mind map structure and content as JSON")
    
    @field_validator("type")
    @classmethod
    def validate_type(cls, v: Optional[str]) -> Optional[str]:
        """Validate mind map type."""
        if v is not None:
            allowed_types = ["life_status", "future_vision", "goals", "custom"]
            if v not in allowed_types:
                raise ValueError(f"Mind map type must be one of: {', '.join(allowed_types)}")
        return v


class MindMapResponse(MindMapBase, BaseResourceSchema):
    """Schema for mind map responses."""
    
    user_id: UUID = Field(..., description="ID of the user who created this mind map")
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this mind map belongs to")


# Calendar Event Schemas
class CalendarEventBase(BaseSchema):
    """Base calendar event schema with common fields."""
    
    title: str = Field(..., max_length=200, description="Title of the calendar event")
    description: Optional[str] = Field(None, description="Detailed description of the event")
    event_date: datetime = Field(..., description="Date and time of the event")
    event_type: Optional[str] = Field(None, description="Type of event")
    is_completed: bool = Field(default=False, description="Whether this event has been completed")
    
    @field_validator("event_type")
    @classmethod
    def validate_event_type(cls, v: Optional[str]) -> Optional[str]:
        """Validate event type."""
        if v is not None:
            allowed_types = ["milestone", "deadline", "review", "focus_theme"]
            if v not in allowed_types:
                raise ValueError(f"Event type must be one of: {', '.join(allowed_types)}")
        return v


class CalendarEventCreate(CalendarEventBase):
    """Schema for creating a new calendar event."""
    
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this event belongs to")
    major_goal_id: Optional[UUID] = Field(None, description="ID of the major goal this event relates to")


class CalendarEventUpdate(BaseSchema):
    """Schema for updating a calendar event."""
    
    title: Optional[str] = Field(None, max_length=200, description="Title of the calendar event")
    description: Optional[str] = Field(None, description="Detailed description of the event")
    event_date: Optional[datetime] = Field(None, description="Date and time of the event")
    event_type: Optional[str] = Field(None, description="Type of event")
    is_completed: Optional[bool] = Field(None, description="Whether this event has been completed")
    
    @field_validator("event_type")
    @classmethod
    def validate_event_type(cls, v: Optional[str]) -> Optional[str]:
        """Validate event type."""
        if v is not None:
            allowed_types = ["milestone", "deadline", "review", "focus_theme"]
            if v not in allowed_types:
                raise ValueError(f"Event type must be one of: {', '.join(allowed_types)}")
        return v


class CalendarEventResponse(CalendarEventBase, BaseResourceSchema):
    """Schema for calendar event responses."""
    
    user_id: UUID = Field(..., description="ID of the user who created this event")
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this event belongs to")
    major_goal_id: Optional[UUID] = Field(None, description="ID of the major goal this event relates to")


# Planning Session Summary Schemas
class PlanningSessionSummary(BaseSchema):
    """Schema for planning session summary."""
    
    session: PlanningSessionResponse = Field(..., description="Planning session details")
    goals_count: int = Field(default=0, description="Number of major goals")
    completed_goals: int = Field(default=0, description="Number of completed goals")
    active_goals: int = Field(default=0, description="Number of active goals")
    upcoming_events: int = Field(default=0, description="Number of upcoming calendar events")
    mind_maps_count: int = Field(default=0, description="Number of mind maps")


class PlanningDashboard(BaseSchema):
    """Schema for planning dashboard overview."""
    
    current_session: Optional[PlanningSessionResponse] = Field(None, description="Current active planning session")
    recent_sessions: List[PlanningSessionResponse] = Field(default=[], description="Recent planning sessions")
    upcoming_events: List[CalendarEventResponse] = Field(default=[], description="Upcoming calendar events")
    progress_summary: Dict[str, Any] = Field(default={}, description="Overall progress summary")
