"""Schemas package for the 8760 Hours Life Planning Platform.

This package contains all Pydantic schemas for request/response validation
across all API endpoints including user management, life areas, planning
sessions, goals, and progress tracking.
"""

from .base import (
    BaseSchema,
    BaseResourceSchema,
    TimestampSchema,
    PaginationParams,
    PaginatedResponse,
    ErrorResponse,
    SuccessResponse
)
from .user import (
    UserCreate,
    UserUpdate,
    UserResponse,
    UserLogin,
    UserRegister,
    Token,
    TokenData
)
from .life_area import (
    LifeAreaCreate,
    LifeAreaUpdate,
    LifeAreaResponse,
    LifeAreaAssessmentCreate,
    LifeAreaAssessmentUpdate,
    LifeAreaAssessmentResponse,
    LifeAreaMetricCreate,
    LifeAreaMetricUpdate,
    LifeAreaMetricResponse,
    AssessmentOverview
)
from .planning_session import (
    PlanningSessionCreate,
    PlanningSessionUpdate,
    PlanningSessionResponse,
    MindMapCreate,
    MindMapUpdate,
    MindMapResponse,
    CalendarEventCreate,
    CalendarEventUpdate,
    CalendarEventResponse,
    PlanningDashboard
)
from .goal import (
    MajorGoalCreate,
    MajorGoalUpdate,
    MajorGoalResponse,
    SubProjectCreate,
    SubProjectUpdate,
    SubProjectResponse,
    GoalUncertaintyCreate,
    GoalUncertaintyUpdate,
    GoalUncertaintyResponse,
    GoalsOverview
)
from .review import (
    ReviewSessionCreate,
    ReviewSessionUpdate,
    ReviewSessionResponse,
    ProgressEntryCreate,
    ProgressEntryUpdate,
    ProgressEntryResponse,
    QuestionTemplateCreate,
    QuestionTemplateUpdate,
    QuestionTemplateResponse,
    QuestionResponseCreate,
    QuestionResponseUpdate,
    QuestionResponseResponse,
    ProgressAnalytics
)

__all__ = [
    # Base schemas
    "BaseSchema",
    "BaseResourceSchema",
    "TimestampSchema",
    "PaginationParams",
    "PaginatedResponse",
    "ErrorResponse",
    "SuccessResponse",

    # User schemas
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    "UserRegister",
    "Token",
    "TokenData",

    # Life area schemas
    "LifeAreaCreate",
    "LifeAreaUpdate",
    "LifeAreaResponse",
    "LifeAreaAssessmentCreate",
    "LifeAreaAssessmentUpdate",
    "LifeAreaAssessmentResponse",
    "LifeAreaMetricCreate",
    "LifeAreaMetricUpdate",
    "LifeAreaMetricResponse",
    "AssessmentOverview",

    # Planning session schemas
    "PlanningSessionCreate",
    "PlanningSessionUpdate",
    "PlanningSessionResponse",
    "MindMapCreate",
    "MindMapUpdate",
    "MindMapResponse",
    "CalendarEventCreate",
    "CalendarEventUpdate",
    "CalendarEventResponse",
    "PlanningDashboard",

    # Goal schemas
    "MajorGoalCreate",
    "MajorGoalUpdate",
    "MajorGoalResponse",
    "SubProjectCreate",
    "SubProjectUpdate",
    "SubProjectResponse",
    "GoalUncertaintyCreate",
    "GoalUncertaintyUpdate",
    "GoalUncertaintyResponse",
    "GoalsOverview",

    # Review and progress schemas
    "ReviewSessionCreate",
    "ReviewSessionUpdate",
    "ReviewSessionResponse",
    "ProgressEntryCreate",
    "ProgressEntryUpdate",
    "ProgressEntryResponse",
    "QuestionTemplateCreate",
    "QuestionTemplateUpdate",
    "QuestionTemplateResponse",
    "QuestionResponseCreate",
    "QuestionResponseUpdate",
    "QuestionResponseResponse",
    "ProgressAnalytics",
]
