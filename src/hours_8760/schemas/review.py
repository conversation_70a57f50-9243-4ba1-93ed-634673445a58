"""Review and Progress schemas for API requests and responses.

This module contains Pydantic schemas for review sessions, progress tracking,
and reflection activities.
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import Field, field_validator

from src.hours_8760.schemas.base import BaseSchema, BaseResourceSchema


class ReviewSessionBase(BaseSchema):
    """Base review session schema with common fields."""
    
    type: str = Field(..., description="Type of review (monthly, quarterly, annual)")
    year: int = Field(..., ge=2000, le=3000, description="Year of the review")
    month: Optional[int] = Field(None, ge=1, le=12, description="Month of the review (1-12 for monthly reviews)")
    quarter: Optional[int] = Field(None, ge=1, le=4, description="Quarter of the review (1-4 for quarterly reviews)")
    goal_progress_notes: Optional[str] = Field(None, description="Notes on goal progress during this period")
    what_went_well: Optional[str] = Field(None, description="Reflection on what went well")
    what_went_poorly: Optional[str] = Field(None, description="Reflection on what went poorly or could be improved")
    adjustments_needed: Optional[str] = Field(None, description="Adjustments needed for future periods")
    priority_changes: Optional[Dict[str, Any]] = Field(None, description="Changes to goal priorities as JSON")
    overall_satisfaction: Optional[int] = Field(None, ge=1, le=7, description="Overall satisfaction rating (1-7 scale)")
    
    @field_validator("type")
    @classmethod
    def validate_type(cls, v: str) -> str:
        """Validate review type."""
        allowed_types = ["monthly", "quarterly", "annual"]
        if v not in allowed_types:
            raise ValueError(f"Review type must be one of: {', '.join(allowed_types)}")
        return v
    
    @field_validator("month")
    @classmethod
    def validate_month_for_type(cls, v: Optional[int], values: dict) -> Optional[int]:
        """Validate month is provided for monthly reviews."""
        if values.get("type") == "monthly" and v is None:
            raise ValueError("Month is required for monthly reviews")
        if values.get("type") != "monthly" and v is not None:
            raise ValueError("Month should only be provided for monthly reviews")
        return v
    
    @field_validator("quarter")
    @classmethod
    def validate_quarter_for_type(cls, v: Optional[int], values: dict) -> Optional[int]:
        """Validate quarter is provided for quarterly reviews."""
        if values.get("type") == "quarterly" and v is None:
            raise ValueError("Quarter is required for quarterly reviews")
        if values.get("type") != "quarterly" and v is not None:
            raise ValueError("Quarter should only be provided for quarterly reviews")
        return v


class ReviewSessionCreate(ReviewSessionBase):
    """Schema for creating a new review session."""
    
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this review relates to")


class ReviewSessionUpdate(BaseSchema):
    """Schema for updating a review session."""
    
    goal_progress_notes: Optional[str] = Field(None, description="Notes on goal progress during this period")
    what_went_well: Optional[str] = Field(None, description="Reflection on what went well")
    what_went_poorly: Optional[str] = Field(None, description="Reflection on what went poorly or could be improved")
    adjustments_needed: Optional[str] = Field(None, description="Adjustments needed for future periods")
    priority_changes: Optional[Dict[str, Any]] = Field(None, description="Changes to goal priorities as JSON")
    overall_satisfaction: Optional[int] = Field(None, ge=1, le=7, description="Overall satisfaction rating (1-7 scale)")


class ReviewSessionResponse(ReviewSessionBase, BaseResourceSchema):
    """Schema for review session responses."""
    
    user_id: UUID = Field(..., description="ID of the user who owns this review session")
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this review relates to")
    completed_at: Optional[datetime] = Field(None, description="Timestamp when the review was completed")


# Progress Entry Schemas
class ProgressEntryBase(BaseSchema):
    """Base progress entry schema with common fields."""
    
    entry_date: date = Field(..., description="Date of the progress entry")
    progress_value: Optional[str] = Field(None, max_length=100, description="Quantitative progress value (flexible format)")
    notes: Optional[str] = Field(None, description="Qualitative notes about progress")
    mood: Optional[int] = Field(None, ge=1, le=7, description="Mood/energy level during this progress (1-7 scale)")


class ProgressEntryCreate(ProgressEntryBase):
    """Schema for creating a new progress entry."""
    
    major_goal_id: Optional[UUID] = Field(None, description="ID of the major goal this progress relates to")
    sub_project_id: Optional[UUID] = Field(None, description="ID of the sub-project this progress relates to")
    
    @field_validator("major_goal_id")
    @classmethod
    def validate_goal_or_project(cls, v: Optional[UUID], values: dict) -> Optional[UUID]:
        """Validate that either major_goal_id or sub_project_id is provided."""
        if v is None and values.get("sub_project_id") is None:
            raise ValueError("Either major_goal_id or sub_project_id must be provided")
        return v


class ProgressEntryUpdate(BaseSchema):
    """Schema for updating a progress entry."""
    
    entry_date: Optional[date] = Field(None, description="Date of the progress entry")
    progress_value: Optional[str] = Field(None, max_length=100, description="Quantitative progress value (flexible format)")
    notes: Optional[str] = Field(None, description="Qualitative notes about progress")
    mood: Optional[int] = Field(None, ge=1, le=7, description="Mood/energy level during this progress (1-7 scale)")


class ProgressEntryResponse(ProgressEntryBase, BaseResourceSchema):
    """Schema for progress entry responses."""
    
    user_id: UUID = Field(..., description="ID of the user who made this progress entry")
    major_goal_id: Optional[UUID] = Field(None, description="ID of the major goal this progress relates to")
    sub_project_id: Optional[UUID] = Field(None, description="ID of the sub-project this progress relates to")


# Question Template Schemas
class QuestionTemplateBase(BaseSchema):
    """Base question template schema with common fields."""
    
    category: str = Field(..., max_length=100, description="Category of question (hamming, reflection, visioning)")
    life_area_id: Optional[UUID] = Field(None, description="Life area this question relates to (if applicable)")
    question_text: str = Field(..., description="The actual question text")
    question_type: Optional[str] = Field(None, description="Type of question (text, scale, multiple_choice)")
    sort_order: Optional[int] = Field(None, description="Display order for the question")
    is_active: bool = Field(default=True, description="Whether this question template is currently active")
    
    @field_validator("category")
    @classmethod
    def validate_category(cls, v: str) -> str:
        """Validate question category."""
        allowed_categories = ["hamming", "reflection", "visioning", "assessment", "planning"]
        if v not in allowed_categories:
            raise ValueError(f"Category must be one of: {', '.join(allowed_categories)}")
        return v
    
    @field_validator("question_type")
    @classmethod
    def validate_question_type(cls, v: Optional[str]) -> Optional[str]:
        """Validate question type."""
        if v is not None:
            allowed_types = ["text", "scale", "multiple_choice", "boolean"]
            if v not in allowed_types:
                raise ValueError(f"Question type must be one of: {', '.join(allowed_types)}")
        return v


class QuestionTemplateCreate(QuestionTemplateBase):
    """Schema for creating a new question template."""
    pass


class QuestionTemplateUpdate(BaseSchema):
    """Schema for updating a question template."""
    
    category: Optional[str] = Field(None, max_length=100, description="Category of question")
    life_area_id: Optional[UUID] = Field(None, description="Life area this question relates to")
    question_text: Optional[str] = Field(None, description="The actual question text")
    question_type: Optional[str] = Field(None, description="Type of question")
    sort_order: Optional[int] = Field(None, description="Display order for the question")
    is_active: Optional[bool] = Field(None, description="Whether this question template is currently active")


class QuestionTemplateResponse(QuestionTemplateBase, BaseResourceSchema):
    """Schema for question template responses."""
    pass


# Question Response Schemas
class QuestionResponseBase(BaseSchema):
    """Base question response schema with common fields."""
    
    question_template_id: UUID = Field(..., description="ID of the question template this response answers")
    response_text: Optional[str] = Field(None, description="Text response to the question")
    response_number: Optional[int] = Field(None, description="Numeric response to the question (for scale questions)")


class QuestionResponseCreate(QuestionResponseBase):
    """Schema for creating a new question response."""
    
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this response belongs to")


class QuestionResponseUpdate(BaseSchema):
    """Schema for updating a question response."""
    
    response_text: Optional[str] = Field(None, description="Text response to the question")
    response_number: Optional[int] = Field(None, description="Numeric response to the question")


class QuestionResponseResponse(QuestionResponseBase, BaseResourceSchema):
    """Schema for question response responses."""
    
    user_id: UUID = Field(..., description="ID of the user who provided this response")
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this response belongs to")
    question_template: Optional[QuestionTemplateResponse] = Field(None, description="Question template details")


# Review Summary Schemas
class ReviewSummary(BaseSchema):
    """Schema for review summary information."""
    
    review: ReviewSessionResponse = Field(..., description="Review session details")
    progress_entries_count: int = Field(default=0, description="Number of progress entries during this period")
    goals_progress: Dict[str, Any] = Field(default={}, description="Progress summary for goals")
    satisfaction_trend: Optional[str] = Field(None, description="Satisfaction trend compared to previous reviews")


class ProgressAnalytics(BaseSchema):
    """Schema for progress analytics and insights."""
    
    period_start: date = Field(..., description="Start date of the analysis period")
    period_end: date = Field(..., description="End date of the analysis period")
    total_entries: int = Field(default=0, description="Total number of progress entries")
    average_mood: Optional[float] = Field(None, description="Average mood rating during the period")
    most_active_goals: List[Dict[str, Any]] = Field(default=[], description="Goals with most progress activity")
    progress_trends: Dict[str, Any] = Field(default={}, description="Progress trends and patterns")
    insights: List[str] = Field(default=[], description="Generated insights and recommendations")
