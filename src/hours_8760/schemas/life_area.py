"""Life Area schemas for API requests and responses.

This module contains Pydantic schemas for life area management,
assessments, and custom metrics.
"""

from typing import Optional, List
from uuid import UUID

from pydantic import Field, field_validator

from src.hours_8760.schemas.base import BaseSchema, BaseResourceSchema


class LifeAreaBase(BaseSchema):
    """Base life area schema with common fields."""
    
    name: str = Field(..., max_length=100, description="Name of the life area")
    description: Optional[str] = Field(None, description="Detailed description of the life area")
    sort_order: Optional[int] = Field(None, description="Display order for the life area")
    is_active: bool = Field(default=True, description="Whether this life area is currently active")


class LifeAreaCreate(LifeAreaBase):
    """Schema for creating a new life area."""
    pass


class LifeAreaUpdate(BaseSchema):
    """Schema for updating a life area."""
    
    name: Optional[str] = Field(None, max_length=100, description="Name of the life area")
    description: Optional[str] = Field(None, description="Detailed description of the life area")
    sort_order: Optional[int] = Field(None, description="Display order for the life area")
    is_active: Optional[bool] = Field(None, description="Whether this life area is currently active")


class LifeAreaResponse(LifeAreaBase, BaseResourceSchema):
    """Schema for life area responses."""
    pass


# Life Area Assessment Schemas
class LifeAreaAssessmentBase(BaseSchema):
    """Base life area assessment schema."""
    
    life_area_id: UUID = Field(..., description="ID of the life area being assessed")
    year: int = Field(..., ge=2000, le=3000, description="Year of the assessment")
    quarter: Optional[int] = Field(None, ge=1, le=4, description="Quarter of the assessment (1-4, NULL for annual)")
    overall_rating: Optional[int] = Field(None, ge=1, le=7, description="Overall rating on 1-7 scale")
    strengths: Optional[List[str]] = Field(None, description="List of strengths in this life area")
    weaknesses: Optional[List[str]] = Field(None, description="List of weaknesses in this life area")
    key_accomplishments: Optional[List[str]] = Field(None, description="Key accomplishments in this life area")
    areas_for_improvement: Optional[List[str]] = Field(None, description="Areas identified for improvement")
    notes: Optional[str] = Field(None, description="Additional notes and reflections")


class LifeAreaAssessmentCreate(LifeAreaAssessmentBase):
    """Schema for creating a new life area assessment."""
    pass


class LifeAreaAssessmentUpdate(BaseSchema):
    """Schema for updating a life area assessment."""
    
    overall_rating: Optional[int] = Field(None, ge=1, le=7, description="Overall rating on 1-7 scale")
    strengths: Optional[List[str]] = Field(None, description="List of strengths in this life area")
    weaknesses: Optional[List[str]] = Field(None, description="List of weaknesses in this life area")
    key_accomplishments: Optional[List[str]] = Field(None, description="Key accomplishments in this life area")
    areas_for_improvement: Optional[List[str]] = Field(None, description="Areas identified for improvement")
    notes: Optional[str] = Field(None, description="Additional notes and reflections")


class LifeAreaAssessmentResponse(LifeAreaAssessmentBase, BaseResourceSchema):
    """Schema for life area assessment responses."""
    
    user_id: UUID = Field(..., description="ID of the user who made this assessment")
    life_area: Optional[LifeAreaResponse] = Field(None, description="Life area details")


# Life Area Metric Schemas
class LifeAreaMetricBase(BaseSchema):
    """Base life area metric schema."""
    
    life_area_id: UUID = Field(..., description="ID of the life area this metric belongs to")
    metric_name: str = Field(..., max_length=100, description="Name of the custom metric")
    metric_type: str = Field(..., description="Type of metric (number, percentage, boolean, scale)")
    target_value: Optional[str] = Field(None, max_length=100, description="Target value for this metric")
    current_value: Optional[str] = Field(None, max_length=100, description="Current value of this metric")
    unit: Optional[str] = Field(None, max_length=20, description="Unit of measurement for this metric")
    is_active: bool = Field(default=True, description="Whether this metric is currently being tracked")
    
    @field_validator("metric_type")
    @classmethod
    def validate_metric_type(cls, v: str) -> str:
        """Validate metric type."""
        allowed_types = ["number", "percentage", "boolean", "scale"]
        if v not in allowed_types:
            raise ValueError(f"Metric type must be one of: {', '.join(allowed_types)}")
        return v


class LifeAreaMetricCreate(LifeAreaMetricBase):
    """Schema for creating a new life area metric."""
    pass


class LifeAreaMetricUpdate(BaseSchema):
    """Schema for updating a life area metric."""
    
    metric_name: Optional[str] = Field(None, max_length=100, description="Name of the custom metric")
    metric_type: Optional[str] = Field(None, description="Type of metric (number, percentage, boolean, scale)")
    target_value: Optional[str] = Field(None, max_length=100, description="Target value for this metric")
    current_value: Optional[str] = Field(None, max_length=100, description="Current value of this metric")
    unit: Optional[str] = Field(None, max_length=20, description="Unit of measurement for this metric")
    is_active: Optional[bool] = Field(None, description="Whether this metric is currently being tracked")
    
    @field_validator("metric_type")
    @classmethod
    def validate_metric_type(cls, v: Optional[str]) -> Optional[str]:
        """Validate metric type."""
        if v is not None:
            allowed_types = ["number", "percentage", "boolean", "scale"]
            if v not in allowed_types:
                raise ValueError(f"Metric type must be one of: {', '.join(allowed_types)}")
        return v


class LifeAreaMetricResponse(LifeAreaMetricBase, BaseResourceSchema):
    """Schema for life area metric responses."""
    
    user_id: UUID = Field(..., description="ID of the user who created this metric")
    life_area: Optional[LifeAreaResponse] = Field(None, description="Life area details")


# Assessment Summary Schemas
class LifeAreaSummary(BaseSchema):
    """Schema for life area assessment summary."""
    
    life_area: LifeAreaResponse = Field(..., description="Life area details")
    current_rating: Optional[int] = Field(None, description="Most recent overall rating")
    rating_trend: Optional[str] = Field(None, description="Rating trend (improving, declining, stable)")
    last_assessment_date: Optional[str] = Field(None, description="Date of last assessment")
    metrics_count: int = Field(default=0, description="Number of custom metrics for this life area")


class AssessmentOverview(BaseSchema):
    """Schema for overall assessment overview."""
    
    year: int = Field(..., description="Assessment year")
    total_areas: int = Field(..., description="Total number of life areas")
    assessed_areas: int = Field(..., description="Number of assessed life areas")
    average_rating: Optional[float] = Field(None, description="Average rating across all assessed areas")
    completion_percentage: float = Field(..., description="Percentage of areas assessed")
    life_areas: List[LifeAreaSummary] = Field(..., description="Summary for each life area")
