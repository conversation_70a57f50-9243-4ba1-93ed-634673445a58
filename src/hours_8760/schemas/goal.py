"""Goal schemas for API requests and responses.

This module contains Pydantic schemas for major goals, sub-projects,
and goal uncertainty analysis.
"""

from datetime import date
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import Field, field_validator

from src.hours_8760.schemas.base import BaseSchema, BaseResourceSchema


class MajorGoalBase(BaseSchema):
    """Base major goal schema with common fields."""
    
    title: str = Field(..., max_length=200, description="Title of the major goal")
    description: Optional[str] = Field(None, description="Detailed description of the goal")
    life_area_id: Optional[UUID] = Field(None, description="Primary life area this goal relates to")
    priority_rank: Optional[int] = Field(None, ge=1, le=5, description="Priority ranking (1-5 for major goals)")
    success_criteria: Optional[List[str]] = Field(None, description="List of success criteria for this goal")
    metrics: Optional[Dict[str, Any]] = Field(None, description="Flexible metrics storage as JSON")
    target_completion_date: Optional[date] = Field(None, description="Target date for goal completion")
    is_meta_skill: bool = Field(default=False, description="Whether this goal is focused on developing meta-skills")
    status: str = Field(default='active', description="Status of the goal")
    completion_percentage: int = Field(default=0, ge=0, le=100, description="Completion percentage (0-100)")
    
    @field_validator("status")
    @classmethod
    def validate_status(cls, v: str) -> str:
        """Validate goal status."""
        allowed_statuses = ["active", "completed", "paused", "dropped"]
        if v not in allowed_statuses:
            raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v


class MajorGoalCreate(MajorGoalBase):
    """Schema for creating a new major goal."""
    
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this goal belongs to")


class MajorGoalUpdate(BaseSchema):
    """Schema for updating a major goal."""
    
    title: Optional[str] = Field(None, max_length=200, description="Title of the major goal")
    description: Optional[str] = Field(None, description="Detailed description of the goal")
    life_area_id: Optional[UUID] = Field(None, description="Primary life area this goal relates to")
    priority_rank: Optional[int] = Field(None, ge=1, le=5, description="Priority ranking (1-5 for major goals)")
    success_criteria: Optional[List[str]] = Field(None, description="List of success criteria for this goal")
    metrics: Optional[Dict[str, Any]] = Field(None, description="Flexible metrics storage as JSON")
    target_completion_date: Optional[date] = Field(None, description="Target date for goal completion")
    is_meta_skill: Optional[bool] = Field(None, description="Whether this goal is focused on developing meta-skills")
    status: Optional[str] = Field(None, description="Status of the goal")
    completion_percentage: Optional[int] = Field(None, ge=0, le=100, description="Completion percentage (0-100)")
    
    @field_validator("status")
    @classmethod
    def validate_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate goal status."""
        if v is not None:
            allowed_statuses = ["active", "completed", "paused", "dropped"]
            if v not in allowed_statuses:
                raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v


class MajorGoalResponse(MajorGoalBase, BaseResourceSchema):
    """Schema for major goal responses."""
    
    user_id: UUID = Field(..., description="ID of the user who owns this goal")
    planning_session_id: Optional[UUID] = Field(None, description="ID of the planning session this goal belongs to")
    sub_projects_count: Optional[int] = Field(None, description="Number of sub-projects for this goal")
    progress_entries_count: Optional[int] = Field(None, description="Number of progress entries for this goal")


# Sub-Project Schemas
class SubProjectBase(BaseSchema):
    """Base sub-project schema with common fields."""
    
    title: str = Field(..., max_length=200, description="Title of the sub-project")
    description: Optional[str] = Field(None, description="Detailed description of the sub-project")
    status: str = Field(default='not_started', description="Status of the sub-project")
    due_date: Optional[date] = Field(None, description="Due date for the sub-project")
    completion_percentage: int = Field(default=0, ge=0, le=100, description="Completion percentage (0-100)")
    sort_order: Optional[int] = Field(None, description="Display order for the sub-project")
    
    @field_validator("status")
    @classmethod
    def validate_status(cls, v: str) -> str:
        """Validate sub-project status."""
        allowed_statuses = ["not_started", "in_progress", "completed", "paused", "cancelled"]
        if v not in allowed_statuses:
            raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v


class SubProjectCreate(SubProjectBase):
    """Schema for creating a new sub-project."""
    
    major_goal_id: UUID = Field(..., description="ID of the major goal this sub-project belongs to")


class SubProjectUpdate(BaseSchema):
    """Schema for updating a sub-project."""
    
    title: Optional[str] = Field(None, max_length=200, description="Title of the sub-project")
    description: Optional[str] = Field(None, description="Detailed description of the sub-project")
    status: Optional[str] = Field(None, description="Status of the sub-project")
    due_date: Optional[date] = Field(None, description="Due date for the sub-project")
    completion_percentage: Optional[int] = Field(None, ge=0, le=100, description="Completion percentage (0-100)")
    sort_order: Optional[int] = Field(None, description="Display order for the sub-project")
    
    @field_validator("status")
    @classmethod
    def validate_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate sub-project status."""
        if v is not None:
            allowed_statuses = ["not_started", "in_progress", "completed", "paused", "cancelled"]
            if v not in allowed_statuses:
                raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v


class SubProjectResponse(SubProjectBase, BaseResourceSchema):
    """Schema for sub-project responses."""
    
    major_goal_id: UUID = Field(..., description="ID of the major goal this sub-project belongs to")
    progress_entries_count: Optional[int] = Field(None, description="Number of progress entries for this sub-project")


# Goal Uncertainty Schemas
class GoalUncertaintyBase(BaseSchema):
    """Base goal uncertainty schema with common fields."""
    
    uncertainty_description: str = Field(..., description="Description of the uncertainty or risk")
    reasons: Optional[str] = Field(None, description="Reasons why this uncertainty exists")
    worst_case_scenario: Optional[str] = Field(None, description="Description of worst-case scenario")
    reality_check: Optional[str] = Field(None, description="Reality check and likelihood assessment")
    action_plan: Optional[str] = Field(None, description="Action plan to address this uncertainty")


class GoalUncertaintyCreate(GoalUncertaintyBase):
    """Schema for creating a new goal uncertainty."""
    
    major_goal_id: UUID = Field(..., description="ID of the major goal this uncertainty relates to")


class GoalUncertaintyUpdate(BaseSchema):
    """Schema for updating a goal uncertainty."""
    
    uncertainty_description: Optional[str] = Field(None, description="Description of the uncertainty or risk")
    reasons: Optional[str] = Field(None, description="Reasons why this uncertainty exists")
    worst_case_scenario: Optional[str] = Field(None, description="Description of worst-case scenario")
    reality_check: Optional[str] = Field(None, description="Reality check and likelihood assessment")
    action_plan: Optional[str] = Field(None, description="Action plan to address this uncertainty")


class GoalUncertaintyResponse(GoalUncertaintyBase, BaseResourceSchema):
    """Schema for goal uncertainty responses."""
    
    major_goal_id: UUID = Field(..., description="ID of the major goal this uncertainty relates to")


# Goal Summary Schemas
class GoalSummary(BaseSchema):
    """Schema for goal summary information."""
    
    goal: MajorGoalResponse = Field(..., description="Major goal details")
    sub_projects: List[SubProjectResponse] = Field(default=[], description="List of sub-projects")
    uncertainties: List[GoalUncertaintyResponse] = Field(default=[], description="List of uncertainties")
    recent_progress: Optional[Dict[str, Any]] = Field(None, description="Recent progress information")


class GoalsOverview(BaseSchema):
    """Schema for goals overview dashboard."""
    
    total_goals: int = Field(default=0, description="Total number of goals")
    active_goals: int = Field(default=0, description="Number of active goals")
    completed_goals: int = Field(default=0, description="Number of completed goals")
    paused_goals: int = Field(default=0, description="Number of paused goals")
    dropped_goals: int = Field(default=0, description="Number of dropped goals")
    average_completion: float = Field(default=0.0, description="Average completion percentage")
    goals_by_life_area: Dict[str, int] = Field(default={}, description="Goals count by life area")
    upcoming_deadlines: List[Dict[str, Any]] = Field(default=[], description="Upcoming goal deadlines")
