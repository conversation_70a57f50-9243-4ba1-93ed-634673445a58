"""User model for 8760 Hours Life Planning Platform.

This module contains the User model with authentication capabilities
and relationships to life planning data.
"""

from sqlalchemy import <PERSON>olean, Column, String, DateTime
from sqlalchemy.orm import relationship

from src.hours_8760.models.base import BaseModel


class User(BaseModel):
    """User model with authentication and life planning capabilities.

    This model represents users of the 8760 Hours Life Planning Platform
    with authentication, profile information, and subscription details.
    """

    __tablename__ = "users"

    email = Column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        doc="User's email address"
    )
    password_hash = Column(
        String(255),
        nullable=False,
        doc="Hashed password"
    )
    first_name = Column(
        String(100),
        nullable=True,
        doc="User's first name"
    )
    last_name = Column(
        String(100),
        nullable=True,
        doc="User's last name"
    )
    last_login = Column(
        DateTime,
        nullable=True,
        doc="Timestamp of last login"
    )
    email_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user's email has been verified"
    )
    subscription_tier = Column(
        String(50),
        default='free',
        nullable=False,
        doc="User's subscription tier (free, premium, etc.)"
    )

    # Relationships
    planning_sessions = relationship(
        "PlanningSession",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    life_area_assessments = relationship(
        "LifeAreaAssessment",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    major_goals = relationship(
        "MajorGoal",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    review_sessions = relationship(
        "ReviewSession",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        """Return string representation of the user."""
        return f"<User(id={self.id}, email='{self.email}', name='{self.first_name} {self.last_name}')>"
