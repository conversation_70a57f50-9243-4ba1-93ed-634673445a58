"""Review and Progress models for 8760 Hours Life Planning Platform.

This module contains models for review sessions, progress tracking,
and reflection activities.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Integer, Text, ForeignKey, DateTime, Date
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from decimal import Decimal

from src.hours_8760.models.base import BaseModel


class ReviewSession(BaseModel):
    """Review session model for periodic life and goal reviews.
    
    This model represents monthly, quarterly, and annual review sessions
    where users reflect on progress and make adjustments.
    """
    
    __tablename__ = "review_sessions"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who owns this review session"
    )
    planning_session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("planning_sessions.id"),
        nullable=True,
        doc="ID of the planning session this review relates to"
    )
    type = Column(
        String(50),
        nullable=False,
        doc="Type of review (monthly, quarterly, annual)"
    )
    year = Column(
        Integer,
        nullable=False,
        doc="Year of the review"
    )
    month = Column(
        Integer,
        nullable=True,
        doc="Month of the review (1-12 for monthly reviews)"
    )
    quarter = Column(
        Integer,
        nullable=True,
        doc="Quarter of the review (1-4 for quarterly reviews)"
    )
    goal_progress_notes = Column(
        Text,
        nullable=True,
        doc="Notes on goal progress during this period"
    )
    what_went_well = Column(
        Text,
        nullable=True,
        doc="Reflection on what went well"
    )
    what_went_poorly = Column(
        Text,
        nullable=True,
        doc="Reflection on what went poorly or could be improved"
    )
    adjustments_needed = Column(
        Text,
        nullable=True,
        doc="Adjustments needed for future periods"
    )
    priority_changes = Column(
        JSONB,
        nullable=True,
        doc="Changes to goal priorities as JSON"
    )
    overall_satisfaction = Column(
        Integer,
        nullable=True,
        doc="Overall satisfaction rating (1-7 scale)"
    )
    completed_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when the review was completed"
    )
    
    # Relationships
    user = relationship("User", back_populates="review_sessions")
    planning_session = relationship("PlanningSession", back_populates="review_sessions")
    
    def __repr__(self) -> str:
        """Return string representation of the review session."""
        return f"<ReviewSession(id={self.id}, type='{self.type}', year={self.year}, month={self.month})>"


class ProgressEntry(BaseModel):
    """Progress entry model for tracking goal and project progress.
    
    This model stores individual progress updates for goals and sub-projects
    with quantitative and qualitative data.
    """
    
    __tablename__ = "progress_entries"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who made this progress entry"
    )
    major_goal_id = Column(
        UUID(as_uuid=True),
        ForeignKey("major_goals.id"),
        nullable=True,
        doc="ID of the major goal this progress relates to"
    )
    sub_project_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sub_projects.id"),
        nullable=True,
        doc="ID of the sub-project this progress relates to"
    )
    entry_date = Column(
        Date,
        nullable=False,
        doc="Date of the progress entry"
    )
    progress_value = Column(
        String(100),
        nullable=True,
        doc="Quantitative progress value (flexible format)"
    )
    notes = Column(
        Text,
        nullable=True,
        doc="Qualitative notes about progress"
    )
    mood = Column(
        Integer,
        nullable=True,
        doc="Mood/energy level during this progress (1-7 scale)"
    )
    
    # Relationships
    user = relationship("User")
    major_goal = relationship("MajorGoal", back_populates="progress_entries")
    sub_project = relationship("SubProject", back_populates="progress_entries")
    
    def __repr__(self) -> str:
        """Return string representation of the progress entry."""
        return f"<ProgressEntry(id={self.id}, date={self.entry_date}, value='{self.progress_value}')>"


class QuestionTemplate(BaseModel):
    """Question template model for structured reflection.
    
    This model stores template questions for different types of
    reflection activities (Hamming questions, life area assessments, etc.).
    """
    
    __tablename__ = "question_templates"
    
    category = Column(
        String(100),
        nullable=False,
        doc="Category of question (hamming, reflection, visioning)"
    )
    life_area_id = Column(
        UUID(as_uuid=True),
        ForeignKey("life_areas.id"),
        nullable=True,
        doc="Life area this question relates to (if applicable)"
    )
    question_text = Column(
        Text,
        nullable=False,
        doc="The actual question text"
    )
    question_type = Column(
        String(50),
        nullable=True,
        doc="Type of question (text, scale, multiple_choice)"
    )
    sort_order = Column(
        Integer,
        nullable=True,
        doc="Display order for the question"
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether this question template is currently active"
    )
    
    # Relationships
    life_area = relationship("LifeArea", back_populates="question_templates")
    responses = relationship(
        "QuestionResponse",
        back_populates="question_template",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """Return string representation of the question template."""
        return f"<QuestionTemplate(id={self.id}, category='{self.category}', text='{self.question_text[:50]}...')>"


class QuestionResponse(BaseModel):
    """Question response model for storing user answers.
    
    This model stores user responses to template questions
    during planning and review sessions.
    """
    
    __tablename__ = "question_responses"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who provided this response"
    )
    planning_session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("planning_sessions.id"),
        nullable=True,
        doc="ID of the planning session this response belongs to"
    )
    question_template_id = Column(
        UUID(as_uuid=True),
        ForeignKey("question_templates.id"),
        nullable=False,
        doc="ID of the question template this response answers"
    )
    response_text = Column(
        Text,
        nullable=True,
        doc="Text response to the question"
    )
    response_number = Column(
        Integer,
        nullable=True,
        doc="Numeric response to the question (for scale questions)"
    )
    
    # Relationships
    user = relationship("User")
    planning_session = relationship("PlanningSession", back_populates="question_responses")
    question_template = relationship("QuestionTemplate", back_populates="responses")
    
    def __repr__(self) -> str:
        """Return string representation of the question response."""
        return f"<QuestionResponse(id={self.id}, user_id={self.user_id}, question_id={self.question_template_id})>"
