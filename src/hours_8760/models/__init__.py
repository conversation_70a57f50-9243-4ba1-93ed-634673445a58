"""Models package for the 8760 Hours Life Planning Platform.

This package contains all SQLAlchemy models for the application,
including user management, life areas, planning sessions, goals,
and progress tracking.
"""

from .base import Base, BaseModel, SoftDeleteMixin, TimestampMixin
from .user import User
from .life_area import LifeArea, LifeAreaAssessment, LifeAreaMetric
from .planning_session import PlanningSession, MindMap, CalendarEvent
from .goal import MajorGoal, SubProject, GoalUncertainty
from .review import ReviewSession, ProgressEntry, QuestionTemplate, QuestionResponse
from .task import Task, TimeEntry, TaskStatus, TaskPriority, DependencyType

__all__ = [
    "Base",
    "BaseModel",
    "SoftDeleteMixin",
    "TimestampMixin",
    "User",
    "LifeArea",
    "LifeAreaAssessment",
    "LifeAreaMetric",
    "PlanningSession",
    "MindMap",
    "CalendarEvent",
    "MajorGoal",
    "SubProject",
    "GoalUncertainty",
    "ReviewSession",
    "ProgressEntry",
    "QuestionTemplate",
    "QuestionResponse",
    "Task",
    "TimeEntry",
    "TaskStatus",
    "TaskPriority",
    "DependencyType",
]
