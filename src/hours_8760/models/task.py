"""Task management models for 8760 Hours application.

This module defines the core task management data models including:
- Task entity with status, priority, and relationships
- Time tracking entries
- Task dependencies
- Integration with existing goal and life area models
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    Column,
    DateTime,
    Enum as SQLE<PERSON>,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Table,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin


class TaskStatus(str, Enum):
    """Task status enumeration."""
    
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    DONE = "done"
    BLOCKED = "blocked"
    DEFERRED = "deferred"


class TaskPriority(str, Enum):
    """Task priority enumeration."""
    
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class DependencyType(str, Enum):
    """Task dependency type enumeration."""
    
    DEPENDS_ON = "depends_on"  # Task A depends on task B to be completed first
    REQUIRES = "requires"      # Task A requires a component from task B
    ENABLES = "enables"        # Task A enables task B to be started


# Association table for task dependencies
task_dependencies = Table(
    'task_dependencies',
    Base.metadata,
    Column('source_task_id', PGUUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True),
    Column('target_task_id', PGUUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True),
    Column('dependency_type', SQLEnum(DependencyType), nullable=False),
    Column('description', Text, nullable=True),
    Column('created_at', DateTime, nullable=False, default=datetime.utcnow),
)

# Association table for task tags
task_tags = Table(
    'task_tags',
    Base.metadata,
    Column('task_id', PGUUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True),
    Column('tag', String(50), primary_key=True),
)


class Task(Base, TimestampMixin):
    """Task model for granular goal tracking and management.
    
    Attributes:
        id: Unique task identifier
        title: Task title/name
        description: Detailed task description
        status: Current task status (TODO, IN_PROGRESS, etc.)
        priority: Task priority level (HIGH, MEDIUM, LOW)
        goal_id: Associated goal ID (foreign key)
        life_area_id: Associated life area ID (foreign key)
        parent_task_id: Parent task for subtask hierarchy
        estimated_hours: Estimated time to complete (hours)
        actual_hours: Actual time spent (calculated from time entries)
        due_date: Optional due date
        completed_at: Timestamp when task was completed
        
        # Relationships
        goal: Associated goal object
        life_area: Associated life area object
        parent_task: Parent task object
        subtasks: List of child tasks
        time_entries: List of time tracking entries
        dependencies: List of tasks this task depends on
        dependents: List of tasks that depend on this task
        tags: List of task tags
    """
    
    __tablename__ = 'tasks'
    
    # Primary fields
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    title = Column(String(200), nullable=False, doc="Task title")
    description = Column(Text, nullable=True, doc="Detailed task description")
    
    # Status and priority
    status = Column(
        SQLEnum(TaskStatus), 
        nullable=False, 
        default=TaskStatus.TODO,
        doc="Current task status"
    )
    priority = Column(
        SQLEnum(TaskPriority), 
        nullable=False, 
        default=TaskPriority.MEDIUM,
        doc="Task priority level"
    )
    
    # Relationships
    goal_id = Column(
        PGUUID(as_uuid=True), 
        ForeignKey('goals.id'), 
        nullable=True,
        doc="Associated goal ID"
    )
    life_area_id = Column(
        PGUUID(as_uuid=True), 
        ForeignKey('life_areas.id'), 
        nullable=True,
        doc="Associated life area ID"
    )
    parent_task_id = Column(
        PGUUID(as_uuid=True), 
        ForeignKey('tasks.id'), 
        nullable=True,
        doc="Parent task for subtask hierarchy"
    )
    
    # Time tracking
    estimated_hours = Column(
        Float, 
        nullable=True, 
        default=0.0,
        doc="Estimated time to complete (hours)"
    )
    actual_hours = Column(
        Float, 
        nullable=True, 
        default=0.0,
        doc="Actual time spent (calculated from time entries)"
    )
    
    # Dates
    due_date = Column(DateTime, nullable=True, doc="Optional due date")
    completed_at = Column(DateTime, nullable=True, doc="Completion timestamp")
    
    # Relationships
    goal = relationship("Goal", back_populates="tasks")
    life_area = relationship("LifeArea", back_populates="tasks")
    
    # Hierarchical relationships
    parent_task = relationship("Task", remote_side=[id], back_populates="subtasks")
    subtasks = relationship("Task", back_populates="parent_task")
    
    # Time tracking
    time_entries = relationship("TimeEntry", back_populates="task", cascade="all, delete-orphan")
    
    # Dependencies (many-to-many self-referential)
    dependencies = relationship(
        "Task",
        secondary=task_dependencies,
        primaryjoin=id == task_dependencies.c.source_task_id,
        secondaryjoin=id == task_dependencies.c.target_task_id,
        back_populates="dependents"
    )
    dependents = relationship(
        "Task",
        secondary=task_dependencies,
        primaryjoin=id == task_dependencies.c.target_task_id,
        secondaryjoin=id == task_dependencies.c.source_task_id,
        back_populates="dependencies"
    )
    
    def __repr__(self) -> str:
        """String representation of task."""
        return f"<Task(id={self.id}, title='{self.title}', status={self.status})>"
    
    @property
    def is_completed(self) -> bool:
        """Check if task is completed."""
        return self.status == TaskStatus.DONE
    
    @property
    def is_overdue(self) -> bool:
        """Check if task is overdue."""
        if not self.due_date or self.is_completed:
            return False
        return datetime.utcnow() > self.due_date
    
    @property
    def progress_percentage(self) -> float:
        """Calculate task progress percentage based on subtasks."""
        if not self.subtasks:
            return 100.0 if self.is_completed else 0.0
        
        completed_subtasks = sum(1 for subtask in self.subtasks if subtask.is_completed)
        return (completed_subtasks / len(self.subtasks)) * 100.0
    
    def add_dependency(self, target_task: 'Task', dependency_type: DependencyType = DependencyType.DEPENDS_ON) -> None:
        """Add a dependency to another task."""
        if target_task not in self.dependencies:
            self.dependencies.append(target_task)
    
    def remove_dependency(self, target_task: 'Task') -> None:
        """Remove a dependency to another task."""
        if target_task in self.dependencies:
            self.dependencies.remove(target_task)
    
    def mark_completed(self) -> None:
        """Mark task as completed with timestamp."""
        self.status = TaskStatus.DONE
        self.completed_at = datetime.utcnow()
    
    def calculate_actual_hours(self) -> float:
        """Calculate actual hours from time entries."""
        total_minutes = sum(entry.duration_minutes for entry in self.time_entries)
        return total_minutes / 60.0


class TimeEntry(Base, TimestampMixin):
    """Time tracking entry for tasks.
    
    Attributes:
        id: Unique time entry identifier
        task_id: Associated task ID
        start_time: When time tracking started
        end_time: When time tracking ended (None if still running)
        duration_minutes: Total duration in minutes
        notes: Optional notes about the time entry
        tags: List of tags for categorization
        
        # Relationships
        task: Associated task object
    """
    
    __tablename__ = 'time_entries'
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    task_id = Column(
        PGUUID(as_uuid=True), 
        ForeignKey('tasks.id'), 
        nullable=False,
        doc="Associated task ID"
    )
    
    # Time tracking
    start_time = Column(DateTime, nullable=False, doc="Start time")
    end_time = Column(DateTime, nullable=True, doc="End time (None if running)")
    duration_minutes = Column(Integer, nullable=False, default=0, doc="Duration in minutes")
    
    # Metadata
    notes = Column(Text, nullable=True, doc="Optional notes")
    
    # Relationships
    task = relationship("Task", back_populates="time_entries")
    
    def __repr__(self) -> str:
        """String representation of time entry."""
        return f"<TimeEntry(id={self.id}, task_id={self.task_id}, duration={self.duration_minutes}min)>"
    
    @property
    def is_running(self) -> bool:
        """Check if time entry is currently running."""
        return self.end_time is None
    
    @property
    def duration_hours(self) -> float:
        """Get duration in hours."""
        return self.duration_minutes / 60.0
    
    def stop(self) -> None:
        """Stop the time entry and calculate duration."""
        if self.is_running:
            self.end_time = datetime.utcnow()
            self.duration_minutes = int((self.end_time - self.start_time).total_seconds() / 60)


# Association table for time entry tags
time_entry_tags = Table(
    'time_entry_tags',
    Base.metadata,
    Column('time_entry_id', PGUUID(as_uuid=True), ForeignKey('time_entries.id'), primary_key=True),
    Column('tag', String(50), primary_key=True),
)
