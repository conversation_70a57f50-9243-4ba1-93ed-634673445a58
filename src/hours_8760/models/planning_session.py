"""Planning Session models for 8760 Hours Life Planning Platform.

This module contains models for annual planning sessions and
related planning activities.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Integer, Text, ForeignKey, DateTime, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from src.hours_8760.models.base import BaseModel


class PlanningSession(BaseModel):
    """Annual planning session model.
    
    This model represents a user's annual life planning session
    where they assess their current state and set goals for the year.
    """
    
    __tablename__ = "planning_sessions"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who owns this planning session"
    )
    year = Column(
        Integer,
        nullable=False,
        doc="Year this planning session is for"
    )
    status = Column(
        String(50),
        default='in_progress',
        nullable=False,
        doc="Status of the planning session (in_progress, completed, archived)"
    )
    yearly_theme = Column(
        String(200),
        nullable=True,
        doc="Overall theme or focus for the year"
    )
    life_philosophy = Column(
        Text,
        nullable=True,
        doc="User's life philosophy and core values"
    )
    ideal_future_vision = Column(
        Text,
        nullable=True,
        doc="Vision of ideal future state"
    )
    major_focus_areas = Column(
        ARRAY(UUID(as_uuid=True)),
        nullable=True,
        doc="Array of life area IDs that are major focus areas for the year"
    )
    completed_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when the planning session was completed"
    )
    
    # Relationships
    user = relationship("User", back_populates="planning_sessions")
    major_goals = relationship(
        "MajorGoal",
        back_populates="planning_session",
        cascade="all, delete-orphan"
    )
    review_sessions = relationship(
        "ReviewSession",
        back_populates="planning_session",
        cascade="all, delete-orphan"
    )
    mind_maps = relationship(
        "MindMap",
        back_populates="planning_session",
        cascade="all, delete-orphan"
    )
    calendar_events = relationship(
        "CalendarEvent",
        back_populates="planning_session",
        cascade="all, delete-orphan"
    )
    question_responses = relationship(
        "QuestionResponse",
        back_populates="planning_session",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """Return string representation of the planning session."""
        return f"<PlanningSession(id={self.id}, user_id={self.user_id}, year={self.year}, status='{self.status}')>"


class MindMap(BaseModel):
    """Mind map model for visual planning and brainstorming.
    
    This model stores mind map data for different planning activities
    like life status assessment, future visioning, and goal mapping.
    """
    
    __tablename__ = "mind_maps"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who created this mind map"
    )
    planning_session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("planning_sessions.id"),
        nullable=True,
        doc="ID of the planning session this mind map belongs to"
    )
    title = Column(
        String(200),
        nullable=False,
        doc="Title of the mind map"
    )
    type = Column(
        String(50),
        nullable=False,
        doc="Type of mind map (life_status, future_vision, goals, custom)"
    )
    data = Column(
        Text,  # JSON data stored as text
        nullable=False,
        doc="Mind map structure and content as JSON"
    )
    
    # Relationships
    user = relationship("User")
    planning_session = relationship("PlanningSession", back_populates="mind_maps")
    
    def __repr__(self) -> str:
        """Return string representation of the mind map."""
        return f"<MindMap(id={self.id}, title='{self.title}', type='{self.type}')>"


class CalendarEvent(BaseModel):
    """Calendar event model for planning milestones and deadlines.
    
    This model stores important dates, milestones, and deadlines
    related to goals and planning activities.
    """
    
    __tablename__ = "calendar_events"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who created this event"
    )
    planning_session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("planning_sessions.id"),
        nullable=True,
        doc="ID of the planning session this event belongs to"
    )
    major_goal_id = Column(
        UUID(as_uuid=True),
        ForeignKey("major_goals.id"),
        nullable=True,
        doc="ID of the major goal this event relates to"
    )
    title = Column(
        String(200),
        nullable=False,
        doc="Title of the calendar event"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Detailed description of the event"
    )
    event_date = Column(
        DateTime,
        nullable=False,
        doc="Date and time of the event"
    )
    event_type = Column(
        String(50),
        nullable=True,
        doc="Type of event (milestone, deadline, review, focus_theme)"
    )
    is_completed = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this event has been completed"
    )
    
    # Relationships
    user = relationship("User")
    planning_session = relationship("PlanningSession", back_populates="calendar_events")
    major_goal = relationship("MajorGoal", back_populates="calendar_events")
    
    def __repr__(self) -> str:
        """Return string representation of the calendar event."""
        return f"<CalendarEvent(id={self.id}, title='{self.title}', date={self.event_date})>"
