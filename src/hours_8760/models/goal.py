"""Goal models for 8760 Hours Life Planning Platform.

This module contains models for major goals, sub-projects, and
goal-related tracking and analysis.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Integer, Text, ForeignKey, DateTime, Date, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from decimal import Decimal

from src.hours_8760.models.base import BaseModel


class MajorGoal(BaseModel):
    """Major goal model for annual planning.
    
    This model represents the 3-5 major goals that users set
    during their annual planning sessions.
    """
    
    __tablename__ = "major_goals"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who owns this goal"
    )
    planning_session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("planning_sessions.id"),
        nullable=True,
        doc="ID of the planning session this goal belongs to"
    )
    title = Column(
        String(200),
        nullable=False,
        doc="Title of the major goal"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Detailed description of the goal"
    )
    life_area_id = Column(
        UUID(as_uuid=True),
        ForeignKey("life_areas.id"),
        nullable=True,
        doc="Primary life area this goal relates to"
    )
    priority_rank = Column(
        Integer,
        nullable=True,
        doc="Priority ranking (1-5 for major goals)"
    )
    success_criteria = Column(
        ARRAY(Text),
        nullable=True,
        doc="List of success criteria for this goal"
    )
    metrics = Column(
        JSONB,
        nullable=True,
        doc="Flexible metrics storage as JSON"
    )
    target_completion_date = Column(
        Date,
        nullable=True,
        doc="Target date for goal completion"
    )
    is_meta_skill = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this goal is focused on developing meta-skills"
    )
    status = Column(
        String(50),
        default='active',
        nullable=False,
        doc="Status of the goal (active, completed, paused, dropped)"
    )
    completion_percentage = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Completion percentage (0-100)"
    )
    
    # Relationships
    user = relationship("User", back_populates="major_goals")
    planning_session = relationship("PlanningSession", back_populates="major_goals")
    life_area = relationship("LifeArea", back_populates="major_goals")
    sub_projects = relationship(
        "SubProject",
        back_populates="major_goal",
        cascade="all, delete-orphan"
    )
    uncertainties = relationship(
        "GoalUncertainty",
        back_populates="major_goal",
        cascade="all, delete-orphan"
    )
    progress_entries = relationship(
        "ProgressEntry",
        back_populates="major_goal",
        cascade="all, delete-orphan"
    )
    calendar_events = relationship(
        "CalendarEvent",
        back_populates="major_goal"
    )
    tasks = relationship(
        "Task",
        back_populates="goal",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """Return string representation of the major goal."""
        return f"<MajorGoal(id={self.id}, title='{self.title}', status='{self.status}')>"


class SubProject(BaseModel):
    """Sub-project model for breaking down major goals.
    
    This model represents smaller projects and tasks that
    contribute to achieving major goals.
    """
    
    __tablename__ = "sub_projects"
    
    major_goal_id = Column(
        UUID(as_uuid=True),
        ForeignKey("major_goals.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the major goal this sub-project belongs to"
    )
    title = Column(
        String(200),
        nullable=False,
        doc="Title of the sub-project"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Detailed description of the sub-project"
    )
    status = Column(
        String(50),
        default='not_started',
        nullable=False,
        doc="Status of the sub-project"
    )
    due_date = Column(
        Date,
        nullable=True,
        doc="Due date for the sub-project"
    )
    completion_percentage = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Completion percentage (0-100)"
    )
    sort_order = Column(
        Integer,
        nullable=True,
        doc="Display order for the sub-project"
    )
    
    # Relationships
    major_goal = relationship("MajorGoal", back_populates="sub_projects")
    progress_entries = relationship(
        "ProgressEntry",
        back_populates="sub_project",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """Return string representation of the sub-project."""
        return f"<SubProject(id={self.id}, title='{self.title}', status='{self.status}')>"


class GoalUncertainty(BaseModel):
    """Goal uncertainty analysis model.
    
    This model stores uncertainty analysis for major goals
    to help users address potential obstacles and risks.
    """
    
    __tablename__ = "goal_uncertainties"
    
    major_goal_id = Column(
        UUID(as_uuid=True),
        ForeignKey("major_goals.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the major goal this uncertainty relates to"
    )
    uncertainty_description = Column(
        Text,
        nullable=False,
        doc="Description of the uncertainty or risk"
    )
    reasons = Column(
        Text,
        nullable=True,
        doc="Reasons why this uncertainty exists"
    )
    worst_case_scenario = Column(
        Text,
        nullable=True,
        doc="Description of worst-case scenario"
    )
    reality_check = Column(
        Text,
        nullable=True,
        doc="Reality check and likelihood assessment"
    )
    action_plan = Column(
        Text,
        nullable=True,
        doc="Action plan to address this uncertainty"
    )
    
    # Relationships
    major_goal = relationship("MajorGoal", back_populates="uncertainties")
    
    def __repr__(self) -> str:
        """Return string representation of the goal uncertainty."""
        return f"<GoalUncertainty(id={self.id}, major_goal_id={self.major_goal_id})>"
