"""Life Area models for 8760 Hours Life Planning Platform.

This module contains models for the 12 life areas framework and
user assessments of those areas.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Integer, Text, ForeignKey, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from src.hours_8760.models.base import BaseModel


class LifeArea(BaseModel):
    """Life Area model representing the 12 key life dimensions.
    
    This model defines the standard life areas used in the 8760 Hours
    methodology for comprehensive life assessment and planning.
    """
    
    __tablename__ = "life_areas"
    
    name = Column(
        String(100),
        nullable=False,
        doc="Name of the life area"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Detailed description of the life area"
    )
    sort_order = Column(
        Integer,
        nullable=True,
        doc="Display order for the life area"
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether this life area is currently active"
    )
    
    # Relationships
    assessments = relationship(
        "LifeAreaAssessment",
        back_populates="life_area",
        cascade="all, delete-orphan"
    )
    metrics = relationship(
        "LifeAreaMetric",
        back_populates="life_area",
        cascade="all, delete-orphan"
    )
    major_goals = relationship(
        "MajorGoal",
        back_populates="life_area"
    )
    question_templates = relationship(
        "QuestionTemplate",
        back_populates="life_area"
    )
    tasks = relationship(
        "Task",
        back_populates="life_area",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """Return string representation of the life area."""
        return f"<LifeArea(id={self.id}, name='{self.name}')>"


class LifeAreaAssessment(BaseModel):
    """User's assessment of a specific life area.
    
    This model stores user ratings and reflections for each life area
    during planning sessions and reviews.
    """
    
    __tablename__ = "life_area_assessments"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who made this assessment"
    )
    life_area_id = Column(
        UUID(as_uuid=True),
        ForeignKey("life_areas.id"),
        nullable=False,
        doc="ID of the life area being assessed"
    )
    year = Column(
        Integer,
        nullable=False,
        doc="Year of the assessment"
    )
    quarter = Column(
        Integer,
        nullable=True,
        doc="Quarter of the assessment (1-4, NULL for annual)"
    )
    overall_rating = Column(
        Integer,
        nullable=True,
        doc="Overall rating on 1-7 scale"
    )
    strengths = Column(
        ARRAY(Text),
        nullable=True,
        doc="List of strengths in this life area"
    )
    weaknesses = Column(
        ARRAY(Text),
        nullable=True,
        doc="List of weaknesses in this life area"
    )
    key_accomplishments = Column(
        ARRAY(Text),
        nullable=True,
        doc="Key accomplishments in this life area"
    )
    areas_for_improvement = Column(
        ARRAY(Text),
        nullable=True,
        doc="Areas identified for improvement"
    )
    notes = Column(
        Text,
        nullable=True,
        doc="Additional notes and reflections"
    )
    
    # Relationships
    user = relationship("User", back_populates="life_area_assessments")
    life_area = relationship("LifeArea", back_populates="assessments")
    
    def __repr__(self) -> str:
        """Return string representation of the assessment."""
        return f"<LifeAreaAssessment(id={self.id}, user_id={self.user_id}, life_area_id={self.life_area_id}, year={self.year})>"


class LifeAreaMetric(BaseModel):
    """Custom metrics for tracking progress in life areas.
    
    This model allows users to define and track custom metrics
    for each life area beyond the standard assessments.
    """
    
    __tablename__ = "life_area_metrics"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user who created this metric"
    )
    life_area_id = Column(
        UUID(as_uuid=True),
        ForeignKey("life_areas.id"),
        nullable=False,
        doc="ID of the life area this metric belongs to"
    )
    metric_name = Column(
        String(100),
        nullable=False,
        doc="Name of the custom metric"
    )
    metric_type = Column(
        String(50),
        nullable=False,
        doc="Type of metric (number, percentage, boolean, scale)"
    )
    target_value = Column(
        String(100),
        nullable=True,
        doc="Target value for this metric"
    )
    current_value = Column(
        String(100),
        nullable=True,
        doc="Current value of this metric"
    )
    unit = Column(
        String(20),
        nullable=True,
        doc="Unit of measurement for this metric"
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether this metric is currently being tracked"
    )
    
    # Relationships
    user = relationship("User")
    life_area = relationship("LifeArea", back_populates="metrics")
    
    def __repr__(self) -> str:
        """Return string representation of the metric."""
        return f"<LifeAreaMetric(id={self.id}, name='{self.metric_name}', type='{self.metric_type}')>"
