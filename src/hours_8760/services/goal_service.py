"""Goal service for managing major goals and sub-projects.

This module provides business logic for goal operations including
CRUD operations for major goals, sub-projects, and goal uncertainties.
"""

from typing import List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload

from src.hours_8760.models.goal import MajorGoal, SubProject, GoalUncertainty
from src.hours_8760.schemas.goal import (
    MajorGoalCreate,
    MajorGoalUpdate,
    SubProjectCreate,
    SubProjectUpdate,
    GoalUncertaintyCreate,
    GoalUncertaintyUpdate
)


class GoalService:
    """Service class for goal operations."""
    
    def __init__(self, db: AsyncSession):
        """Initialize the goal service.
        
        Args:
            db: Database session.
        """
        self.db = db
    
    # Major Goal CRUD operations
    async def get_user_goals(
        self, 
        user_id: UUID,
        planning_session_id: Optional[UUID] = None,
        status: Optional[str] = None,
        life_area_id: Optional[UUID] = None
    ) -> List[MajorGoal]:
        """Get user's major goals.
        
        Args:
            user_id: User's unique identifier.
            planning_session_id: Optional planning session filter.
            status: Optional status filter.
            life_area_id: Optional life area filter.
            
        Returns:
            List of major goals.
        """
        query = select(MajorGoal).options(
            selectinload(MajorGoal.life_area),
            selectinload(MajorGoal.sub_projects),
            selectinload(MajorGoal.uncertainties)
        ).where(MajorGoal.user_id == user_id)
        
        if planning_session_id:
            query = query.where(MajorGoal.planning_session_id == planning_session_id)
        
        if status:
            query = query.where(MajorGoal.status == status)
        
        if life_area_id:
            query = query.where(MajorGoal.life_area_id == life_area_id)
        
        query = query.order_by(MajorGoal.priority_rank, MajorGoal.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_goal_by_id(
        self, 
        goal_id: UUID, 
        user_id: UUID
    ) -> Optional[MajorGoal]:
        """Get a major goal by ID for a specific user.
        
        Args:
            goal_id: Goal's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Major goal if found, None otherwise.
        """
        result = await self.db.execute(
            select(MajorGoal)
            .options(
                selectinload(MajorGoal.life_area),
                selectinload(MajorGoal.sub_projects),
                selectinload(MajorGoal.uncertainties)
            )
            .where(
                and_(
                    MajorGoal.id == goal_id,
                    MajorGoal.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def create_goal(
        self, 
        user_id: UUID, 
        goal_data: MajorGoalCreate
    ) -> MajorGoal:
        """Create a new major goal.
        
        Args:
            user_id: User's unique identifier.
            goal_data: Goal creation data.
            
        Returns:
            Created goal object.
        """
        goal = MajorGoal(
            user_id=user_id,
            **goal_data.model_dump()
        )
        
        self.db.add(goal)
        await self.db.commit()
        await self.db.refresh(goal)
        
        # Load relationships
        await self.db.refresh(goal, ["life_area", "sub_projects", "uncertainties"])
        return goal
    
    async def update_goal(
        self, 
        goal_id: UUID, 
        user_id: UUID, 
        goal_data: MajorGoalUpdate
    ) -> Optional[MajorGoal]:
        """Update a major goal.
        
        Args:
            goal_id: Goal's unique identifier.
            user_id: User's unique identifier.
            goal_data: Updated goal data.
            
        Returns:
            Updated goal object if found, None otherwise.
        """
        goal = await self.get_goal_by_id(goal_id, user_id)
        if not goal:
            return None
        
        update_data = goal_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(goal, field, value)
        
        await self.db.commit()
        await self.db.refresh(goal)
        return goal
    
    async def delete_goal(self, goal_id: UUID, user_id: UUID) -> bool:
        """Delete a major goal.
        
        Args:
            goal_id: Goal's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            True if goal was deleted, False if not found.
        """
        goal = await self.get_goal_by_id(goal_id, user_id)
        if not goal:
            return False
        
        goal.soft_delete()
        await self.db.commit()
        return True
    
    # Sub-Project operations
    async def get_goal_sub_projects(
        self, 
        goal_id: UUID, 
        user_id: UUID,
        status: Optional[str] = None
    ) -> List[SubProject]:
        """Get sub-projects for a major goal.
        
        Args:
            goal_id: Goal's unique identifier.
            user_id: User's unique identifier.
            status: Optional status filter.
            
        Returns:
            List of sub-projects.
        """
        # First verify the goal belongs to the user
        goal = await self.get_goal_by_id(goal_id, user_id)
        if not goal:
            return []
        
        query = select(SubProject).where(SubProject.major_goal_id == goal_id)
        
        if status:
            query = query.where(SubProject.status == status)
        
        query = query.order_by(SubProject.sort_order, SubProject.created_at)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_sub_project_by_id(
        self, 
        sub_project_id: UUID, 
        user_id: UUID
    ) -> Optional[SubProject]:
        """Get a sub-project by ID for a specific user.
        
        Args:
            sub_project_id: Sub-project's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Sub-project if found, None otherwise.
        """
        result = await self.db.execute(
            select(SubProject)
            .join(MajorGoal)
            .where(
                and_(
                    SubProject.id == sub_project_id,
                    MajorGoal.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def create_sub_project(
        self, 
        user_id: UUID, 
        sub_project_data: SubProjectCreate
    ) -> Optional[SubProject]:
        """Create a new sub-project.
        
        Args:
            user_id: User's unique identifier.
            sub_project_data: Sub-project creation data.
            
        Returns:
            Created sub-project object if goal exists, None otherwise.
        """
        # Verify the goal belongs to the user
        goal = await self.get_goal_by_id(sub_project_data.major_goal_id, user_id)
        if not goal:
            return None
        
        sub_project = SubProject(**sub_project_data.model_dump())
        
        self.db.add(sub_project)
        await self.db.commit()
        await self.db.refresh(sub_project)
        return sub_project
    
    async def update_sub_project(
        self, 
        sub_project_id: UUID, 
        user_id: UUID, 
        sub_project_data: SubProjectUpdate
    ) -> Optional[SubProject]:
        """Update a sub-project.
        
        Args:
            sub_project_id: Sub-project's unique identifier.
            user_id: User's unique identifier.
            sub_project_data: Updated sub-project data.
            
        Returns:
            Updated sub-project object if found, None otherwise.
        """
        sub_project = await self.get_sub_project_by_id(sub_project_id, user_id)
        if not sub_project:
            return None
        
        update_data = sub_project_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(sub_project, field, value)
        
        await self.db.commit()
        await self.db.refresh(sub_project)
        return sub_project
    
    async def delete_sub_project(self, sub_project_id: UUID, user_id: UUID) -> bool:
        """Delete a sub-project.
        
        Args:
            sub_project_id: Sub-project's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            True if sub-project was deleted, False if not found.
        """
        sub_project = await self.get_sub_project_by_id(sub_project_id, user_id)
        if not sub_project:
            return False
        
        sub_project.soft_delete()
        await self.db.commit()
        return True
    
    # Goal analytics and summary methods
    async def get_goals_overview(self, user_id: UUID, planning_session_id: Optional[UUID] = None) -> dict:
        """Get goals overview for a user.
        
        Args:
            user_id: User's unique identifier.
            planning_session_id: Optional planning session filter.
            
        Returns:
            Dictionary with goals overview data.
        """
        goals = await self.get_user_goals(user_id, planning_session_id)
        
        total_goals = len(goals)
        active_goals = len([g for g in goals if g.status == 'active'])
        completed_goals = len([g for g in goals if g.status == 'completed'])
        paused_goals = len([g for g in goals if g.status == 'paused'])
        dropped_goals = len([g for g in goals if g.status == 'dropped'])
        
        # Calculate average completion
        completion_percentages = [g.completion_percentage for g in goals if g.completion_percentage is not None]
        average_completion = sum(completion_percentages) / len(completion_percentages) if completion_percentages else 0.0
        
        return {
            "total_goals": total_goals,
            "active_goals": active_goals,
            "completed_goals": completed_goals,
            "paused_goals": paused_goals,
            "dropped_goals": dropped_goals,
            "average_completion": average_completion,
            "goals_by_life_area": {},  # TODO: Calculate by life area
            "upcoming_deadlines": []   # TODO: Calculate upcoming deadlines
        }
