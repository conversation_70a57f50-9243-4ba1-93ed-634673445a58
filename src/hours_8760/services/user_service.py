"""User service for authentication and user management.

This module provides business logic for user operations including
registration, authentication, profile management, and password reset.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from src.hours_8760.models.user import User
from src.hours_8760.schemas.user import UserCreate, UserUpdate, UserRegister
from src.hours_8760.core.security import (
    get_password_hash,
    verify_password,
    create_access_token,
    generate_password_reset_token,
    verify_password_reset_token
)


class UserService:
    """Service class for user operations."""
    
    def __init__(self, db: AsyncSession):
        """Initialize the user service.
        
        Args:
            db: Database session.
        """
        self.db = db
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get a user by ID.
        
        Args:
            user_id: User's unique identifier.
            
        Returns:
            User object if found, None otherwise.
        """
        result = await self.db.execute(
            select(User).where(User.id == user_id, User.is_deleted == False)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by email address.
        
        Args:
            email: User's email address.
            
        Returns:
            User object if found, None otherwise.
        """
        result = await self.db.execute(
            select(User).where(User.email == email, User.is_deleted == False)
        )
        return result.scalar_one_or_none()
    
    async def create_user(self, user_data: UserCreate) -> User:
        """Create a new user.
        
        Args:
            user_data: User creation data.
            
        Returns:
            Created user object.
            
        Raises:
            ValueError: If email already exists.
        """
        # Check if user with email already exists
        existing_user = await self.get_user_by_email(user_data.email)
        if existing_user:
            raise ValueError("User with this email already exists")
        
        # Hash the password
        hashed_password = get_password_hash(user_data.password)
        
        # Create user object
        user = User(
            email=user_data.email,
            password_hash=hashed_password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            email_verified=False,
            subscription_tier='free'
        )
        
        try:
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            return user
        except IntegrityError:
            await self.db.rollback()
            raise ValueError("User with this email already exists")
    
    async def register_user(self, user_data: UserRegister) -> User:
        """Register a new user with validation.
        
        Args:
            user_data: User registration data.
            
        Returns:
            Created user object.
        """
        # Convert to UserCreate (excluding confirm_password)
        create_data = UserCreate(
            email=user_data.email,
            password=user_data.password,
            first_name=user_data.first_name,
            last_name=user_data.last_name
        )
        
        return await self.create_user(create_data)
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate a user with email and password.
        
        Args:
            email: User's email address.
            password: User's password.
            
        Returns:
            User object if authentication successful, None otherwise.
        """
        user = await self.get_user_by_email(email)
        if not user:
            return None
        
        if not verify_password(password, user.password_hash):
            return None
        
        # Update last login timestamp
        user.last_login = datetime.utcnow()
        await self.db.commit()
        
        return user
    
    async def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[User]:
        """Update user information.
        
        Args:
            user_id: User's unique identifier.
            user_data: Updated user data.
            
        Returns:
            Updated user object if found, None otherwise.
            
        Raises:
            ValueError: If email already exists for another user.
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            return None
        
        # Check if email is being updated and already exists
        if user_data.email and user_data.email != user.email:
            existing_user = await self.get_user_by_email(user_data.email)
            if existing_user and existing_user.id != user_id:
                raise ValueError("User with this email already exists")
        
        # Update user fields
        update_data = user_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        try:
            await self.db.commit()
            await self.db.refresh(user)
            return user
        except IntegrityError:
            await self.db.rollback()
            raise ValueError("User with this email already exists")
    
    async def delete_user(self, user_id: UUID) -> bool:
        """Soft delete a user.
        
        Args:
            user_id: User's unique identifier.
            
        Returns:
            True if user was deleted, False if not found.
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.soft_delete()
        await self.db.commit()
        return True
    
    async def verify_email(self, user_id: UUID) -> bool:
        """Mark user's email as verified.
        
        Args:
            user_id: User's unique identifier.
            
        Returns:
            True if user was found and updated, False otherwise.
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.email_verified = True
        await self.db.commit()
        return True
    
    async def change_password(self, user_id: UUID, current_password: str, new_password: str) -> bool:
        """Change user's password.
        
        Args:
            user_id: User's unique identifier.
            current_password: Current password for verification.
            new_password: New password to set.
            
        Returns:
            True if password was changed, False if current password is incorrect.
            
        Raises:
            ValueError: If user not found.
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        if not verify_password(current_password, user.password_hash):
            return False
        
        user.password_hash = get_password_hash(new_password)
        await self.db.commit()
        return True
    
    async def reset_password(self, token: str, new_password: str) -> bool:
        """Reset user's password using a reset token.
        
        Args:
            token: Password reset token.
            new_password: New password to set.
            
        Returns:
            True if password was reset, False if token is invalid.
        """
        email = verify_password_reset_token(token)
        if not email:
            return False
        
        user = await self.get_user_by_email(email)
        if not user:
            return False
        
        user.password_hash = get_password_hash(new_password)
        await self.db.commit()
        return True
    
    def generate_reset_token(self, email: str) -> str:
        """Generate a password reset token for an email.
        
        Args:
            email: User's email address.
            
        Returns:
            Password reset token.
        """
        return generate_password_reset_token(email)
    
    def create_access_token_for_user(self, user: User) -> str:
        """Create an access token for a user.
        
        Args:
            user: User object.
            
        Returns:
            JWT access token.
        """
        return create_access_token(subject=str(user.id))
