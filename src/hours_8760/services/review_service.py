"""Review service for managing review sessions and progress tracking.

This module provides business logic for review operations including
CRUD operations for review sessions, progress entries, and analytics.
"""

from datetime import datetime, date
from typing import List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload

from src.hours_8760.models.review import ReviewSession, ProgressEntry, QuestionTemplate, QuestionResponse
from src.hours_8760.schemas.review import (
    ReviewSessionCreate,
    ReviewSessionUpdate,
    ProgressEntryCreate,
    ProgressEntryUpdate,
    QuestionTemplateCreate,
    QuestionTemplateUpdate,
    QuestionResponseCreate,
    QuestionResponseUpdate
)


class ReviewService:
    """Service class for review operations."""
    
    def __init__(self, db: AsyncSession):
        """Initialize the review service.
        
        Args:
            db: Database session.
        """
        self.db = db
    
    # Review Session CRUD operations
    async def get_user_review_sessions(
        self, 
        user_id: UUID,
        review_type: Optional[str] = None,
        year: Optional[int] = None,
        planning_session_id: Optional[UUID] = None
    ) -> List[ReviewSession]:
        """Get user's review sessions.
        
        Args:
            user_id: User's unique identifier.
            review_type: Optional type filter (monthly, quarterly, annual).
            year: Optional year filter.
            planning_session_id: Optional planning session filter.
            
        Returns:
            List of review sessions.
        """
        query = select(ReviewSession).where(ReviewSession.user_id == user_id)
        
        if review_type:
            query = query.where(ReviewSession.type == review_type)
        
        if year:
            query = query.where(ReviewSession.year == year)
        
        if planning_session_id:
            query = query.where(ReviewSession.planning_session_id == planning_session_id)
        
        query = query.order_by(
            ReviewSession.year.desc(),
            ReviewSession.quarter.desc(),
            ReviewSession.month.desc(),
            ReviewSession.created_at.desc()
        )
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_review_session_by_id(
        self, 
        review_id: UUID, 
        user_id: UUID
    ) -> Optional[ReviewSession]:
        """Get a review session by ID for a specific user.
        
        Args:
            review_id: Review session's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Review session if found, None otherwise.
        """
        result = await self.db.execute(
            select(ReviewSession).where(
                and_(
                    ReviewSession.id == review_id,
                    ReviewSession.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def create_review_session(
        self, 
        user_id: UUID, 
        review_data: ReviewSessionCreate
    ) -> ReviewSession:
        """Create a new review session.
        
        Args:
            user_id: User's unique identifier.
            review_data: Review session creation data.
            
        Returns:
            Created review session object.
            
        Raises:
            ValueError: If review session already exists for the same criteria.
        """
        # Check if review already exists for this user, type, year, and period
        existing = await self.db.execute(
            select(ReviewSession).where(
                and_(
                    ReviewSession.user_id == user_id,
                    ReviewSession.type == review_data.type,
                    ReviewSession.year == review_data.year,
                    ReviewSession.month == review_data.month,
                    ReviewSession.quarter == review_data.quarter
                )
            )
        )
        
        if existing.scalar_one_or_none():
            period_desc = ""
            if review_data.type == "monthly":
                period_desc = f"month {review_data.month}"
            elif review_data.type == "quarterly":
                period_desc = f"quarter {review_data.quarter}"
            else:
                period_desc = "year"
            
            raise ValueError(f"Review session already exists for {review_data.type} review of {period_desc} {review_data.year}")
        
        review = ReviewSession(
            user_id=user_id,
            **review_data.model_dump()
        )
        
        self.db.add(review)
        await self.db.commit()
        await self.db.refresh(review)
        return review
    
    async def update_review_session(
        self, 
        review_id: UUID, 
        user_id: UUID, 
        review_data: ReviewSessionUpdate
    ) -> Optional[ReviewSession]:
        """Update a review session.
        
        Args:
            review_id: Review session's unique identifier.
            user_id: User's unique identifier.
            review_data: Updated review session data.
            
        Returns:
            Updated review session object if found, None otherwise.
        """
        review = await self.get_review_session_by_id(review_id, user_id)
        if not review:
            return None
        
        update_data = review_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(review, field, value)
        
        # Set completed_at if not already set
        if review.completed_at is None and any([
            review.goal_progress_notes,
            review.what_went_well,
            review.what_went_poorly,
            review.overall_satisfaction
        ]):
            review.completed_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(review)
        return review
    
    async def delete_review_session(self, review_id: UUID, user_id: UUID) -> bool:
        """Delete a review session.
        
        Args:
            review_id: Review session's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            True if review session was deleted, False if not found.
        """
        review = await self.get_review_session_by_id(review_id, user_id)
        if not review:
            return False
        
        review.soft_delete()
        await self.db.commit()
        return True
    
    # Progress Entry operations
    async def get_user_progress_entries(
        self, 
        user_id: UUID,
        major_goal_id: Optional[UUID] = None,
        sub_project_id: Optional[UUID] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[ProgressEntry]:
        """Get user's progress entries.
        
        Args:
            user_id: User's unique identifier.
            major_goal_id: Optional major goal filter.
            sub_project_id: Optional sub-project filter.
            start_date: Optional start date filter.
            end_date: Optional end date filter.
            
        Returns:
            List of progress entries.
        """
        query = select(ProgressEntry).where(ProgressEntry.user_id == user_id)
        
        if major_goal_id:
            query = query.where(ProgressEntry.major_goal_id == major_goal_id)
        
        if sub_project_id:
            query = query.where(ProgressEntry.sub_project_id == sub_project_id)
        
        if start_date:
            query = query.where(ProgressEntry.entry_date >= start_date)
        
        if end_date:
            query = query.where(ProgressEntry.entry_date <= end_date)
        
        query = query.order_by(ProgressEntry.entry_date.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_progress_entry_by_id(
        self, 
        entry_id: UUID, 
        user_id: UUID
    ) -> Optional[ProgressEntry]:
        """Get a progress entry by ID for a specific user.
        
        Args:
            entry_id: Progress entry's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Progress entry if found, None otherwise.
        """
        result = await self.db.execute(
            select(ProgressEntry).where(
                and_(
                    ProgressEntry.id == entry_id,
                    ProgressEntry.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def create_progress_entry(
        self, 
        user_id: UUID, 
        entry_data: ProgressEntryCreate
    ) -> ProgressEntry:
        """Create a new progress entry.
        
        Args:
            user_id: User's unique identifier.
            entry_data: Progress entry creation data.
            
        Returns:
            Created progress entry object.
        """
        entry = ProgressEntry(
            user_id=user_id,
            **entry_data.model_dump()
        )
        
        self.db.add(entry)
        await self.db.commit()
        await self.db.refresh(entry)
        return entry
    
    async def update_progress_entry(
        self, 
        entry_id: UUID, 
        user_id: UUID, 
        entry_data: ProgressEntryUpdate
    ) -> Optional[ProgressEntry]:
        """Update a progress entry.
        
        Args:
            entry_id: Progress entry's unique identifier.
            user_id: User's unique identifier.
            entry_data: Updated progress entry data.
            
        Returns:
            Updated progress entry object if found, None otherwise.
        """
        entry = await self.get_progress_entry_by_id(entry_id, user_id)
        if not entry:
            return None
        
        update_data = entry_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(entry, field, value)
        
        await self.db.commit()
        await self.db.refresh(entry)
        return entry
    
    async def delete_progress_entry(self, entry_id: UUID, user_id: UUID) -> bool:
        """Delete a progress entry.
        
        Args:
            entry_id: Progress entry's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            True if progress entry was deleted, False if not found.
        """
        entry = await self.get_progress_entry_by_id(entry_id, user_id)
        if not entry:
            return False
        
        entry.soft_delete()
        await self.db.commit()
        return True
    
    # Analytics and insights methods
    async def get_progress_analytics(
        self, 
        user_id: UUID, 
        start_date: date, 
        end_date: date
    ) -> dict:
        """Get progress analytics for a user and date range.
        
        Args:
            user_id: User's unique identifier.
            start_date: Start date for analysis.
            end_date: End date for analysis.
            
        Returns:
            Dictionary with progress analytics data.
        """
        entries = await self.get_user_progress_entries(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date
        )
        
        total_entries = len(entries)
        
        # Calculate average mood
        mood_ratings = [e.mood for e in entries if e.mood is not None]
        average_mood = sum(mood_ratings) / len(mood_ratings) if mood_ratings else None
        
        # Find most active goals
        goal_activity = {}
        for entry in entries:
            if entry.major_goal_id:
                goal_activity[entry.major_goal_id] = goal_activity.get(entry.major_goal_id, 0) + 1
        
        most_active_goals = sorted(goal_activity.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "period_start": start_date,
            "period_end": end_date,
            "total_entries": total_entries,
            "average_mood": average_mood,
            "most_active_goals": [{"goal_id": str(goal_id), "entries": count} for goal_id, count in most_active_goals],
            "progress_trends": {},  # TODO: Calculate trends
            "insights": []  # TODO: Generate insights
        }
