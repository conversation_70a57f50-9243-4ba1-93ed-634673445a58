"""Planning Session service for managing annual planning sessions.

This module provides business logic for planning session operations including
CRUD operations for planning sessions, mind maps, and calendar events.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload

from src.hours_8760.models.planning_session import PlanningSession, MindMap, CalendarEvent
from src.hours_8760.schemas.planning_session import (
    PlanningSessionCreate,
    PlanningSessionUpdate,
    MindMapCreate,
    MindMapUpdate,
    CalendarEventCreate,
    CalendarEventUpdate
)


class PlanningSessionService:
    """Service class for planning session operations."""
    
    def __init__(self, db: AsyncSession):
        """Initialize the planning session service.
        
        Args:
            db: Database session.
        """
        self.db = db
    
    # Planning Session CRUD operations
    async def get_user_planning_sessions(
        self, 
        user_id: UUID,
        year: Optional[int] = None,
        status: Optional[str] = None
    ) -> List[PlanningSession]:
        """Get user's planning sessions.
        
        Args:
            user_id: User's unique identifier.
            year: Optional year filter.
            status: Optional status filter.
            
        Returns:
            List of planning sessions.
        """
        query = select(PlanningSession).where(PlanningSession.user_id == user_id)
        
        if year:
            query = query.where(PlanningSession.year == year)
        
        if status:
            query = query.where(PlanningSession.status == status)
        
        query = query.order_by(PlanningSession.year.desc(), PlanningSession.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_planning_session_by_id(
        self, 
        session_id: UUID, 
        user_id: UUID
    ) -> Optional[PlanningSession]:
        """Get a planning session by ID for a specific user.
        
        Args:
            session_id: Planning session's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Planning session if found, None otherwise.
        """
        result = await self.db.execute(
            select(PlanningSession).where(
                and_(
                    PlanningSession.id == session_id,
                    PlanningSession.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_current_planning_session(self, user_id: UUID) -> Optional[PlanningSession]:
        """Get user's current (most recent active) planning session.
        
        Args:
            user_id: User's unique identifier.
            
        Returns:
            Current planning session if found, None otherwise.
        """
        current_year = datetime.now().year
        
        # First try to get current year's session
        result = await self.db.execute(
            select(PlanningSession).where(
                and_(
                    PlanningSession.user_id == user_id,
                    PlanningSession.year == current_year
                )
            )
        )
        session = result.scalar_one_or_none()
        
        if session:
            return session
        
        # If no current year session, get the most recent one
        result = await self.db.execute(
            select(PlanningSession)
            .where(PlanningSession.user_id == user_id)
            .order_by(PlanningSession.year.desc())
            .limit(1)
        )
        return result.scalar_one_or_none()
    
    async def create_planning_session(
        self, 
        user_id: UUID, 
        session_data: PlanningSessionCreate
    ) -> PlanningSession:
        """Create a new planning session.
        
        Args:
            user_id: User's unique identifier.
            session_data: Planning session creation data.
            
        Returns:
            Created planning session object.
            
        Raises:
            ValueError: If planning session already exists for the same user and year.
        """
        # Check if session already exists for this user and year
        existing = await self.db.execute(
            select(PlanningSession).where(
                and_(
                    PlanningSession.user_id == user_id,
                    PlanningSession.year == session_data.year
                )
            )
        )
        
        if existing.scalar_one_or_none():
            raise ValueError("Planning session already exists for this year")
        
        session = PlanningSession(
            user_id=user_id,
            **session_data.model_dump()
        )
        
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        return session
    
    async def update_planning_session(
        self, 
        session_id: UUID, 
        user_id: UUID, 
        session_data: PlanningSessionUpdate
    ) -> Optional[PlanningSession]:
        """Update a planning session.
        
        Args:
            session_id: Planning session's unique identifier.
            user_id: User's unique identifier.
            session_data: Updated planning session data.
            
        Returns:
            Updated planning session object if found, None otherwise.
        """
        session = await self.get_planning_session_by_id(session_id, user_id)
        if not session:
            return None
        
        update_data = session_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(session, field, value)
        
        # Set completed_at if status is being changed to completed
        if session_data.status == "completed" and session.completed_at is None:
            session.completed_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(session)
        return session
    
    async def delete_planning_session(self, session_id: UUID, user_id: UUID) -> bool:
        """Delete a planning session.
        
        Args:
            session_id: Planning session's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            True if planning session was deleted, False if not found.
        """
        session = await self.get_planning_session_by_id(session_id, user_id)
        if not session:
            return False
        
        session.soft_delete()
        await self.db.commit()
        return True
    
    # Mind Map operations
    async def get_session_mind_maps(
        self, 
        session_id: UUID, 
        user_id: UUID,
        mind_map_type: Optional[str] = None
    ) -> List[MindMap]:
        """Get mind maps for a planning session.
        
        Args:
            session_id: Planning session's unique identifier.
            user_id: User's unique identifier.
            mind_map_type: Optional type filter.
            
        Returns:
            List of mind maps.
        """
        query = select(MindMap).where(
            and_(
                MindMap.planning_session_id == session_id,
                MindMap.user_id == user_id
            )
        )
        
        if mind_map_type:
            query = query.where(MindMap.type == mind_map_type)
        
        query = query.order_by(MindMap.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_mind_map_by_id(
        self, 
        mind_map_id: UUID, 
        user_id: UUID
    ) -> Optional[MindMap]:
        """Get a mind map by ID for a specific user.
        
        Args:
            mind_map_id: Mind map's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Mind map if found, None otherwise.
        """
        result = await self.db.execute(
            select(MindMap).where(
                and_(
                    MindMap.id == mind_map_id,
                    MindMap.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def create_mind_map(
        self, 
        user_id: UUID, 
        mind_map_data: MindMapCreate
    ) -> MindMap:
        """Create a new mind map.
        
        Args:
            user_id: User's unique identifier.
            mind_map_data: Mind map creation data.
            
        Returns:
            Created mind map object.
        """
        mind_map = MindMap(
            user_id=user_id,
            **mind_map_data.model_dump()
        )
        
        self.db.add(mind_map)
        await self.db.commit()
        await self.db.refresh(mind_map)
        return mind_map
    
    async def update_mind_map(
        self, 
        mind_map_id: UUID, 
        user_id: UUID, 
        mind_map_data: MindMapUpdate
    ) -> Optional[MindMap]:
        """Update a mind map.
        
        Args:
            mind_map_id: Mind map's unique identifier.
            user_id: User's unique identifier.
            mind_map_data: Updated mind map data.
            
        Returns:
            Updated mind map object if found, None otherwise.
        """
        mind_map = await self.get_mind_map_by_id(mind_map_id, user_id)
        if not mind_map:
            return None
        
        update_data = mind_map_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(mind_map, field, value)
        
        await self.db.commit()
        await self.db.refresh(mind_map)
        return mind_map

    async def delete_mind_map(self, mind_map_id: UUID, user_id: UUID) -> bool:
        """Delete a mind map.

        Args:
            mind_map_id: Mind map's unique identifier.
            user_id: User's unique identifier.

        Returns:
            True if mind map was deleted, False if not found.
        """
        mind_map = await self.get_mind_map_by_id(mind_map_id, user_id)
        if not mind_map:
            return False

        mind_map.soft_delete()
        await self.db.commit()
        return True

    # Calendar Event operations
    async def get_session_calendar_events(
        self,
        session_id: UUID,
        user_id: UUID,
        event_type: Optional[str] = None,
        upcoming_only: bool = False
    ) -> List[CalendarEvent]:
        """Get calendar events for a planning session.

        Args:
            session_id: Planning session's unique identifier.
            user_id: User's unique identifier.
            event_type: Optional type filter.
            upcoming_only: Whether to return only upcoming events.

        Returns:
            List of calendar events.
        """
        query = select(CalendarEvent).where(
            and_(
                CalendarEvent.planning_session_id == session_id,
                CalendarEvent.user_id == user_id
            )
        )

        if event_type:
            query = query.where(CalendarEvent.event_type == event_type)

        if upcoming_only:
            query = query.where(CalendarEvent.event_date >= datetime.utcnow())

        query = query.order_by(CalendarEvent.event_date)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_calendar_event_by_id(
        self,
        event_id: UUID,
        user_id: UUID
    ) -> Optional[CalendarEvent]:
        """Get a calendar event by ID for a specific user.

        Args:
            event_id: Calendar event's unique identifier.
            user_id: User's unique identifier.

        Returns:
            Calendar event if found, None otherwise.
        """
        result = await self.db.execute(
            select(CalendarEvent).where(
                and_(
                    CalendarEvent.id == event_id,
                    CalendarEvent.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()

    async def create_calendar_event(
        self,
        user_id: UUID,
        event_data: CalendarEventCreate
    ) -> CalendarEvent:
        """Create a new calendar event.

        Args:
            user_id: User's unique identifier.
            event_data: Calendar event creation data.

        Returns:
            Created calendar event object.
        """
        event = CalendarEvent(
            user_id=user_id,
            **event_data.model_dump()
        )

        self.db.add(event)
        await self.db.commit()
        await self.db.refresh(event)
        return event

    async def update_calendar_event(
        self,
        event_id: UUID,
        user_id: UUID,
        event_data: CalendarEventUpdate
    ) -> Optional[CalendarEvent]:
        """Update a calendar event.

        Args:
            event_id: Calendar event's unique identifier.
            user_id: User's unique identifier.
            event_data: Updated calendar event data.

        Returns:
            Updated calendar event object if found, None otherwise.
        """
        event = await self.get_calendar_event_by_id(event_id, user_id)
        if not event:
            return None

        update_data = event_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(event, field, value)

        await self.db.commit()
        await self.db.refresh(event)
        return event

    async def delete_calendar_event(self, event_id: UUID, user_id: UUID) -> bool:
        """Delete a calendar event.

        Args:
            event_id: Calendar event's unique identifier.
            user_id: User's unique identifier.

        Returns:
            True if calendar event was deleted, False if not found.
        """
        event = await self.get_calendar_event_by_id(event_id, user_id)
        if not event:
            return False

        event.soft_delete()
        await self.db.commit()
        return True

    # Dashboard and summary methods
    async def get_planning_dashboard(self, user_id: UUID) -> dict:
        """Get planning dashboard data for a user.

        Args:
            user_id: User's unique identifier.

        Returns:
            Dictionary with dashboard data.
        """
        # Get current session
        current_session = await self.get_current_planning_session(user_id)

        # Get recent sessions (last 3 years)
        current_year = datetime.now().year
        recent_sessions = await self.get_user_planning_sessions(user_id)
        recent_sessions = [s for s in recent_sessions if s.year >= current_year - 2][:5]

        # Get upcoming events
        upcoming_events = []
        if current_session:
            upcoming_events = await self.get_session_calendar_events(
                current_session.id,
                user_id,
                upcoming_only=True
            )
            upcoming_events = upcoming_events[:10]  # Limit to 10 upcoming events

        return {
            "current_session": current_session,
            "recent_sessions": recent_sessions,
            "upcoming_events": upcoming_events,
            "progress_summary": {}  # TODO: Add progress calculation
        }
