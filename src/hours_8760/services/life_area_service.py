"""Life Area service for managing life areas and assessments.

This module provides business logic for life area operations including
CRUD operations for life areas, assessments, and custom metrics.
"""

from typing import List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload

from src.hours_8760.models.life_area import LifeArea, LifeAreaAssessment, LifeAreaMetric
from src.hours_8760.schemas.life_area import (
    LifeAreaCreate,
    LifeAreaUpdate,
    LifeAreaAssessmentCreate,
    LifeAreaAssessmentUpdate,
    LifeAreaMetricCreate,
    LifeAreaMetricUpdate
)


class LifeAreaService:
    """Service class for life area operations."""
    
    def __init__(self, db: AsyncSession):
        """Initialize the life area service.
        
        Args:
            db: Database session.
        """
        self.db = db
    
    # Life Area CRUD operations
    async def get_life_areas(self, active_only: bool = True) -> List[LifeArea]:
        """Get all life areas.
        
        Args:
            active_only: Whether to return only active life areas.
            
        Returns:
            List of life areas.
        """
        query = select(LifeArea).order_by(LifeArea.sort_order, LifeArea.name)
        
        if active_only:
            query = query.where(LifeArea.is_active == True)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_life_area_by_id(self, life_area_id: UUID) -> Optional[LifeArea]:
        """Get a life area by ID.
        
        Args:
            life_area_id: Life area's unique identifier.
            
        Returns:
            Life area object if found, None otherwise.
        """
        result = await self.db.execute(
            select(LifeArea).where(LifeArea.id == life_area_id)
        )
        return result.scalar_one_or_none()
    
    async def create_life_area(self, life_area_data: LifeAreaCreate) -> LifeArea:
        """Create a new life area.
        
        Args:
            life_area_data: Life area creation data.
            
        Returns:
            Created life area object.
        """
        life_area = LifeArea(**life_area_data.model_dump())
        
        self.db.add(life_area)
        await self.db.commit()
        await self.db.refresh(life_area)
        return life_area
    
    async def update_life_area(
        self, 
        life_area_id: UUID, 
        life_area_data: LifeAreaUpdate
    ) -> Optional[LifeArea]:
        """Update a life area.
        
        Args:
            life_area_id: Life area's unique identifier.
            life_area_data: Updated life area data.
            
        Returns:
            Updated life area object if found, None otherwise.
        """
        life_area = await self.get_life_area_by_id(life_area_id)
        if not life_area:
            return None
        
        update_data = life_area_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(life_area, field, value)
        
        await self.db.commit()
        await self.db.refresh(life_area)
        return life_area
    
    async def delete_life_area(self, life_area_id: UUID) -> bool:
        """Soft delete a life area.
        
        Args:
            life_area_id: Life area's unique identifier.
            
        Returns:
            True if life area was deleted, False if not found.
        """
        life_area = await self.get_life_area_by_id(life_area_id)
        if not life_area:
            return False
        
        life_area.soft_delete()
        await self.db.commit()
        return True
    
    # Life Area Assessment operations
    async def get_user_assessments(
        self, 
        user_id: UUID, 
        year: Optional[int] = None,
        quarter: Optional[int] = None
    ) -> List[LifeAreaAssessment]:
        """Get user's life area assessments.
        
        Args:
            user_id: User's unique identifier.
            year: Optional year filter.
            quarter: Optional quarter filter.
            
        Returns:
            List of life area assessments.
        """
        query = select(LifeAreaAssessment).options(
            selectinload(LifeAreaAssessment.life_area)
        ).where(LifeAreaAssessment.user_id == user_id)
        
        if year:
            query = query.where(LifeAreaAssessment.year == year)
        
        if quarter:
            query = query.where(LifeAreaAssessment.quarter == quarter)
        
        query = query.order_by(
            LifeAreaAssessment.year.desc(),
            LifeAreaAssessment.quarter.desc(),
            LifeAreaAssessment.created_at.desc()
        )
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_assessment_by_id(
        self, 
        assessment_id: UUID, 
        user_id: UUID
    ) -> Optional[LifeAreaAssessment]:
        """Get a life area assessment by ID for a specific user.
        
        Args:
            assessment_id: Assessment's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Life area assessment if found, None otherwise.
        """
        result = await self.db.execute(
            select(LifeAreaAssessment)
            .options(selectinload(LifeAreaAssessment.life_area))
            .where(
                and_(
                    LifeAreaAssessment.id == assessment_id,
                    LifeAreaAssessment.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def create_assessment(
        self, 
        user_id: UUID, 
        assessment_data: LifeAreaAssessmentCreate
    ) -> LifeAreaAssessment:
        """Create a new life area assessment.
        
        Args:
            user_id: User's unique identifier.
            assessment_data: Assessment creation data.
            
        Returns:
            Created assessment object.
            
        Raises:
            ValueError: If assessment already exists for the same user, life area, year, and quarter.
        """
        # Check if assessment already exists
        existing = await self.db.execute(
            select(LifeAreaAssessment).where(
                and_(
                    LifeAreaAssessment.user_id == user_id,
                    LifeAreaAssessment.life_area_id == assessment_data.life_area_id,
                    LifeAreaAssessment.year == assessment_data.year,
                    LifeAreaAssessment.quarter == assessment_data.quarter
                )
            )
        )
        
        if existing.scalar_one_or_none():
            raise ValueError("Assessment already exists for this life area, year, and quarter")
        
        assessment = LifeAreaAssessment(
            user_id=user_id,
            **assessment_data.model_dump()
        )
        
        self.db.add(assessment)
        await self.db.commit()
        await self.db.refresh(assessment)
        
        # Load the life area relationship
        await self.db.refresh(assessment, ["life_area"])
        return assessment
    
    async def update_assessment(
        self, 
        assessment_id: UUID, 
        user_id: UUID, 
        assessment_data: LifeAreaAssessmentUpdate
    ) -> Optional[LifeAreaAssessment]:
        """Update a life area assessment.
        
        Args:
            assessment_id: Assessment's unique identifier.
            user_id: User's unique identifier.
            assessment_data: Updated assessment data.
            
        Returns:
            Updated assessment object if found, None otherwise.
        """
        assessment = await self.get_assessment_by_id(assessment_id, user_id)
        if not assessment:
            return None
        
        update_data = assessment_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(assessment, field, value)
        
        await self.db.commit()
        await self.db.refresh(assessment)
        return assessment
    
    async def delete_assessment(self, assessment_id: UUID, user_id: UUID) -> bool:
        """Delete a life area assessment.
        
        Args:
            assessment_id: Assessment's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            True if assessment was deleted, False if not found.
        """
        assessment = await self.get_assessment_by_id(assessment_id, user_id)
        if not assessment:
            return False
        
        assessment.soft_delete()
        await self.db.commit()
        return True
    
    # Life Area Metrics operations
    async def get_user_metrics(
        self, 
        user_id: UUID, 
        life_area_id: Optional[UUID] = None,
        active_only: bool = True
    ) -> List[LifeAreaMetric]:
        """Get user's life area metrics.
        
        Args:
            user_id: User's unique identifier.
            life_area_id: Optional life area filter.
            active_only: Whether to return only active metrics.
            
        Returns:
            List of life area metrics.
        """
        query = select(LifeAreaMetric).options(
            selectinload(LifeAreaMetric.life_area)
        ).where(LifeAreaMetric.user_id == user_id)
        
        if life_area_id:
            query = query.where(LifeAreaMetric.life_area_id == life_area_id)
        
        if active_only:
            query = query.where(LifeAreaMetric.is_active == True)
        
        query = query.order_by(LifeAreaMetric.metric_name)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_metric_by_id(
        self, 
        metric_id: UUID, 
        user_id: UUID
    ) -> Optional[LifeAreaMetric]:
        """Get a life area metric by ID for a specific user.
        
        Args:
            metric_id: Metric's unique identifier.
            user_id: User's unique identifier.
            
        Returns:
            Life area metric if found, None otherwise.
        """
        result = await self.db.execute(
            select(LifeAreaMetric)
            .options(selectinload(LifeAreaMetric.life_area))
            .where(
                and_(
                    LifeAreaMetric.id == metric_id,
                    LifeAreaMetric.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def create_metric(
        self, 
        user_id: UUID, 
        metric_data: LifeAreaMetricCreate
    ) -> LifeAreaMetric:
        """Create a new life area metric.
        
        Args:
            user_id: User's unique identifier.
            metric_data: Metric creation data.
            
        Returns:
            Created metric object.
        """
        metric = LifeAreaMetric(
            user_id=user_id,
            **metric_data.model_dump()
        )
        
        self.db.add(metric)
        await self.db.commit()
        await self.db.refresh(metric)
        
        # Load the life area relationship
        await self.db.refresh(metric, ["life_area"])
        return metric

    async def update_metric(
        self,
        metric_id: UUID,
        user_id: UUID,
        metric_data: LifeAreaMetricUpdate
    ) -> Optional[LifeAreaMetric]:
        """Update a life area metric.

        Args:
            metric_id: Metric's unique identifier.
            user_id: User's unique identifier.
            metric_data: Updated metric data.

        Returns:
            Updated metric object if found, None otherwise.
        """
        metric = await self.get_metric_by_id(metric_id, user_id)
        if not metric:
            return None

        update_data = metric_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(metric, field, value)

        await self.db.commit()
        await self.db.refresh(metric)
        return metric

    async def delete_metric(self, metric_id: UUID, user_id: UUID) -> bool:
        """Delete a life area metric.

        Args:
            metric_id: Metric's unique identifier.
            user_id: User's unique identifier.

        Returns:
            True if metric was deleted, False if not found.
        """
        metric = await self.get_metric_by_id(metric_id, user_id)
        if not metric:
            return False

        metric.soft_delete()
        await self.db.commit()
        return True

    # Assessment analytics and summary methods
    async def get_assessment_overview(self, user_id: UUID, year: int) -> dict:
        """Get assessment overview for a user and year.

        Args:
            user_id: User's unique identifier.
            year: Year for the overview.

        Returns:
            Dictionary with assessment overview data.
        """
        # Get all life areas
        life_areas = await self.get_life_areas()
        total_areas = len(life_areas)

        # Get assessments for the year
        assessments = await self.get_user_assessments(user_id, year)
        assessed_areas = len(set(a.life_area_id for a in assessments))

        # Calculate average rating
        ratings = [a.overall_rating for a in assessments if a.overall_rating is not None]
        average_rating = sum(ratings) / len(ratings) if ratings else None

        # Calculate completion percentage
        completion_percentage = (assessed_areas / total_areas * 100) if total_areas > 0 else 0

        return {
            "year": year,
            "total_areas": total_areas,
            "assessed_areas": assessed_areas,
            "average_rating": average_rating,
            "completion_percentage": completion_percentage,
            "assessments": assessments
        }
