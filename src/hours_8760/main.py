"""Main FastAPI application.

This module creates and configures the FastAPI application with
middleware, routers, and other components.
"""

from contextlib import asynccontextmanager
from typing import Async<PERSON><PERSON>ator

import structlog
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from src.hours_8760.api.v1.router import api_router
from src.hours_8760.core.config import settings
from src.hours_8760.core.database import create_tables
from src.hours_8760.core.cache import cache_service
from src.hours_8760.core.performance import PerformanceMiddleware, get_health_status
from src.hours_8760.core.security_middleware import SecurityMiddleware
from src.hours_8760.core.rate_limiting import rate_limit_middleware


# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    logger.info("Starting up application", environment=settings.ENVIRONMENT)

    # Initialize cache service
    await cache_service.connect()
    logger.info("Cache service connected")

    # Create database tables if in development
    if settings.is_development:
        await create_tables()
        logger.info("Database tables created")

    yield

    # Shutdown
    logger.info("Shutting down application")

    # Disconnect cache service
    await cache_service.disconnect()
    logger.info("Cache service disconnected")


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.API_VERSION,
    description="8,760 Hours - Life Planning Platform: A comprehensive digital life planning platform using the proven 8,760 Hours methodology",
    openapi_url=f"{settings.API_PREFIX}/openapi.json" if settings.ENABLE_DOCS else None,
    docs_url=f"{settings.API_PREFIX}/docs" if settings.ENABLE_DOCS else None,
    redoc_url=f"{settings.API_PREFIX}/redoc" if settings.ENABLE_REDOC else None,
    lifespan=lifespan,
)

# Add CORS middleware
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Add trusted host middleware
if settings.ALLOWED_HOSTS:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )

# Add security middleware
app.add_middleware(SecurityMiddleware)

# Add performance monitoring middleware
app.add_middleware(PerformanceMiddleware)

# Add rate limiting middleware
app.middleware("http")(rate_limit_middleware)

# Include API router
app.include_router(api_router)


@app.get("/", tags=["root"])
async def root():
    """Root endpoint."""
    return {
        "message": f"Welcome to {settings.PROJECT_NAME}",
        "version": settings.API_VERSION,
        "docs_url": f"{settings.API_PREFIX}/docs" if settings.ENABLE_DOCS else None,
    }


@app.get("/health", tags=["monitoring"])
async def health_check():
    """Health check endpoint."""
    return await get_health_status()


@app.get("/metrics", tags=["monitoring"])
async def metrics():
    """Performance metrics endpoint."""
    from src.hours_8760.core.performance import performance_monitor
    return performance_monitor.get_all_stats()
