version: '3.8'

services:
  # FastAPI Backend Service
  api:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: 8760-hours-api
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD:-postgres}@database:5432/${POSTGRES_DB:-8760hours}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-jwt-secret-key-change-in-production}
      - DEBUG=false
      - LOG_LEVEL=INFO
    networks:
      - traefik_network
      - 8760-network
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik_network"
      - "traefik.http.routers.api8760.rule=Host(`api.8760.localhost`)"
      - "traefik.http.services.api8760.loadbalancer.server.port=8000"
      - "traefik.http.routers.api8760.entrypoints=web"
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Database Migrations Service
  migrations:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: 8760-hours-migrations
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD:-postgres}@database:5432/${POSTGRES_DB:-8760hours}
    networks:
      - 8760-network
    depends_on:
      database:
        condition: service_healthy
    command: ["python", "-m", "alembic", "upgrade", "head"]
    restart: "no"

  # Task Scheduler (for background tasks)
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: 8760-hours-scheduler
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD:-postgres}@database:5432/${POSTGRES_DB:-8760hours}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
    networks:
      - 8760-network
    depends_on:
      - api
      - redis
    command: ["python", "-m", "src.hours_8760.scheduler"]
    restart: unless-stopped

  # Monitoring - Prometheus Metrics Exporter
  metrics:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: 8760-hours-metrics
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD:-postgres}@database:5432/${POSTGRES_DB:-8760hours}
      - REDIS_URL=redis://redis:6379/0
    networks:
      - 8760-network
      - traefik_network
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik_network"
      - "traefik.http.routers.metrics8760.rule=Host(`metrics.8760.localhost`)"
      - "traefik.http.services.metrics8760.loadbalancer.server.port=8001"
      - "traefik.http.routers.metrics8760.entrypoints=web"
    depends_on:
      - api
    command: ["python", "-m", "src.hours_8760.metrics_server"]
    restart: unless-stopped

networks:
  8760-network:
    external: true
  traefik_network:
    external: true
