(()=>{var e={};e.id=9566,e.ids=[9566],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19616:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(50482),a=r(69108),n=r(62563),o=r.n(n),i=r(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let d=["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68427)),"/mnt/persist/workspace/frontend/src/app/(auth)/register/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84295)),"/mnt/persist/workspace/frontend/src/app/(auth)/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/(auth)/register/page.tsx"],m="/(auth)/register/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81790:(e,s,r)=>{Promise.resolve().then(r.bind(r,44377))},44377:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t=r(95344),a=r(3729),n=r(22254),o=r(20783),i=r.n(o),l=r(60708),d=r(85453),c=r(3389),m=r(25545),u=r(5094),p=r(23673),x=r(46540),g=r(60339),h=r(41460),f=r(33468);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let w=c.z.object({email:c.z.string().email("Please enter a valid email address"),password:c.z.string().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number"),confirmPassword:c.z.string(),firstName:c.z.string().min(1,"First name is required"),lastName:c.z.string().min(1,"Last name is required")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function j(){let[e,s]=(0,a.useState)(!1),[r,o]=(0,a.useState)(!1),c=(0,n.useRouter)(),{toast:j}=(0,g.pm)(),{showSuccess:y,showError:b}=(0,h.z)(),{register:N,isLoading:v}=(0,f.t)(),{register:P,handleSubmit:_,watch:q,formState:{errors:k}}=(0,l.cI)({resolver:(0,d.F)(w)}),C=q("password"),z=(e=>{if(!e)return{score:0,label:"",color:""};let s=0;return e.length>=8&&s++,/[A-Z]/.test(e)&&s++,/[a-z]/.test(e)&&s++,/[0-9]/.test(e)&&s++,/[^A-Za-z0-9]/.test(e)&&s++,{score:s,label:["Very Weak","Weak","Fair","Good","Strong"][Math.min(s-1,4)]||"",color:["bg-red-500","bg-orange-500","bg-yellow-500","bg-blue-500","bg-green-500"][Math.min(s-1,4)]||"bg-gray-300"}})(C||""),E=async e=>{try{await N({email:e.email,password:e.password,firstName:e.firstName,lastName:e.lastName}),y("Welcome to 8,760 Hours!","Your account has been created successfully."),c.push("/onboarding")}catch(s){let e="Registration failed. Please try again.";s instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())&&(s.isValidationError?e=s.message:s.isNetworkError&&(e="Network error. Please check your connection.")),b("Registration Failed",e)}};return t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background px-4 py-8",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-2 text-2xl font-bold",children:[t.jsx(m.Z,{className:"h-8 w-8"}),t.jsx("span",{children:"8,760 Hours"})]}),t.jsx("h1",{className:"mt-6 text-3xl font-bold tracking-tight",children:"Create your account"}),t.jsx("p",{className:"mt-2 text-sm text-muted-foreground",children:"Start your systematic life planning journey today"})]}),(0,t.jsxs)(p.Zb,{children:[(0,t.jsxs)(p.Ol,{children:[t.jsx(p.ll,{children:"Sign Up"}),t.jsx(p.SZ,{children:"Create your account to begin planning your best life"})]}),(0,t.jsxs)(p.aY,{children:[(0,t.jsxs)("form",{onSubmit:_(E),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[t.jsx(x.I,{...P("firstName"),label:"First Name",placeholder:"John",autoComplete:"given-name",error:k.firstName?.message,"data-testid":"firstName",required:!0}),t.jsx(x.I,{...P("lastName"),label:"Last Name",placeholder:"Doe",autoComplete:"family-name",error:k.lastName?.message,"data-testid":"lastName",required:!0})]}),t.jsx(x.I,{...P("email"),type:"email",label:"Email",placeholder:"<EMAIL>",autoComplete:"email",error:k.email?.message,"data-testid":"email",required:!0}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(x.I,{...P("password"),type:"password",label:"Password",placeholder:"Create a strong password",autoComplete:"new-password",error:k.password?.message,"data-testid":"password",required:!0}),C&&t.jsx("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"flex-1 h-2 bg-gray-200 rounded-full overflow-hidden",children:t.jsx("div",{className:`h-full transition-all duration-300 ${z.color}`,style:{width:`${z.score/5*100}%`}})}),t.jsx("span",{className:"text-xs text-muted-foreground",children:z.label})]})})]}),t.jsx(x.I,{...P("confirmPassword"),type:"password",label:"Confirm Password",placeholder:"Confirm your password",autoComplete:"new-password",error:k.confirmPassword?.message,"data-testid":"confirmPassword",required:!0}),t.jsx(u.z,{type:"submit",className:"w-full",loading:v,"data-testid":"register-button",children:v?"Creating account...":"Create Account"})]}),(0,t.jsxs)("div",{className:"relative my-6",children:[t.jsx("div",{className:"absolute inset-0 flex items-center",children:t.jsx("span",{className:"w-full border-t"})}),t.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:t.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"Or"})})]}),t.jsx("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Already have an account?"," ",t.jsx(i(),{href:"/login",className:"font-medium text-primary hover:underline",children:"Sign in"})]})})]})]}),t.jsx("div",{className:"text-center text-xs text-muted-foreground",children:(0,t.jsxs)("p",{children:["By creating an account, you agree to our"," ",t.jsx(i(),{href:"/terms",className:"hover:underline",children:"Terms of Service"})," ","and"," ",t.jsx(i(),{href:"/privacy",className:"hover:underline",children:"Privacy Policy"})]})})]})})}},68427:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>o});let t=(0,r(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(auth)/register/page.tsx`),{__esModule:a,$$typeof:n}=t,o=t.default}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[1638,8356,3828,1476,2372,28,2778],()=>r(19616));module.exports=t})();