(()=>{var e={};e.id=4665,e.ids=[4665],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},53409:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(50482),n=t(69108),a=t(62563),i=t.n(a),o=t(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c=["",{children:["(auth)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52169)),"/mnt/persist/workspace/frontend/src/app/(auth)/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,84295)),"/mnt/persist/workspace/frontend/src/app/(auth)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/(auth)/login/page.tsx"],u="/(auth)/login/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/(auth)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36248:(e,r,t)=>{Promise.resolve().then(t.bind(t,3473))},3473:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var s=t(95344),n=t(3729),a=t(22254),i=t(20783),o=t.n(i),l=t(60708),c=t(85453),d=t(3389),u=t(25545),p=t(5094),m=t(23673),x=t(46540),h=t(60339),g=t(41460),f=t(33468);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let j=d.z.object({email:d.z.string().email("Please enter a valid email address"),password:d.z.string().min(1,"Password is required")});function y(){let[e,r]=(0,n.useState)(!1),t=(0,a.useRouter)(),{toast:i}=(0,h.pm)(),{showSuccess:d,showError:y}=(0,g.z)(),{login:v,isLoading:b}=(0,f.t)(),{register:w,handleSubmit:N,formState:{errors:P}}=(0,l.cI)({resolver:(0,c.F)(j)}),_=async e=>{try{await v(e),d("Welcome back!","You have been successfully logged in."),t.push("/dashboard")}catch(r){let e="Login failed. Please try again.";r instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())&&(r.isAuthError?e="Invalid email or password.":r.isValidationError?e=r.message:r.isNetworkError&&(e="Network error. Please check your connection.")),y("Login Failed",e)}};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background px-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)(o(),{href:"/",className:"inline-flex items-center space-x-2 text-2xl font-bold",children:[s.jsx(u.Z,{className:"h-8 w-8"}),s.jsx("span",{children:"8,760 Hours"})]}),s.jsx("h1",{className:"mt-6 text-3xl font-bold tracking-tight",children:"Welcome back"}),s.jsx("p",{className:"mt-2 text-sm text-muted-foreground",children:"Sign in to your account to continue your life planning journey"})]}),(0,s.jsxs)(m.Zb,{children:[(0,s.jsxs)(m.Ol,{children:[s.jsx(m.ll,{children:"Sign In"}),s.jsx(m.SZ,{children:"Enter your email and password to access your account"})]}),(0,s.jsxs)(m.aY,{children:[(0,s.jsxs)("form",{onSubmit:N(_),className:"space-y-4",children:[s.jsx(x.I,{...w("email"),type:"email",label:"Email",placeholder:"Enter your email",autoComplete:"email",error:P.email?.message,"data-testid":"email",required:!0}),s.jsx(x.I,{...w("password"),type:"password",label:"Password",placeholder:"Enter your password",autoComplete:"current-password",error:P.password?.message,"data-testid":"password",required:!0}),s.jsx("div",{className:"flex justify-end",children:s.jsx(o(),{href:"/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot your password?"})}),s.jsx(p.z,{type:"submit",className:"w-full",loading:b,"data-testid":"login-button",children:b?"Signing in...":"Sign In"})]}),(0,s.jsxs)("div",{className:"relative my-6",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("span",{className:"w-full border-t"})}),s.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:s.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"Or"})})]}),s.jsx("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",s.jsx(o(),{href:"/register",className:"font-medium text-primary hover:underline",children:"Sign up for free"})]})})]})]}),s.jsx("div",{className:"text-center text-xs text-muted-foreground",children:(0,s.jsxs)("p",{children:["By signing in, you agree to our"," ",s.jsx(o(),{href:"/terms",className:"hover:underline",children:"Terms of Service"})," ","and"," ",s.jsx(o(),{href:"/privacy",className:"hover:underline",children:"Privacy Policy"})]})})]})})}},52169:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let s=(0,t(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(auth)/login/page.tsx`),{__esModule:n,$$typeof:a}=s,i=s.default}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,8356,3828,1476,2372,28,2778],()=>t(53409));module.exports=s})();