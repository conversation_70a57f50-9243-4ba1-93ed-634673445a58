(()=>{var e={};e.id=5688,e.ids=[5688],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23093:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(50482),a=t(69108),n=t(62563),i=t.n(n),l=t(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["(dashboard)",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,57069)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,95021)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/(dashboard)/profile/page.tsx"],u="/(dashboard)/profile/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13660:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},61113:(e,s,t)=>{Promise.resolve().then(t.bind(t,37523))},35303:()=>{},66138:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},37523:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(95344),a=t(3729),n=t(22254),i=t(23673),l=t(5094),o=t(46540),d=t(33668),c=t(2690),u=t(12772),m=t(19591),x=t(50909),f=t(60339),h=t(33468),p=t(25545),j=t(31498),g=t(75695),v=t(18822),b=t(13746),N=t(33037),y=t(70009),w=t(51765),_=t(17910),O=t(46064),C=t(55794),E=t(23485);function k(){let{user:e,isAuthenticated:s,isLoading:t}=(0,h.t)(),k=(0,n.useRouter)(),{toast:S}=(0,f.pm)(),[T,D]=(0,a.useState)(!1),[P,M]=(0,a.useState)(!1),[A,U]=(0,a.useState)({firstName:"",lastName:"",email:"",phone:"",location:"",timezone:"UTC",bio:"",website:"",joinedDate:new Date,preferences:{emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,darkMode:!0,language:"en"},stats:{assessmentsCompleted:0,goalsSet:0,progressEntries:0,daysActive:0}});(0,a.useEffect)(()=>{t||s||k.push("/login")},[s,t,k]),(0,a.useEffect)(()=>{e&&U(s=>({...s,firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",joinedDate:e.createdAt?new Date(e.createdAt):new Date}))},[e]);let Z=async()=>{M(!0);try{await new Promise(e=>setTimeout(e,1e3)),S({title:"Profile Updated",description:"Your profile has been saved successfully."}),D(!1)}catch(e){S({title:"Save Failed",description:"Failed to save your profile. Please try again.",variant:"destructive"})}finally{M(!1)}},F=(e,s)=>{U(t=>({...t,[e]:s}))},R=(e,s)=>{U(t=>({...t,preferences:{...t.preferences,[e]:s}}))};return t?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):s&&e?(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[r.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(p.Z,{className:"h-6 w-6"}),r.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("span",{className:"text-sm text-muted-foreground",children:e.firstName||e.email}),r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>h.t.getState().logout(),children:"Sign Out"})]})]})}),r.jsx("main",{className:"container py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Profile"}),r.jsx("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences"})]}),r.jsx("div",{className:"flex items-center space-x-2",children:T?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.z,{variant:"outline",onClick:()=>D(!1),disabled:P,children:"Cancel"}),(0,r.jsxs)(l.z,{onClick:Z,disabled:P,children:[r.jsx(j.Z,{className:"mr-2 h-4 w-4"}),P?"Saving...":"Save Changes"]})]}):(0,r.jsxs)(l.z,{onClick:()=>D(!0),children:[r.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Edit Profile"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(v.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Basic Information"})]}),r.jsx(i.SZ,{children:"Your personal details and contact information"})]}),(0,r.jsxs)(i.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"firstName",children:"First Name"}),r.jsx(o.I,{id:"firstName",value:A.firstName,onChange:e=>F("firstName",e.target.value),disabled:!T})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"lastName",children:"Last Name"}),r.jsx(o.I,{id:"lastName",value:A.lastName,onChange:e=>F("lastName",e.target.value),disabled:!T})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"email",children:"Email"}),r.jsx(o.I,{id:"email",type:"email",value:A.email,onChange:e=>F("email",e.target.value),disabled:!T})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"phone",children:"Phone"}),r.jsx(o.I,{id:"phone",value:A.phone,onChange:e=>F("phone",e.target.value),disabled:!T,placeholder:"+****************"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"location",children:"Location"}),r.jsx(o.I,{id:"location",value:A.location,onChange:e=>F("location",e.target.value),disabled:!T,placeholder:"City, Country"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"bio",children:"Bio"}),r.jsx(c.g,{id:"bio",value:A.bio,onChange:e=>F("bio",e.target.value),disabled:!T,placeholder:"Tell us about yourself...",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"website",children:"Website"}),r.jsx(o.I,{id:"website",value:A.website,onChange:e=>F("website",e.target.value),disabled:!T,placeholder:"https://yourwebsite.com"})]})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(b.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Preferences"})]}),r.jsx(i.SZ,{children:"Customize your experience and notification settings"})]}),(0,r.jsxs)(i.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium flex items-center space-x-2",children:[r.jsx(N.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Notifications"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(d._,{children:"Email Notifications"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive updates and reminders via email"})]}),r.jsx(u.r,{checked:A.preferences.emailNotifications,onCheckedChange:e=>R("emailNotifications",e),disabled:!T})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(d._,{children:"Push Notifications"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Get instant notifications in your browser"})]}),r.jsx(u.r,{checked:A.preferences.pushNotifications,onCheckedChange:e=>R("pushNotifications",e),disabled:!T})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(d._,{children:"Weekly Reports"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive weekly progress summaries"})]}),r.jsx(u.r,{checked:A.preferences.weeklyReports,onCheckedChange:e=>R("weeklyReports",e),disabled:!T})]})]})]}),r.jsx(x.Z,{}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Appearance"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(d._,{children:"Dark Mode"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Use dark theme for better eye comfort"})]}),r.jsx(u.r,{checked:A.preferences.darkMode,onCheckedChange:e=>R("darkMode",e),disabled:!T})]})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"h-24 w-24 rounded-full bg-primary flex items-center justify-center",children:r.jsx("span",{className:"text-2xl font-bold text-primary-foreground",children:(A.firstName?.[0]||e.email?.[0]||"U").toUpperCase()})}),T&&r.jsx(l.z,{size:"sm",variant:"outline",className:"absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0",children:r.jsx(w.Z,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h3",{className:"font-semibold",children:A.firstName&&A.lastName?`${A.firstName} ${A.lastName}`:e.email}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Member since ",A.joinedDate.toLocaleDateString()]})]})]})})}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{className:"text-lg",children:"Your Progress"}),r.jsx(i.SZ,{children:"Overview of your life planning journey"})]}),r.jsx(i.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg",children:[r.jsx(_.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:A.stats.assessmentsCompleted}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Assessments"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg",children:[r.jsx(O.Z,{className:"h-6 w-6 text-green-600 dark:text-green-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:A.stats.goalsSet}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Goals Set"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg",children:[r.jsx(p.Z,{className:"h-6 w-6 text-purple-600 dark:text-purple-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:A.stats.progressEntries}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Progress Entries"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg",children:[r.jsx(C.Z,{className:"h-6 w-6 text-orange-600 dark:text-orange-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:A.stats.daysActive}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Days Active"})]})]})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"text-lg flex items-center space-x-2",children:[r.jsx(E.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Account Status"})]})}),(0,r.jsxs)(i.aY,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm",children:"Account Type"}),r.jsx(m.C,{variant:"default",children:"Free"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm",children:"Email Verified"}),r.jsx(m.C,{variant:"outline",className:"text-green-600 border-green-600",children:"Verified"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm",children:"Two-Factor Auth"}),r.jsx(m.C,{variant:"outline",className:"text-orange-600 border-orange-600",children:"Not Enabled"})]}),r.jsx(x.Z,{}),r.jsx(l.z,{variant:"outline",size:"sm",className:"w-full",children:"Upgrade to Pro"})]})]})]})]})]})})]}):null}},19591:(e,s,t)=>{"use strict";t.d(s,{C:()=>i});var r=t(95344);t(3729);var a=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...t}){return r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(n({variant:s}),e),...t})}},5094:(e,s,t)=>{"use strict";t.d(s,{z:()=>d});var r=t(95344),a=t(3729),n=t(32751),i=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var l=t(42739);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,loading:i=!1,leftIcon:d,rightIcon:c,children:u,disabled:m,...x},f)=>{let h=a?n.g7:"button",p=m||i;return(0,r.jsxs)(h,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o({variant:s,size:t,className:e})),ref:f,disabled:p,"aria-disabled":p,...x,children:[i&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!i&&d&&r.jsx("span",{className:"mr-2",children:d}),u,!i&&c&&r.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>i,SZ:()=>o,Zb:()=>n,aY:()=>d,ll:()=>l});var r=t(95344),a=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let i=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...s}));l.displayName="CardTitle";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...s}));d.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},60339:(e,s,t)=>{"use strict";t.d(s,{pm:()=>m});var r=t(3729);let a=0,n=new Map,i=e=>{if(n.has(e))return;let s=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,s)},l=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?i(t):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},o=[],d={toasts:[]};function c(e){d=l(d,e),o.forEach(e=>{e(d)})}function u({...e}){let s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...e,id:s,open:!0,onOpenChange:e=>{e||t()}}}),{id:s,dismiss:t,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function m(){let[e,s]=r.useState(d);return r.useEffect(()=>(o.push(s),()=>{let e=o.indexOf(s);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},95021:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>a});var r=t(25036);let a={title:"Dashboard",description:"Your life planning dashboard - track progress and manage goals"};function n({children:e}){return r.jsx(r.Fragment,{children:e})}},57069:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(dashboard)/profile/page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default},21342:(e,s,t)=>{"use strict";function r({children:e}){return e}t.r(s),t.d(s,{default:()=>r})},85222:(e,s,t)=>{"use strict";function r(e,s,{checkForDefaultPrevented:t=!0}={}){return function(r){if(e?.(r),!1===t||!r.defaultPrevented)return s?.(r)}}t.d(s,{M:()=>r})},98462:(e,s,t)=>{"use strict";t.d(s,{b:()=>i,k:()=>n});var r=t(3729),a=t(95344);function n(e,s){let t=r.createContext(s),n=e=>{let{children:s,...n}=e,i=r.useMemo(()=>n,Object.values(n));return(0,a.jsx)(t.Provider,{value:i,children:s})};return n.displayName=e+"Provider",[n,function(a){let n=r.useContext(t);if(n)return n;if(void 0!==s)return s;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function i(e,s=[]){let t=[],n=()=>{let s=t.map(e=>r.createContext(e));return function(t){let a=t?.[e]||s;return r.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return n.scopeName=e,[function(s,n){let i=r.createContext(n),l=t.length;t=[...t,n];let o=s=>{let{scope:t,children:n,...o}=s,d=t?.[e]?.[l]||i,c=r.useMemo(()=>o,Object.values(o));return(0,a.jsx)(d.Provider,{value:c,children:n})};return o.displayName=s+"Provider",[o,function(t,a){let o=a?.[e]?.[l]||i,d=r.useContext(o);if(d)return d;if(void 0!==n)return n;throw Error(`\`${t}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((s,{useScope:t,scopeName:r})=>{let a=t(e)[`__scope${r}`];return{...s,...a}},{});return r.useMemo(()=>({[`__scope${s.scopeName}`]:a}),[a])}};return t.scopeName=s.scopeName,t}(n,...s)]}},62409:(e,s,t)=>{"use strict";t.d(s,{WV:()=>l,jH:()=>o});var r=t(3729),a=t(81202),n=t(32751),i=t(95344),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,n.Z8)(`Primitive.${s}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e,l=a?t:s;return(0,i.jsx)(l,{...n,ref:r})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function o(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}},33183:(e,s,t)=>{"use strict";t.d(s,{T:()=>l});var r,a=t(3729),n=t(16069),i=(r||(r=t.t(a,2)))[" useInsertionEffect ".trim().toString()]||n.b;function l({prop:e,defaultProp:s,onChange:t=()=>{},caller:r}){let[n,l,o]=function({defaultProp:e,onChange:s}){let[t,r]=a.useState(e),n=a.useRef(t),l=a.useRef(s);return i(()=>{l.current=s},[s]),a.useEffect(()=>{n.current!==t&&(l.current?.(t),n.current=t)},[t,n]),[t,r,l]}({defaultProp:s,onChange:t}),d=void 0!==e,c=d?e:n;{let s=a.useRef(void 0!==e);a.useEffect(()=>{s.current,s.current=d},[d,r])}return[c,a.useCallback(s=>{if(d){let t="function"==typeof s?s(e):s;t!==e&&o.current?.(t)}else l(s)},[d,e,l,o])]}Symbol("RADIX:SYNC_STATE")},16069:(e,s,t)=>{"use strict";t.d(s,{b:()=>a});var r=t(3729),a=globalThis?.document?r.useLayoutEffect:()=>{}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,8356,3828,2372,12],()=>t(23093));module.exports=r})();