(()=>{var e={};e.id=6903,e.ids=[6903],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78646:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(50482),n=r(69108),a=r(62563),i=r.n(a),o=r(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["(dashboard)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1463)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,95021)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/(dashboard)/dashboard/page.tsx"],u="/(dashboard)/dashboard/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/(dashboard)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13660:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},16763:(e,t,r)=>{Promise.resolve().then(r.bind(r,89594))},35303:()=>{},66138:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},50340:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55794:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},7060:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},17910:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},89594:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(95344),n=r(3729),a=r(22254),i=r(23673),o=r(5094),l=r(33468),d=r(25545),c=r(55794),u=r(50340),m=r(46064),x=r(17910),h=r(7060),f=r(66138);function p(){let{user:e,isAuthenticated:t,isLoading:r}=(0,l.t)(),p=(0,a.useRouter)();return((0,n.useEffect)(()=>{r||t||p.push("/login")},[t,r,p]),r)?s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):t&&e?(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[s.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(d.Z,{className:"h-6 w-6"}),s.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Welcome back, ",e.firstName||e.email]}),s.jsx(o.z,{variant:"outline",size:"sm",onClick:()=>l.t.getState().logout(),children:"Sign Out"})]})]})}),s.jsx("main",{className:"container py-8",children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Welcome to Your Life Planning Dashboard"}),s.jsx("p",{className:"text-muted-foreground",children:"Transform your life with systematic planning across all 12 life areas."})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(i.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>p.push("/planning"),children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Start Planning Session"}),s.jsx(c.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:"2025"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Begin your annual life review"})]})]}),(0,s.jsxs)(i.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>p.push("/assessment"),children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Assess Life Areas"}),s.jsx(u.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:"0/12"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Areas assessed this year"})]})]}),(0,s.jsxs)(i.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>p.push("/progress"),children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Track Progress"}),s.jsx(m.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:"0"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Progress entries this week"})]})]}),(0,s.jsxs)(i.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Set Goals"}),s.jsx(x.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:"0"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Major goals for 2025"})]})]}),(0,s.jsxs)(i.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Track Progress"}),s.jsx(m.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:"0%"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Average completion"})]})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[(0,s.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[s.jsx(h.Z,{className:"h-5 w-5 text-green-500"}),s.jsx("span",{children:"Getting Started"})]}),s.jsx(i.SZ,{children:"Follow these steps to begin your life planning journey"})]}),s.jsx(i.aY,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"1"}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium",children:"Learn the 12 Life Areas"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Understand the framework that will guide your planning"})]}),s.jsx(o.z,{variant:"outline",size:"sm",children:"Learn More"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-muted text-muted-foreground text-sm font-medium",children:"2"}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium",children:"Assess Your Current State"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Rate yourself across all 12 life areas"})]}),s.jsx(o.z,{variant:"outline",size:"sm",onClick:()=>p.push("/planning"),children:"Start Planning"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-muted text-muted-foreground text-sm font-medium",children:"3"}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium",children:"Create Your Planning Session"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Begin your structured annual planning process"})]}),s.jsx(o.z,{variant:"outline",size:"sm",children:"Create Session"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-muted text-muted-foreground text-sm font-medium",children:"4"}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium",children:"Set Your Major Goals"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Define 3-5 major goals for the year"})]}),s.jsx(o.z,{variant:"outline",size:"sm",children:"Set Goals"})]})]})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Recent Activity"}),s.jsx(i.SZ,{children:"Your latest actions and updates"})]}),s.jsx(i.aY,{children:s.jsx("div",{className:"flex items-center justify-center py-8 text-muted-foreground",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx(f.Z,{className:"h-8 w-8 mx-auto"}),s.jsx("p",{children:"No recent activity"}),s.jsx("p",{className:"text-sm",children:"Start by creating your first planning session"})]})})})]})]})})]}):null}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(95344),n=r(3729),a=r(32751),i=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var o=r(42739);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,loading:i=!1,leftIcon:d,rightIcon:c,children:u,disabled:m,...x},h)=>{let f=n?a.g7:"button",p=m||i;return(0,s.jsxs)(f,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l({variant:t,size:r,className:e})),ref:h,disabled:p,"aria-disabled":p,...x,children:[i&&s.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!i&&d&&s.jsx("span",{className:"mr-2",children:d}),u,!i&&c&&s.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>i,SZ:()=>l,Zb:()=>a,aY:()=>d,ll:()=>o});var s=r(95344),n=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let a=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));a.displayName="Card";let i=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let o=n.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let l=n.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let d=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...t}));d.displayName="CardContent",n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},33468:(e,t,r)=>{"use strict";r.d(t,{t:()=>a});var s=r(43158),n=r(67023);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let a=(0,s.Ue)()((0,n.tJ)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.login(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Login failed. Please try again."}),t}},register:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.register(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Registration failed. Please try again."}),t}},logout:async()=>{e({isLoading:!0});try{await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.logout()}catch(e){}finally{e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}},refreshToken:async()=>{let{token:r}=t();if(!r)throw Error("No token available for refresh");try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.refreshToken();e({user:t.user,token:t.accessToken,isAuthenticated:!0,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,error:"Session expired. Please login again."}),t}},updateProfile:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.updateProfile(t);e({user:r,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Profile update failed. Please try again."}),t}},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},initialize:async()=>{let{token:r}=t();if(r){e({isLoading:!0});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.me();e({user:t,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}}}}),{name:"auth-storage",partialize:e=>({token:e.token,user:e.user}),onRehydrateStorage:()=>e=>{e?.token&&Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e.token)}})),i=null,o=()=>{let{token:e,refreshToken:t,logout:r}=a.getState();if(i&&clearTimeout(i),e)try{let s=JSON.parse(atob(e.split(".")[1])),n=1e3*s.exp,a=Date.now();i=setTimeout(async()=>{try{await t(),o()}catch(e){await r()}},Math.max(n-a-3e5,0))}catch(e){r()}};a.subscribe(e=>e.token,e=>{e?(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e),o()):(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(null),i&&(clearTimeout(i),i=null))})},1463:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let s=(0,r(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(dashboard)/dashboard/page.tsx`),{__esModule:n,$$typeof:a}=s,i=s.default},95021:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>n});var s=r(25036);let n={title:"Dashboard",description:"Your life planning dashboard - track progress and manage goals"};function a({children:e}){return s.jsx(s.Fragment,{children:e})}},21342:(e,t,r)=>{"use strict";function s({children:e}){return e}r.r(t),r.d(t,{default:()=>s})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,8356,3828,2372],()=>r(78646));module.exports=s})();