(()=>{var e={};e.id=3339,e.ids=[3339],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},40655:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(50482),a=s(69108),n=s(62563),i=s.n(n),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["(dashboard)",{children:["progress",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84620)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/progress/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,95021)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/(dashboard)/progress/page.tsx"],u="/(dashboard)/progress/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/progress/page",pathname:"/progress",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13660:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},34184:(e,t,s)=>{Promise.resolve().then(s.bind(s,37992))},35303:()=>{},66138:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},45961:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},50340:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55794:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},7060:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},82958:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},1222:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},51354:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},35341:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},62093:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},51838:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},17910:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},14513:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},79200:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},37992:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var r=s(95344),a=s(3729),n=s(22254),i=s(23673),l=s(5094),o=s(19591),d=s(47210),c=s(60339),u=s(33468),m=s(46540),x=s(33668),h=s(2690),p=s(46064),f=s(25545),g=s(51354),b=s(79200),v=s(17910),j=s(7060),N=s(14513),y=s(51838),w=s(45961),O=s(31498);function D({goal:e,existingEntry:t,onSave:s,onCancel:n,className:o}){let[c,u]=(0,a.useState)({goalId:e.id,date:t?.date||new Date,progress:t?.progress||e.progress||0,timeSpent:t?.timeSpent||0,mood:t?.mood||"neutral",energy:t?.energy||"medium",motivation:t?.motivation||"medium",wins:t?.wins||[],challenges:t?.challenges||[],lessons:t?.lessons||[],nextSteps:t?.nextSteps||[],notes:t?.notes||""}),[D,k]=(0,a.useState)(""),[E,_]=(0,a.useState)(""),[C,U]=(0,a.useState)(""),[M,T]=(0,a.useState)(""),Z=e=>{u(t=>({...t,progress:Math.max(0,Math.min(100,e))}))},S=e=>{u(t=>({...t,timeSpent:Math.max(0,e)}))},L=e=>{u(t=>({...t,mood:e}))},A=e=>{u(t=>({...t,energy:e}))},F=e=>{u(t=>({...t,motivation:e}))},P=(e,t)=>{if(t.trim())switch(u(s=>({...s,[e]:[...s[e]||[],t.trim()]})),e){case"wins":k("");break;case"challenges":_("");break;case"lessons":U("");break;case"nextSteps":T("")}},z=(e,t)=>{u(s=>({...s,[e]:s[e]?.filter((e,s)=>s!==t)||[]}))},I=void 0!==c.progress&&c.progress>=0;return(0,r.jsxs)(i.Zb,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("max-w-4xl mx-auto",o),children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center",children:[r.jsx(p.Z,{className:"mr-2 h-5 w-5"}),t?"Update Progress":"Log Progress"]}),(0,r.jsxs)(i.SZ,{children:["Track your progress on: ",e.title]})]}),(0,r.jsxs)(i.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(x._,{htmlFor:"progress",children:"Progress Percentage"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx(m.I,{id:"progress",type:"number",min:"0",max:"100",value:c.progress||0,onChange:e=>Z(parseInt(e.target.value)||0),className:"w-24"}),r.jsx("span",{className:"text-sm text-muted-foreground",children:"%"})]}),r.jsx(d.E,{value:c.progress||0,className:"h-2"}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Previous: ",e.progress,"% → Current: ",c.progress,"%"]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(x._,{htmlFor:"timeSpent",children:"Time Spent (minutes)"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(f.Z,{className:"h-4 w-4 text-muted-foreground"}),r.jsx(m.I,{id:"timeSpent",type:"number",min:"0",value:c.timeSpent||0,onChange:e=>S(parseInt(e.target.value)||0),placeholder:"0"}),r.jsx("span",{className:"text-sm text-muted-foreground",children:"minutes"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x._,{className:"flex items-center",children:[r.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"How are you feeling about this goal?"]}),r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-2",children:[{value:"excited",label:"Excited",emoji:"\uD83D\uDE80"},{value:"confident",label:"Confident",emoji:"\uD83D\uDE0A"},{value:"neutral",label:"Neutral",emoji:"\uD83D\uDE10"},{value:"concerned",label:"Concerned",emoji:"\uD83D\uDE1F"},{value:"frustrated",label:"Frustrated",emoji:"\uD83D\uDE24"}].map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>L(e.value),className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-3 rounded-lg border text-center transition-all hover:shadow-md",c.mood===e.value?"border-primary bg-primary/10 text-primary":"border-muted hover:border-primary/50"),children:[r.jsx("div",{className:"text-2xl mb-1",children:e.emoji}),r.jsx("div",{className:"text-xs font-medium",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x._,{className:"flex items-center",children:[r.jsx(b.Z,{className:"mr-2 h-4 w-4"}),"Energy Level"]}),r.jsx("div",{className:"space-y-2",children:[{value:"high",label:"High Energy",color:"bg-green-500"},{value:"medium",label:"Medium Energy",color:"bg-yellow-500"},{value:"low",label:"Low Energy",color:"bg-red-500"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[r.jsx("input",{type:"radio",name:"energy",value:e.value,checked:c.energy===e.value,onChange:()=>A(e.value)}),r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-3 h-3 rounded-full",e.color)}),r.jsx("span",{className:"font-medium",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x._,{className:"flex items-center",children:[r.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Motivation Level"]}),r.jsx("div",{className:"space-y-2",children:[{value:"high",label:"High Motivation",color:"bg-green-500"},{value:"medium",label:"Medium Motivation",color:"bg-yellow-500"},{value:"low",label:"Low Motivation",color:"bg-red-500"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[r.jsx("input",{type:"radio",name:"motivation",value:e.value,checked:c.motivation===e.value,onChange:()=>F(e.value)}),r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-3 h-3 rounded-full",e.color)}),r.jsx("span",{className:"font-medium",children:e.label})]},e.value))})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x._,{className:"flex items-center",children:[r.jsx(j.Z,{className:"mr-2 h-4 w-4 text-green-500"}),"Wins & Accomplishments"]}),(0,r.jsxs)("div",{className:"space-y-2",children:[c.wins?.map((e,t)=>r.jsxs("div",{className:"flex items-center justify-between bg-green-50 dark:bg-green-950/20 p-2 rounded",children:[r.jsx("span",{className:"text-sm",children:e}),r.jsx("button",{type:"button",onClick:()=>z("wins",t),className:"text-muted-foreground hover:text-destructive",children:r.jsx(N.Z,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(m.I,{placeholder:"What went well? What are you proud of?",value:D,onChange:e=>k(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),P("wins",D))}),r.jsx(l.z,{type:"button",variant:"outline",onClick:()=>P("wins",D),children:r.jsx(y.Z,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x._,{className:"flex items-center",children:[r.jsx(w.Z,{className:"mr-2 h-4 w-4 text-orange-500"}),"Challenges & Obstacles"]}),(0,r.jsxs)("div",{className:"space-y-2",children:[c.challenges?.map((e,t)=>r.jsxs("div",{className:"flex items-center justify-between bg-orange-50 dark:bg-orange-950/20 p-2 rounded",children:[r.jsx("span",{className:"text-sm",children:e}),r.jsx("button",{type:"button",onClick:()=>z("challenges",t),className:"text-muted-foreground hover:text-destructive",children:r.jsx(N.Z,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(m.I,{placeholder:"What challenges did you face? What got in the way?",value:E,onChange:e=>_(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),P("challenges",E))}),r.jsx(l.z,{type:"button",variant:"outline",onClick:()=>P("challenges",E),children:r.jsx(y.Z,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x._,{className:"flex items-center",children:[r.jsx(v.Z,{className:"mr-2 h-4 w-4 text-blue-500"}),"Lessons Learned"]}),(0,r.jsxs)("div",{className:"space-y-2",children:[c.lessons?.map((e,t)=>r.jsxs("div",{className:"flex items-center justify-between bg-blue-50 dark:bg-blue-950/20 p-2 rounded",children:[r.jsx("span",{className:"text-sm",children:e}),r.jsx("button",{type:"button",onClick:()=>z("lessons",t),className:"text-muted-foreground hover:text-destructive",children:r.jsx(N.Z,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(m.I,{placeholder:"What did you learn? What insights did you gain?",value:C,onChange:e=>U(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),P("lessons",C))}),r.jsx(l.z,{type:"button",variant:"outline",onClick:()=>P("lessons",C),children:r.jsx(y.Z,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x._,{className:"flex items-center",children:[r.jsx(p.Z,{className:"mr-2 h-4 w-4 text-purple-500"}),"Next Steps"]}),(0,r.jsxs)("div",{className:"space-y-2",children:[c.nextSteps?.map((e,t)=>r.jsxs("div",{className:"flex items-center justify-between bg-purple-50 dark:bg-purple-950/20 p-2 rounded",children:[r.jsx("span",{className:"text-sm",children:e}),r.jsx("button",{type:"button",onClick:()=>z("nextSteps",t),className:"text-muted-foreground hover:text-destructive",children:r.jsx(N.Z,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(m.I,{placeholder:"What will you do next? What's your next action?",value:M,onChange:e=>T(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),P("nextSteps",M))}),r.jsx(l.z,{type:"button",variant:"outline",onClick:()=>P("nextSteps",M),children:r.jsx(y.Z,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(x._,{htmlFor:"notes",children:"Additional Notes"}),r.jsx(h.g,{id:"notes",placeholder:"Any additional thoughts, reflections, or context...",value:c.notes,onChange:e=>u(t=>({...t,notes:e.target.value})),rows:3})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-4 border-t",children:[r.jsx(l.z,{type:"button",variant:"outline",onClick:n,children:"Cancel"}),(0,r.jsxs)(l.z,{onClick:()=>{let e={...c,id:t?.id||`progress-${Date.now()}`,updatedAt:new Date};t||(e.createdAt=new Date),s(e)},disabled:!I,children:[r.jsx(O.Z,{className:"mr-2 h-4 w-4"}),t?"Update Progress":"Log Progress"]})]})]})]})}!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var k=s(82958);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let E=(0,s(69224).Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]);var _=s(62093),C=s(55794);function U({habits:e,habitEntries:t,onToggleHabit:s,onAddHabit:n,onEditHabit:o,className:d}){let[c,u]=(0,a.useState)(new Date),[m,x]=(0,a.useState)("today"),h=(()=>{let e=[];for(let t=6;t>=0;t--){let s=new Date;s.setDate(s.getDate()-t),e.push(s)}return e})(),p=(e,s)=>{let r=s.toDateString();return t.find(t=>t.habitId===e&&t.date.toDateString()===r)},f=(e,t)=>{let s=p(e,t);return s?.completed||!1},g=(e,t)=>{let r=f(e,t);s(e,t,!r)},b=e=>{let s=t.filter(t=>t.habitId===e.id);return{streak:Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(s),weeklyRate:Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(s,7),monthlyRate:Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(s,30)}},N={health:"bg-green-500",productivity:"bg-blue-500",learning:"bg-purple-500",relationships:"bg-pink-500",mindfulness:"bg-teal-500",other:"bg-gray-500"};return"today"===m?(0,r.jsxs)(i.Zb,{className:d,children:[r.jsx(i.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(i.ll,{className:"flex items-center",children:[r.jsx(j.Z,{className:"mr-2 h-5 w-5"}),"Today's Habits"]}),r.jsx(i.SZ,{children:c.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(l.z,{variant:"today"===m?"default":"outline",size:"sm",onClick:()=>x("today"),children:"Today"}),r.jsx(l.z,{variant:"week"===m?"default":"outline",size:"sm",onClick:()=>x("week"),children:"Week"}),r.jsx(l.z,{variant:"outline",size:"sm",onClick:n,children:r.jsx(y.Z,{className:"h-4 w-4"})})]})]})}),r.jsx(i.aY,{className:"space-y-4",children:0===e.filter(e=>e.isActive).length?(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(v.Z,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No habits yet"}),r.jsx("p",{className:"text-muted-foreground mb-4",children:"Start building positive habits to support your goals"}),(0,r.jsxs)(l.z,{onClick:n,children:[r.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Add Your First Habit"]})]}):r.jsx("div",{className:"space-y-3",children:e.filter(e=>e.isActive).map(e=>{let t=f(e.id,c),s=b(e);return(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center justify-between p-4 rounded-lg border transition-all",t?"bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800":"hover:bg-muted/50"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("button",{onClick:()=>g(e.id,c),className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-1 rounded-full transition-colors",t?"text-green-600 hover:text-green-700":"text-muted-foreground hover:text-foreground"),children:t?r.jsx(j.Z,{className:"h-6 w-6"}):r.jsx(k.Z,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("h4",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("font-medium",t&&"line-through text-muted-foreground"),children:e.title}),r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-2 h-2 rounded-full",N[e.category])})]}),r.jsx("p",{className:"text-sm text-muted-foreground",children:e.description})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[s.streak>0&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-orange-600",children:[r.jsx(E,{className:"h-4 w-4"}),r.jsx("span",{className:"text-sm font-medium",children:s.streak})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-sm font-medium",children:[Math.round(s.weeklyRate),"%"]}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"7 days"})]}),r.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>o(e),className:"h-8 w-8 p-0",children:r.jsx(_.Z,{className:"h-4 w-4"})})]})]},e.id)})})})]}):"week"===m?(0,r.jsxs)(i.Zb,{className:d,children:[r.jsx(i.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(i.ll,{className:"flex items-center",children:[r.jsx(C.Z,{className:"mr-2 h-5 w-5"}),"Weekly Habit Tracker"]}),r.jsx(i.SZ,{children:"Track your habits across the week"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(l.z,{variant:"today"===m?"default":"outline",size:"sm",onClick:()=>x("today"),children:"Today"}),r.jsx(l.z,{variant:"week"===m?"default":"outline",size:"sm",onClick:()=>x("week"),children:"Week"}),r.jsx(l.z,{variant:"outline",size:"sm",onClick:n,children:r.jsx(y.Z,{className:"h-4 w-4"})})]})]})}),r.jsx(i.aY,{children:0===e.filter(e=>e.isActive).length?(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(v.Z,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No habits yet"}),r.jsx("p",{className:"text-muted-foreground mb-4",children:"Start building positive habits to support your goals"}),(0,r.jsxs)(l.z,{onClick:n,children:[r.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Add Your First Habit"]})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-8 gap-2 mb-4",children:[r.jsx("div",{className:"text-sm font-medium text-muted-foreground",children:"Habit"}),h.map((e,t)=>(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-xs text-muted-foreground",children:e.toLocaleDateString("en-US",{weekday:"short"})}),r.jsx("div",{className:"text-sm font-medium",children:e.getDate()})]},t))]}),e.filter(e=>e.isActive).map(e=>{let t=b(e);return(0,r.jsxs)("div",{className:"grid grid-cols-8 gap-2 items-center py-2 border-b",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-2 h-2 rounded-full",N[e.category])}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium truncate",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-muted-foreground",children:[t.streak>0&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[r.jsx(E,{className:"h-3 w-3 text-orange-600"}),r.jsx("span",{children:t.streak})]}),(0,r.jsxs)("span",{children:[Math.round(t.weeklyRate),"%"]})]})]})]}),h.map((t,s)=>{let a=f(e.id,t),n=t.toDateString()===new Date().toDateString(),i=t>new Date;return r.jsx("div",{className:"flex justify-center",children:r.jsx("button",{onClick:()=>!i&&g(e.id,t),disabled:i,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-1 rounded-full transition-colors",i&&"opacity-30 cursor-not-allowed",n&&"ring-2 ring-primary ring-offset-1",a?"text-green-600 hover:text-green-700":"text-muted-foreground hover:text-foreground"),children:a?r.jsx(j.Z,{className:"h-5 w-5"}):r.jsx(k.Z,{className:"h-5 w-5"})})},s)})]},e.id)})]})})]}):null}function M({habits:e,habitEntries:t,className:s}){let a=e.filter(e=>e.isActive);t.filter(e=>e.completed).length;let n=a.length>0?a.reduce((e,s)=>{let r=t.filter(e=>e.habitId===s.id);return e+Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(r)},0)/a.length:0,l=a.length>0?a.reduce((e,s)=>{let r=t.filter(e=>e.habitId===s.id);return e+Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(r,30)},0)/a.length:0;return(0,r.jsxs)(i.Zb,{className:s,children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center",children:[r.jsx(p.Z,{className:"mr-2 h-5 w-5"}),"Habit Statistics"]})}),(0,r.jsxs)(i.aY,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold",children:a.length}),r.jsx("div",{className:"text-sm text-muted-foreground",children:"Active Habits"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold",children:Math.round(n)}),r.jsx("div",{className:"text-sm text-muted-foreground",children:"Avg Streak"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[Math.round(l),"%"]}),r.jsx("div",{className:"text-sm text-muted-foreground",children:"30-Day Rate"})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[r.jsx("span",{children:"Overall Progress"}),(0,r.jsxs)("span",{children:[Math.round(l),"%"]})]}),r.jsx(d.E,{value:l,className:"h-2"})]})]})]})}(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var T=s(35341),Z=s(50340);function S(){let{user:e,isAuthenticated:t,isLoading:s}=(0,u.t)(),m=(0,n.useRouter)(),{toast:x}=(0,c.pm)(),[h,g]=(0,a.useState)({goals:[],progressEntries:[],habits:[],habitEntries:[],lastUpdated:new Date}),[b,N]=(0,a.useState)(!1),[w,O]=(0,a.useState)(null),[k,E]=(0,a.useState)("week");(0,a.useEffect)(()=>{s||t||m.push("/login")},[t,s,m]),(0,a.useEffect)(()=>{let e=localStorage.getItem("planning-data");if(e)try{let t=JSON.parse(e);g(e=>({...e,goals:t.goals||[]}))}catch(e){}let t=localStorage.getItem("progress-data");if(t)try{let e=JSON.parse(t);g(t=>({...t,progressEntries:e.progressEntries?.map(e=>({...e,date:new Date(e.date),createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt)}))||[],habits:e.habits?.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt)}))||[],habitEntries:e.habitEntries?.map(e=>({...e,date:new Date(e.date),createdAt:new Date(e.createdAt)}))||[],lastUpdated:new Date(e.lastUpdated)}))}catch(e){}},[]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{let e={progressEntries:h.progressEntries,habits:h.habits,habitEntries:h.habitEntries,lastUpdated:h.lastUpdated};localStorage.setItem("progress-data",JSON.stringify(e))},2e3);return()=>clearTimeout(e)},[h]);let _=h.goals.filter(e=>"in_progress"===e.status),S=h.goals.filter(e=>"completed"===e.status),L=_.length>0?_.reduce((e,t)=>e+t.progress,0)/_.length:0,A=h.progressEntries.filter(e=>(new Date().getTime()-e.date.getTime())/864e5<=7).sort((e,t)=>t.date.getTime()-e.date.getTime()),F=Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(A,h.habitEntries);return s?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):t&&e?b&&w?(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[r.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(f.Z,{className:"h-6 w-6"}),r.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("span",{className:"text-sm text-muted-foreground",children:e.firstName||e.email}),r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>u.t.getState().logout(),children:"Sign Out"})]})]})}),r.jsx("main",{className:"container py-8",children:r.jsx(D,{goal:w,onSave:e=>{if(g(t=>{let s=t.progressEntries.findIndex(t=>t.id===e.id),r=s>=0?t.progressEntries.map((t,r)=>r===s?e:t):[...t.progressEntries,e];return{...t,progressEntries:r,lastUpdated:new Date}}),w&&void 0!==e.progress){let t=h.goals.map(t=>t.id===w.id?{...t,progress:e.progress,updatedAt:new Date}:t),s=localStorage.getItem("planning-data");if(s)try{let e=JSON.parse(s);e.goals=t,e.lastUpdated=new Date,localStorage.setItem("planning-data",JSON.stringify(e))}catch(e){}}x({title:"Progress Logged",description:`Progress has been logged for "${w?.title}".`}),N(!1),O(null)},onCancel:()=>{N(!1),O(null)}})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[r.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(f.Z,{className:"h-6 w-6"}),r.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("span",{className:"text-sm text-muted-foreground",children:e.firstName||e.email}),r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>u.t.getState().logout(),children:"Sign Out"})]})]})}),r.jsx("main",{className:"container py-8",children:(0,r.jsxs)("div",{className:"space-y-8",children:[r.jsx("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Progress Tracking"}),r.jsx("p",{className:"text-muted-foreground",children:"Track your daily progress and maintain momentum toward your goals"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium",children:"Active Goals"}),r.jsx("p",{className:"text-2xl font-bold",children:_.length})]}),r.jsx(v.Z,{className:"h-8 w-8 text-muted-foreground"})]})})}),r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium",children:"Completed"}),r.jsx("p",{className:"text-2xl font-bold",children:S.length})]}),r.jsx(j.Z,{className:"h-8 w-8 text-green-500"})]})})}),r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium",children:"Avg Progress"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[Math.round(L),"%"]})]}),r.jsx(p.Z,{className:"h-8 w-8 text-blue-500"})]})})}),r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium",children:"Active Habits"}),r.jsx("p",{className:"text-2xl font-bold",children:h.habits.filter(e=>e.isActive).length})]}),r.jsx(C.Z,{className:"h-8 w-8 text-purple-500"})]})})})]}),F.length>0&&(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center",children:[r.jsx(T.Z,{className:"mr-2 h-5 w-5"}),"Progress Insights"]})}),r.jsx(i.aY,{children:r.jsx("div",{className:"space-y-2",children:F.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[r.jsx("div",{className:"w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"}),r.jsx("p",{className:"text-sm",children:e})]},t))})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center",children:[r.jsx(v.Z,{className:"mr-2 h-5 w-5"}),"Goal Progress"]}),r.jsx(i.SZ,{children:"Log progress on your active goals"})]}),r.jsx(i.aY,{children:0===_.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(v.Z,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No active goals"}),r.jsx("p",{className:"text-muted-foreground mb-4",children:"Create some goals in your planning session to start tracking progress"}),r.jsx(l.z,{onClick:()=>m.push("/planning"),children:"Go to Planning"})]}):r.jsx("div",{className:"space-y-4",children:_.map(e=>{let t=A.find(t=>t.goalId===e.id);return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h4",{className:"font-medium",children:e.title}),r.jsx("p",{className:"text-sm text-muted-foreground line-clamp-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[r.jsx("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[e.progress,"%"]})]}),r.jsx(d.E,{value:e.progress,className:"h-2"})]}),t&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-lg",children:Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(t.mood)}),r.jsx("span",{className:"text-xs text-muted-foreground",children:t.date.toLocaleDateString()})]})]})]}),(0,r.jsxs)(l.z,{onClick:()=>{O(e),N(!0)},className:"ml-4",children:[r.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Log Progress"]})]},e.id)})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx(U,{habits:h.habits,habitEntries:h.habitEntries,onToggleHabit:(e,t,s)=>{let r=`habit-entry-${e}-${t.toDateString()}`;g(a=>{let n;let i=a.habitEntries.findIndex(s=>s.habitId===e&&s.date.toDateString()===t.toDateString());if(i>=0)n=a.habitEntries.map((e,t)=>t===i?{...e,completed:s}:e);else{let i={id:r,habitId:e,date:t,completed:s,createdAt:new Date};n=[...a.habitEntries,i]}return{...a,habitEntries:n,lastUpdated:new Date}}),x({title:s?"Habit Completed":"Habit Unchecked",description:`Habit has been ${s?"marked as complete":"unchecked"} for ${t.toLocaleDateString()}.`})},onAddHabit:()=>{let e={id:`habit-${Date.now()}`,title:"New Habit",description:"Description of your new habit",category:"other",frequency:"daily",streak:0,longestStreak:0,totalCompletions:0,goalIds:[],lifeAreas:[],isActive:!0,createdAt:new Date,updatedAt:new Date};g(t=>({...t,habits:[...t.habits,e],lastUpdated:new Date})),x({title:"Habit Added",description:"New habit has been added. Click to edit and customize it."})},onEditHabit:e=>{x({title:"Edit Habit",description:"Habit editing will be available in the next update."})}})}),r.jsx("div",{children:r.jsx(M,{habits:h.habits,habitEntries:h.habitEntries})})]}),A.length>0&&(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center",children:[r.jsx(Z.Z,{className:"mr-2 h-5 w-5"}),"Recent Activity"]})}),r.jsx(i.aY,{children:r.jsx("div",{className:"space-y-4",children:A.slice(0,5).map(e=>{let t=h.goals.find(t=>t.id===e.goalId);return(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded-lg",children:[r.jsx("div",{className:"text-2xl",children:Object(function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}())(e.mood)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("h4",{className:"font-medium",children:t?.title}),(0,r.jsxs)(o.C,{variant:"outline",children:[e.progress,"%"]})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.date.toLocaleDateString()," • ",e.timeSpent," minutes"]}),e.wins.length>0&&(0,r.jsxs)("div",{className:"mt-2",children:[r.jsx("p",{className:"text-sm font-medium text-green-600",children:"Wins:"}),r.jsx("ul",{className:"text-sm text-muted-foreground",children:e.wins.slice(0,2).map((e,t)=>(0,r.jsxs)("li",{children:["• ",e]},t))})]})]})]},e.id)})})})]})]})})]}):null}!function(){var e=Error("Cannot find module '@/lib/progress'");throw e.code="MODULE_NOT_FOUND",e}()},19591:(e,t,s)=>{"use strict";s.d(t,{C:()=>i});var r=s(95344);s(3729);var a=s(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...s}){return r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(n({variant:t}),e),...s})}},5094:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var r=s(95344),a=s(3729),n=s(32751),i=s(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var l=s(42739);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,loading:i=!1,leftIcon:d,rightIcon:c,children:u,disabled:m,...x},h)=>{let p=a?n.g7:"button",f=m||i;return(0,r.jsxs)(p,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o({variant:t,size:s,className:e})),ref:h,disabled:f,"aria-disabled":f,...x,children:[i&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!i&&d&&r.jsx("span",{className:"mr-2",children:d}),u,!i&&c&&r.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>i,SZ:()=>o,Zb:()=>n,aY:()=>d,ll:()=>l});var r=s(95344),a=s(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let i=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let o=a.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},46540:(e,t,s)=>{"use strict";s.d(t,{I:()=>o});var r=s(95344),a=s(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var n=s(66138),i=s(1222),l=s(53148);let o=a.forwardRef(({className:e,type:t,label:s,error:o,helperText:d,leftIcon:c,rightIcon:u,variant:m="default",id:x,required:h,...p},f)=>{let[g,b]=a.useState(!1),[v]=a.useState(()=>x||`input-${Math.random().toString(36).substr(2,9)}`),j="password"===t,N=j&&g?"text":t,y=!!o||"error"===m;return(0,r.jsxs)("div",{className:"space-y-2",children:[s&&(0,r.jsxs)("label",{htmlFor:v,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[s,h&&r.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[c&&r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:c})}),r.jsx("input",{type:N,id:v,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",y&&"border-destructive focus-visible:ring-destructive","success"===m&&"border-green-500 focus-visible:ring-green-500",c&&"pl-10",(u||j||y)&&"pr-10",e),ref:f,"aria-invalid":y,"aria-describedby":o?`${v}-error`:d?`${v}-helper`:void 0,...p}),y&&r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(n.Z,{className:"h-4 w-4 text-destructive"})}),j&&r.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{b(!g)},"aria-label":g?"Hide password":"Show password",children:g?r.jsx(i.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):r.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),u&&!y&&!j&&r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:u})})]}),o&&r.jsx("p",{id:`${v}-error`,className:"text-sm text-destructive",role:"alert",children:o}),d&&!o&&r.jsx("p",{id:`${v}-helper`,className:"text-sm text-muted-foreground",children:d})]})});o.displayName="Input"},33668:(e,t,s)=>{"use strict";s.d(t,{_:()=>d});var r=s(95344),a=s(3729),n=s(62409),i=a.forwardRef((e,t)=>(0,r.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=s(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>r.jsx(i,{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o(),e),...t}));d.displayName=i.displayName},47210:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(95344),a=s(3729),n=s(47370);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let i=a.forwardRef(({className:e,value:t,...s},a)=>r.jsx(n.fC,{ref:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:r.jsx(n.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));i.displayName=n.fC.displayName},2690:(e,t,s)=>{"use strict";s.d(t,{g:()=>n});var r=s(95344),a=s(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=a.forwardRef(({className:e,...t},s)=>r.jsx("textarea",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n.displayName="Textarea"},60339:(e,t,s)=>{"use strict";s.d(t,{pm:()=>m});var r=s(3729);let a=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=l(d,e),o.forEach(e=>{e(d)})}function u({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=r.useState(d);return r.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},33468:(e,t,s)=>{"use strict";s.d(t,{t:()=>n});var r=s(43158),a=s(67023);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let n=(0,r.Ue)()((0,a.tJ)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{e({isLoading:!0,error:null});try{let s=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.login(t);e({user:s.user,token:s.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Login failed. Please try again."}),t}},register:async t=>{e({isLoading:!0,error:null});try{let s=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.register(t);e({user:s.user,token:s.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Registration failed. Please try again."}),t}},logout:async()=>{e({isLoading:!0});try{await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.logout()}catch(e){}finally{e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}},refreshToken:async()=>{let{token:s}=t();if(!s)throw Error("No token available for refresh");try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.refreshToken();e({user:t.user,token:t.accessToken,isAuthenticated:!0,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,error:"Session expired. Please login again."}),t}},updateProfile:async t=>{e({isLoading:!0,error:null});try{let s=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.updateProfile(t);e({user:s,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Profile update failed. Please try again."}),t}},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},initialize:async()=>{let{token:s}=t();if(s){e({isLoading:!0});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.me();e({user:t,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}}}}),{name:"auth-storage",partialize:e=>({token:e.token,user:e.user}),onRehydrateStorage:()=>e=>{e?.token&&Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e.token)}})),i=null,l=()=>{let{token:e,refreshToken:t,logout:s}=n.getState();if(i&&clearTimeout(i),e)try{let r=JSON.parse(atob(e.split(".")[1])),a=1e3*r.exp,n=Date.now();i=setTimeout(async()=>{try{await t(),l()}catch(e){await s()}},Math.max(a-n-3e5,0))}catch(e){s()}};n.subscribe(e=>e.token,e=>{e?(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e),l()):(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(null),i&&(clearTimeout(i),i=null))})},95021:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>a});var r=s(25036);let a={title:"Dashboard",description:"Your life planning dashboard - track progress and manage goals"};function n({children:e}){return r.jsx(r.Fragment,{children:e})}},84620:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(dashboard)/progress/page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default},21342:(e,t,s)=>{"use strict";function r({children:e}){return e}s.r(t),s.d(t,{default:()=>r})},98462:(e,t,s)=>{"use strict";s.d(t,{b:()=>i,k:()=>n});var r=s(3729),a=s(95344);function n(e,t){let s=r.createContext(t),n=e=>{let{children:t,...n}=e,i=r.useMemo(()=>n,Object.values(n));return(0,a.jsx)(s.Provider,{value:i,children:t})};return n.displayName=e+"Provider",[n,function(a){let n=r.useContext(s);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function i(e,t=[]){let s=[],n=()=>{let t=s.map(e=>r.createContext(e));return function(s){let a=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return n.scopeName=e,[function(t,n){let i=r.createContext(n),l=s.length;s=[...s,n];let o=t=>{let{scope:s,children:n,...o}=t,d=s?.[e]?.[l]||i,c=r.useMemo(()=>o,Object.values(o));return(0,a.jsx)(d.Provider,{value:c,children:n})};return o.displayName=t+"Provider",[o,function(s,a){let o=a?.[e]?.[l]||i,d=r.useContext(o);if(d)return d;if(void 0!==n)return n;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=s.reduce((t,{useScope:s,scopeName:r})=>{let a=s(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return s.scopeName=t.scopeName,s}(n,...t)]}},62409:(e,t,s)=>{"use strict";s.d(t,{WV:()=>l,jH:()=>o});var r=s(3729),a=s(81202),n=s(32751),i=s(95344),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.Z8)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e,l=a?s:t;return(0,i.jsx)(l,{...n,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function o(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},47370:(e,t,s)=>{"use strict";s.d(t,{fC:()=>j,z$:()=>N});var r=s(3729),a=s(98462),n=s(62409),i=s(95344),l="Progress",[o,d]=(0,a.b)(l),[c,u]=o(l),m=r.forwardRef((e,t)=>{let{__scopeProgress:s,value:r=null,max:a,getValueLabel:l=p,...o}=e;(a||0===a)&&b(a);let d=b(a)?a:100;null!==r&&v(r,d);let u=v(r,d)?r:null,m=g(u)?l(u,d):void 0;return(0,i.jsx)(c,{scope:s,value:u,max:d,children:(0,i.jsx)(n.WV.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":g(u)?u:void 0,"aria-valuetext":m,role:"progressbar","data-state":f(u,d),"data-value":u??void 0,"data-max":d,...o,ref:t})})});m.displayName=l;var x="ProgressIndicator",h=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,a=u(x,s);return(0,i.jsx)(n.WV.div,{"data-state":f(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:t})});function p(e,t){return`${Math.round(e/t*100)}%`}function f(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function b(e){return g(e)&&!isNaN(e)&&e>0}function v(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=x;var j=m,N=h}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,8356,3828,2372],()=>s(40655));module.exports=r})();