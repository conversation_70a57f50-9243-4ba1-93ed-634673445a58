(()=>{var e={};e.id=9357,e.ids=[9357],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3213:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(50482),a=r(69108),n=r(62563),i=r.n(n),o=r(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["(dashboard)",{children:["onboarding",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26358)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/onboarding/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,95021)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/(dashboard)/onboarding/page.tsx"],u="/(dashboard)/onboarding/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/onboarding/page",pathname:"/onboarding",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13660:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},41958:(e,t,r)=>{Promise.resolve().then(r.bind(r,45656))},35303:()=>{},63024:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35299:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},50340:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},5700:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]])},7060:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51354:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},17910:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},79200:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},45656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var s=r(95344),a=r(3729),n=r(22254),i=r(23673),o=r(5094),l=r(19591),d=r(33468),c=r(51354),u=r(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,u.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),h=(0,u.Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),p=(0,u.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),x=(0,u.Z)("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]);var f=r(79200);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,u.Z)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]]);var y=r(5700),b=r(17910),v=r(25545),j=r(50340),N=r(7060),w=r(63024),O=r(35299);let k=[{name:"Values & Purpose",description:"Your core values, life mission, and sense of purpose",icon:c.Z,color:"bg-purple-500"},{name:"Contribution & Impact",description:"How you contribute to others and make a positive impact",icon:m,color:"bg-emerald-500"},{name:"Location & Tangibles",description:"Your physical environment, possessions, and material circumstances",icon:h,color:"bg-amber-500"},{name:"Money & Finances",description:"Financial health, income, expenses, savings, and investments",icon:p,color:"bg-green-500"},{name:"Career & Work",description:"Professional development, career progression, and job satisfaction",icon:x,color:"bg-blue-500"},{name:"Health & Fitness",description:"Physical health, fitness, nutrition, and overall well-being",icon:f.Z,color:"bg-red-500"},{name:"Education & Skill Development",description:"Continuous learning, skill development, and intellectual growth",icon:g,color:"bg-indigo-500"},{name:"Social Life & Relationships",description:"Family, friends, romantic partners, and social connections",icon:m,color:"bg-pink-500"},{name:"Emotions & Well-Being",description:"Emotional health, mental well-being, and psychological balance",icon:y.Z,color:"bg-teal-500"},{name:"Character & Identity",description:"Personal character, integrity, self-awareness, and authenticity",icon:c.Z,color:"bg-violet-500"},{name:"Productivity & Organization",description:"Personal productivity systems, time management, and efficiency",icon:b.Z,color:"bg-orange-500"},{name:"Adventure & Creativity",description:"Fun, adventure, creativity, hobbies, and activities that bring joy",icon:f.Z,color:"bg-lime-500"}],_=[{title:"Welcome to 8,760 Hours",description:"Transform your life with systematic annual planning",content:"WelcomeStep"},{title:"The 12 Life Areas Framework",description:"Understanding the foundation of systematic life planning",content:"LifeAreasStep"},{title:"How It Works",description:"Your journey to intentional living",content:"ProcessStep"},{title:"Ready to Begin",description:"Start your life planning journey",content:"ReadyStep"}];function E(){let[e,t]=(0,a.useState)(0),{user:r}=(0,d.t)(),c=(0,n.useRouter)(),u=()=>(0,s.jsxs)("div",{className:"text-center space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("div",{className:"mx-auto w-16 h-16 bg-primary rounded-full flex items-center justify-center",children:s.jsx(v.Z,{className:"h-8 w-8 text-primary-foreground"})}),(0,s.jsxs)("h2",{className:"text-2xl font-bold",children:["Welcome, ",r?.firstName||"there","!"]}),s.jsx("p",{className:"text-muted-foreground max-w-md mx-auto",children:"You're about to embark on a transformative journey of systematic life planning. The 8,760 Hours methodology will help you make the most of every hour in your year."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto",children:[(0,s.jsxs)(i.Zb,{className:"text-center p-4",children:[s.jsx(j.Z,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),s.jsx("h3",{className:"font-semibold",children:"Assess"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Evaluate your current state across all life areas"})]}),(0,s.jsxs)(i.Zb,{className:"text-center p-4",children:[s.jsx(b.Z,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),s.jsx("h3",{className:"font-semibold",children:"Plan"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Set meaningful goals and create actionable plans"})]}),(0,s.jsxs)(i.Zb,{className:"text-center p-4",children:[s.jsx(N.Z,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),s.jsx("h3",{className:"font-semibold",children:"Achieve"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Track progress and achieve your life goals"})]})]})]}),m=()=>(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("h2",{className:"text-2xl font-bold",children:"The 12 Life Areas"}),s.jsx("p",{className:"text-muted-foreground",children:"These areas form the foundation of comprehensive life planning"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:k.map((e,t)=>{let r=e.icon;return s.jsx(i.Zb,{className:"p-4 hover:shadow-md transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:`p-2 rounded-lg ${e.color}`,children:s.jsx(r,{className:"h-4 w-4 text-white"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("h3",{className:"font-semibold text-sm",children:e.name}),s.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:e.description})]})]})},t)})})]}),h=()=>(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("h2",{className:"text-2xl font-bold",children:"Your Planning Process"}),s.jsx("p",{className:"text-muted-foreground",children:"Follow this proven methodology for systematic life improvement"})]}),(0,s.jsxs)("div",{className:"space-y-4 max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"1"}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold",children:"Life Snapshot"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Assess your current state across all 12 life areas with detailed ratings and reflections"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"2"}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold",children:"Future Visioning"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Create a compelling vision of your ideal future using mind mapping and visualization"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"3"}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold",children:"Goal Setting"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Set 3-5 major goals with clear success criteria and actionable sub-projects"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[s.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"4"}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold",children:"Progress Tracking"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Monitor your progress with regular reviews and adjust your plans as needed"})]})]})]})]}),p=()=>(0,s.jsxs)("div",{className:"text-center space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("div",{className:"mx-auto w-16 h-16 bg-green-500 rounded-full flex items-center justify-center",children:s.jsx(N.Z,{className:"h-8 w-8 text-white"})}),s.jsx("h2",{className:"text-2xl font-bold",children:"You're Ready to Begin!"}),s.jsx("p",{className:"text-muted-foreground max-w-md mx-auto",children:"You now understand the framework and process. Let's start your journey toward intentional living and meaningful goal achievement."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("h3",{className:"font-semibold",children:"Next Steps:"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[s.jsx("p",{children:"1. Complete your first life area assessment"}),s.jsx("p",{children:"2. Create your annual planning session"}),s.jsx("p",{children:"3. Set your major goals for the year"}),s.jsx("p",{children:"4. Begin tracking your daily progress"})]})]})]});return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[s.jsx("header",{className:"border-b bg-background/95 backdrop-blur",children:(0,s.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(v.Z,{className:"h-6 w-6"}),s.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,s.jsxs)(l.C,{variant:"secondary",children:["Step ",e+1," of ",_.length]})]})}),s.jsx("div",{className:"w-full bg-muted h-1",children:s.jsx("div",{className:"bg-primary h-1 transition-all duration-300",style:{width:`${(e+1)/_.length*100}%`}})}),s.jsx("main",{className:"container py-8",children:s.jsx("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)(i.Zb,{className:"p-8",children:[(0,s.jsxs)(i.Ol,{className:"text-center pb-8",children:[s.jsx(i.ll,{className:"text-xl",children:_[e]?.title||"Welcome"}),s.jsx(i.SZ,{children:_[e]?.description||"Getting started with your life planning journey"})]}),s.jsx(i.aY,{children:(()=>{let t=_[e];if(!t)return s.jsx(u,{});switch(t.content){case"WelcomeStep":default:return s.jsx(u,{});case"LifeAreasStep":return s.jsx(m,{});case"ProcessStep":return s.jsx(h,{});case"ReadyStep":return s.jsx(p,{})}})()}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-8",children:[(0,s.jsxs)(o.z,{variant:"outline",onClick:()=>{e>0&&t(e-1)},disabled:0===e,children:[s.jsx(w.Z,{className:"mr-2 h-4 w-4"}),"Previous"]}),s.jsx("div",{className:"flex space-x-2",children:_.map((t,r)=>s.jsx("div",{className:`w-2 h-2 rounded-full transition-colors ${r<=e?"bg-primary":"bg-muted"}`},r))}),s.jsx(o.z,{onClick:()=>{e<_.length-1?t(e+1):c.push("/dashboard")},children:e===_.length-1?"Get Started":(0,s.jsxs)(s.Fragment,{children:["Next",s.jsx(O.Z,{className:"ml-2 h-4 w-4"})]})})]})]})})})]})}},19591:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var s=r(95344);r(3729);var a=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(n({variant:t}),e),...r})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(95344),a=r(3729),n=r(32751),i=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var o=r(42739);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,loading:i=!1,leftIcon:d,rightIcon:c,children:u,disabled:m,...h},p)=>{let x=a?n.g7:"button",f=m||i;return(0,s.jsxs)(x,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l({variant:t,size:r,className:e})),ref:p,disabled:f,"aria-disabled":f,...h,children:[i&&s.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!i&&d&&s.jsx("span",{className:"mr-2",children:d}),u,!i&&c&&s.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>i,SZ:()=>l,Zb:()=>n,aY:()=>d,ll:()=>o});var s=r(95344),a=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},33468:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var s=r(43158),a=r(67023);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let n=(0,s.Ue)()((0,a.tJ)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.login(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Login failed. Please try again."}),t}},register:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.register(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Registration failed. Please try again."}),t}},logout:async()=>{e({isLoading:!0});try{await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.logout()}catch(e){}finally{e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}},refreshToken:async()=>{let{token:r}=t();if(!r)throw Error("No token available for refresh");try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.refreshToken();e({user:t.user,token:t.accessToken,isAuthenticated:!0,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,error:"Session expired. Please login again."}),t}},updateProfile:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.updateProfile(t);e({user:r,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Profile update failed. Please try again."}),t}},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},initialize:async()=>{let{token:r}=t();if(r){e({isLoading:!0});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.me();e({user:t,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}}}}),{name:"auth-storage",partialize:e=>({token:e.token,user:e.user}),onRehydrateStorage:()=>e=>{e?.token&&Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e.token)}})),i=null,o=()=>{let{token:e,refreshToken:t,logout:r}=n.getState();if(i&&clearTimeout(i),e)try{let s=JSON.parse(atob(e.split(".")[1])),a=1e3*s.exp,n=Date.now();i=setTimeout(async()=>{try{await t(),o()}catch(e){await r()}},Math.max(a-n-3e5,0))}catch(e){r()}};n.subscribe(e=>e.token,e=>{e?(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e),o()):(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(null),i&&(clearTimeout(i),i=null))})},95021:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>a});var s=r(25036);let a={title:"Dashboard",description:"Your life planning dashboard - track progress and manage goals"};function n({children:e}){return s.jsx(s.Fragment,{children:e})}},26358:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let s=(0,r(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(dashboard)/onboarding/page.tsx`),{__esModule:a,$$typeof:n}=s,i=s.default},21342:(e,t,r)=>{"use strict";function s({children:e}){return e}r.r(t),r.d(t,{default:()=>s})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,8356,3828,2372],()=>r(3213));module.exports=s})();