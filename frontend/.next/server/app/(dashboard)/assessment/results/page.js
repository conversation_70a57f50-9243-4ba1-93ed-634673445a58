(()=>{var e={};e.id=2178,e.ids=[2178],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},43955:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var s=t(50482),n=t(69108),a=t(62563),i=t.n(a),o=t(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["(dashboard)",{children:["assessment",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,33734)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/assessment/results/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,95021)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/(dashboard)/assessment/results/page.tsx"],u="/(dashboard)/assessment/results/page",m={require:t,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/(dashboard)/assessment/results/page",pathname:"/assessment/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13660:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},74304:(e,r,t)=>{Promise.resolve().then(t.bind(t,38994))},35303:()=>{},45961:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},63024:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},65187:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},50340:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},17910:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},38994:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(95344),n=t(3729),a=t(22254),i=t(23673),o=t(5094),l=t(19591),d=t(47210),c=t(33468),u=t(92400);!function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}();var m=t(25545),f=t(63024),h=t(50340),x=t(65187),p=t(45961),b=t(17910),N=t(46064);function g(){let{user:e,isAuthenticated:r,isLoading:t}=(0,c.t)(),g=(0,a.useRouter)(),[j,v]=(0,n.useState)({ratings:{},notes:{}});(0,n.useEffect)(()=>{t||r||g.push("/login")},[r,t,g]),(0,n.useEffect)(()=>{let e=localStorage.getItem("assessment-data");if(e)try{let r=JSON.parse(e);v(r)}catch(e){g.push("/assessment")}else g.push("/assessment")},[g]);let O=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())(j.ratings),w=Object.values(j.ratings).filter(e=>e>0).length,y=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).filter(e=>(j.ratings[e.id]||0)>=5).sort((e,r)=>(j.ratings[r.id]||0)-(j.ratings[e.id]||0)),_=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).filter(e=>3>=(j.ratings[e.id]||0)&&(j.ratings[e.id]||0)>0).sort((e,r)=>(j.ratings[e.id]||0)-(j.ratings[r.id]||0)),E={foundation:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())(Object.fromEntries(Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())("foundation").map(e=>[e.id,j.ratings[e.id]||0]))),growth:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())(Object.fromEntries(Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())("growth").map(e=>[e.id,j.ratings[e.id]||0]))),connection:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())(Object.fromEntries(Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())("connection").map(e=>[e.id,j.ratings[e.id]||0]))),expression:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())(Object.fromEntries(Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())("expression").map(e=>[e.id,j.ratings[e.id]||0])))};return t?s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):r&&e?(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[s.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(m.Z,{className:"h-6 w-6"}),s.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:e.firstName||e.email}),s.jsx(o.z,{variant:"outline",size:"sm",onClick:()=>c.t.getState().logout(),children:"Sign Out"})]})]})}),s.jsx("main",{className:"container py-8",children:(0,s.jsxs)("div",{className:"space-y-8",children:[s.jsx("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)(o.z,{variant:"ghost",onClick:()=>g.push("/assessment"),className:"mb-4",children:[s.jsx(f.Z,{className:"mr-2 h-4 w-4"}),"Back to Assessment"]}),s.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Assessment Results"}),s.jsx("p",{className:"text-muted-foreground",children:"Your life areas assessment insights and recommendations"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[s.jsx(i.Zb,{children:s.jsx(i.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Overall Average"}),s.jsx("p",{className:"text-2xl font-bold",children:O.toFixed(1)})]}),s.jsx(h.Z,{className:"h-8 w-8 text-muted-foreground"})]})})}),s.jsx(i.Zb,{children:s.jsx(i.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Strong Areas"}),s.jsx("p",{className:"text-2xl font-bold",children:y.length})]}),s.jsx(x.Z,{className:"h-8 w-8 text-green-500"})]})})}),s.jsx(i.Zb,{children:s.jsx(i.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Improvement Areas"}),s.jsx("p",{className:"text-2xl font-bold",children:_.length})]}),s.jsx(p.Z,{className:"h-8 w-8 text-orange-500"})]})})}),s.jsx(i.Zb,{children:s.jsx(i.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Completed"}),(0,s.jsxs)("p",{className:"text-2xl font-bold",children:[w,"/",Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).length]})]}),s.jsx(b.Z,{className:"h-8 w-8 text-blue-500"})]})})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Category Breakdown"}),s.jsx(i.SZ,{children:"Average ratings across the four life categories"})]}),s.jsx(i.aY,{children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(E).map(([e,r])=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm font-medium capitalize",children:e}),s.jsx("span",{className:"text-sm font-bold",children:r.toFixed(1)})]}),s.jsx(d.E,{value:r/7*100})]},e))})})]}),y.length>0&&(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[(0,s.jsxs)(i.ll,{className:"flex items-center",children:[s.jsx(x.Z,{className:"mr-2 h-5 w-5 text-green-500"}),"Your Strengths"]}),s.jsx(i.SZ,{children:"Life areas where you're performing well (rated 5+)"})]}),s.jsx(i.aY,{children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:y.map(e=>{let r=e.icon;return(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg bg-green-50 dark:bg-green-950/20",children:[s.jsx("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:s.jsx(r,{className:"h-4 w-4 text-white"})}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium",children:e.name}),s.jsx("p",{className:"text-sm text-muted-foreground",children:e.description})]}),s.jsx(u.Zh,{rating:j.ratings[e.id]||0,size:"sm",showLabel:!1})]},e.id)})})})]}),_.length>0&&(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[(0,s.jsxs)(i.ll,{className:"flex items-center",children:[s.jsx(N.Z,{className:"mr-2 h-5 w-5 text-orange-500"}),"Areas for Improvement"]}),s.jsx(i.SZ,{children:"Life areas with the most potential for growth (rated 3 or below)"})]}),s.jsx(i.aY,{children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:_.map(e=>{let r=e.icon;return(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg bg-orange-50 dark:bg-orange-950/20",children:[s.jsx("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:s.jsx(r,{className:"h-4 w-4 text-white"})}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium",children:e.name}),s.jsx("p",{className:"text-sm text-muted-foreground",children:e.description}),s.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:e.improvementAreas.slice(0,3).map((e,r)=>s.jsx(l.C,{variant:"outline",className:"text-xs",children:e},r))})]}),s.jsx(u.Zh,{rating:j.ratings[e.id]||0,size:"sm",showLabel:!1})]},e.id)})})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Next Steps"}),s.jsx(i.SZ,{children:"Recommended actions based on your assessment"})]}),(0,s.jsxs)(i.aY,{children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"1"}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium",children:"Create Your Annual Planning Session"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Use these insights to set meaningful goals for the year ahead"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"2"}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium",children:"Focus on Top 3 Improvement Areas"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Choose 3 areas with the lowest ratings for focused improvement"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium",children:"3"}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium",children:"Leverage Your Strengths"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Use your strong areas to support growth in weaker areas"})]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-4 mt-6",children:[s.jsx(o.z,{onClick:()=>g.push("/planning"),children:"Start Planning Session"}),s.jsx(o.z,{variant:"outline",onClick:()=>g.push("/assessment"),children:"Retake Assessment"})]})]})]})]})})]}):null}},92400:(e,r,t)=>{"use strict";t.d(r,{Bb:()=>d,Mm:()=>c,Zh:()=>l,z4:()=>o});var s=t(95344),n=t(3729);(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}();var a=t(5094),i=t(23673);function o({value:e,onChange:r,disabled:t=!1,size:i="md",showLabels:o=!0,showDescriptions:l=!1,className:d}){let[c,u]=(0,n.useState)(null),m={sm:"h-8 w-8 text-xs",md:"h-10 w-10 text-sm",lg:"h-12 w-12 text-base"},f=c||e;return(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("space-y-4",d),children:[s.jsx("div",{className:"flex items-center justify-center space-x-2",children:Object.keys(Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())).map(Number).map(n=>{let o=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())[n],l=e===n,d=c===n,h=f&&n<=f;return s.jsx(a.z,{variant:l?"default":"outline",size:"sm",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(m[i],"relative rounded-full border-2 transition-all duration-200 hover:scale-110",l&&"ring-2 ring-offset-2 ring-primary",d&&"scale-110",h&&!l&&"bg-opacity-20",t&&"opacity-50 cursor-not-allowed"),style:{backgroundColor:l||d?o.color:void 0,borderColor:h?o.color:void 0,color:l||d?"white":void 0},onClick:()=>!t&&r(n),onMouseEnter:()=>!t&&u(n),onMouseLeave:()=>!t&&u(null),disabled:t,"aria-label":`Rate ${n} - ${o.label}`,"data-testid":`rating-${n}`,children:n},n)})}),f&&(0,s.jsxs)("div",{className:"text-center space-y-2",children:[o&&(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[s.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())[f].color}}),(0,s.jsxs)("span",{className:"font-medium text-lg",children:[f," - ",Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())[f].label]})]}),l&&s.jsx("p",{className:"text-sm text-muted-foreground max-w-md mx-auto",children:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())[f].description})]}),!f&&o&&s.jsx("div",{className:"text-center",children:s.jsx("p",{className:"text-sm text-muted-foreground",children:"Select a rating from 1 (Poor) to 7 (Perfect)"})})]})}function l({rating:e,size:r="md",showLabel:t=!0,showDescription:n=!1,className:a}){let i=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())[e];return(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center space-x-2",a),children:[s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-full flex items-center justify-center font-medium text-white",{sm:"h-6 w-6 text-xs",md:"h-8 w-8 text-sm",lg:"h-10 w-10 text-base"}[r]),style:{backgroundColor:i.color},children:e}),t&&(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("span",{className:"font-medium",children:i.label}),n&&s.jsx("p",{className:"text-sm text-muted-foreground",children:i.description})]})]})}function d({compact:e=!1,className:r}){return(0,s.jsxs)(i.Zb,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-4",r),children:[s.jsx("h3",{className:"font-semibold mb-3",children:"Rating Scale"}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("space-y-2",e&&"grid grid-cols-2 gap-2 space-y-0"),children:Object.keys(Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())).map(Number).map(r=>{let t=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())[r];return(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-medium",style:{backgroundColor:t.color},children:r}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("span",{className:"font-medium text-sm",children:t.label}),!e&&s.jsx("p",{className:"text-xs text-muted-foreground",children:t.description})]})]},r)})})]})}function c({ratings:e,totalAreas:r,className:t}){let n=Object.values(e).filter(e=>e>0).length,a=n>0?Object.values(e).reduce((e,r)=>e+r,0)/n:0,o=n/r*100;return s.jsx(i.Zb,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-4",t),children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"font-semibold",children:"Assessment Progress"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:[n," of ",r," areas"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[Math.round(o),"%"]})]}),s.jsx("div",{className:"w-full bg-muted rounded-full h-2",children:s.jsx("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${o}%`}})})]}),a>0&&(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm font-medium",children:"Average Rating"}),s.jsx(l,{rating:Math.round(a),size:"sm",showLabel:!1})]})]})})}},19591:(e,r,t)=>{"use strict";t.d(r,{C:()=>i});var s=t(95344);t(3729);var n=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let a=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...t}){return s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(a({variant:r}),e),...t})}},5094:(e,r,t)=>{"use strict";t.d(r,{z:()=>d});var s=t(95344),n=t(3729),a=t(32751),i=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var o=t(42739);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,loading:i=!1,leftIcon:d,rightIcon:c,children:u,disabled:m,...f},h)=>{let x=n?a.g7:"button",p=m||i;return(0,s.jsxs)(x,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l({variant:r,size:t,className:e})),ref:h,disabled:p,"aria-disabled":p,...f,children:[i&&s.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!i&&d&&s.jsx("span",{className:"mr-2",children:d}),u,!i&&c&&s.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>i,SZ:()=>l,Zb:()=>a,aY:()=>d,ll:()=>o});var s=t(95344),n=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let a=n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));a.displayName="Card";let i=n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let o=n.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let l=n.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let d=n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...r}));d.displayName="CardContent",n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},47210:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(95344),n=t(3729),a=t(47370);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let i=n.forwardRef(({className:e,value:r,...t},n)=>s.jsx(a.fC,{ref:n,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:s.jsx(a.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));i.displayName=a.fC.displayName},33468:(e,r,t)=>{"use strict";t.d(r,{t:()=>a});var s=t(43158),n=t(67023);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let a=(0,s.Ue)()((0,n.tJ)((e,r)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async r=>{e({isLoading:!0,error:null});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.login(r);e({user:t.user,token:t.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:r instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?r.message:"Login failed. Please try again."}),r}},register:async r=>{e({isLoading:!0,error:null});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.register(r);e({user:t.user,token:t.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:r instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?r.message:"Registration failed. Please try again."}),r}},logout:async()=>{e({isLoading:!0});try{await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.logout()}catch(e){}finally{e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}},refreshToken:async()=>{let{token:t}=r();if(!t)throw Error("No token available for refresh");try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.refreshToken();e({user:r.user,token:r.accessToken,isAuthenticated:!0,error:null})}catch(r){throw e({user:null,token:null,isAuthenticated:!1,error:"Session expired. Please login again."}),r}},updateProfile:async r=>{e({isLoading:!0,error:null});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.updateProfile(r);e({user:t,isLoading:!1,error:null})}catch(r){throw e({isLoading:!1,error:r instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?r.message:"Profile update failed. Please try again."}),r}},clearError:()=>{e({error:null})},setLoading:r=>{e({isLoading:r})},initialize:async()=>{let{token:t}=r();if(t){e({isLoading:!0});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.me();e({user:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}}}}),{name:"auth-storage",partialize:e=>({token:e.token,user:e.user}),onRehydrateStorage:()=>e=>{e?.token&&Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e.token)}})),i=null,o=()=>{let{token:e,refreshToken:r,logout:t}=a.getState();if(i&&clearTimeout(i),e)try{let s=JSON.parse(atob(e.split(".")[1])),n=1e3*s.exp,a=Date.now();i=setTimeout(async()=>{try{await r(),o()}catch(e){await t()}},Math.max(n-a-3e5,0))}catch(e){t()}};a.subscribe(e=>e.token,e=>{e?(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e),o()):(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(null),i&&(clearTimeout(i),i=null))})},33734:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let s=(0,t(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(dashboard)/assessment/results/page.tsx`),{__esModule:n,$$typeof:a}=s,i=s.default},95021:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>n});var s=t(25036);let n={title:"Dashboard",description:"Your life planning dashboard - track progress and manage goals"};function a({children:e}){return s.jsx(s.Fragment,{children:e})}},21342:(e,r,t)=>{"use strict";function s({children:e}){return e}t.r(r),t.d(r,{default:()=>s})},98462:(e,r,t)=>{"use strict";t.d(r,{b:()=>i,k:()=>a});var s=t(3729),n=t(95344);function a(e,r){let t=s.createContext(r),a=e=>{let{children:r,...a}=e,i=s.useMemo(()=>a,Object.values(a));return(0,n.jsx)(t.Provider,{value:i,children:r})};return a.displayName=e+"Provider",[a,function(n){let a=s.useContext(t);if(a)return a;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function i(e,r=[]){let t=[],a=()=>{let r=t.map(e=>s.createContext(e));return function(t){let n=t?.[e]||r;return s.useMemo(()=>({[`__scope${e}`]:{...t,[e]:n}}),[t,n])}};return a.scopeName=e,[function(r,a){let i=s.createContext(a),o=t.length;t=[...t,a];let l=r=>{let{scope:t,children:a,...l}=r,d=t?.[e]?.[o]||i,c=s.useMemo(()=>l,Object.values(l));return(0,n.jsx)(d.Provider,{value:c,children:a})};return l.displayName=r+"Provider",[l,function(t,n){let l=n?.[e]?.[o]||i,d=s.useContext(l);if(d)return d;if(void 0!==a)return a;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=t.reduce((r,{useScope:t,scopeName:s})=>{let n=t(e)[`__scope${s}`];return{...r,...n}},{});return s.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return t.scopeName=r.scopeName,t}(a,...r)]}},62409:(e,r,t)=>{"use strict";t.d(r,{WV:()=>o,jH:()=>l});var s=t(3729),n=t(81202),a=t(32751),i=t(95344),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,a.Z8)(`Primitive.${r}`),n=s.forwardRef((e,s)=>{let{asChild:n,...a}=e,o=n?t:r;return(0,i.jsx)(o,{...a,ref:s})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{});function l(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},47370:(e,r,t)=>{"use strict";t.d(r,{fC:()=>j,z$:()=>v});var s=t(3729),n=t(98462),a=t(62409),i=t(95344),o="Progress",[l,d]=(0,n.b)(o),[c,u]=l(o),m=s.forwardRef((e,r)=>{let{__scopeProgress:t,value:s=null,max:n,getValueLabel:o=x,...l}=e;(n||0===n)&&N(n);let d=N(n)?n:100;null!==s&&g(s,d);let u=g(s,d)?s:null,m=b(u)?o(u,d):void 0;return(0,i.jsx)(c,{scope:t,value:u,max:d,children:(0,i.jsx)(a.WV.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":b(u)?u:void 0,"aria-valuetext":m,role:"progressbar","data-state":p(u,d),"data-value":u??void 0,"data-max":d,...l,ref:r})})});m.displayName=o;var f="ProgressIndicator",h=s.forwardRef((e,r)=>{let{__scopeProgress:t,...s}=e,n=u(f,t);return(0,i.jsx)(a.WV.div,{"data-state":p(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...s,ref:r})});function x(e,r){return`${Math.round(e/r*100)}%`}function p(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function b(e){return"number"==typeof e}function N(e){return b(e)&&!isNaN(e)&&e>0}function g(e,r){return b(e)&&!isNaN(e)&&e<=r&&e>=0}h.displayName=f;var j=m,v=h}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,8356,3828,2372],()=>t(43955));module.exports=s})();