(()=>{var e={};e.id=2473,e.ids=[2473],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},65690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=t(50482),l=t(69108),r=t(62563),n=t.n(r),i=t(68300),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let o=["",{children:["(dashboard)",{children:["planning",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,920)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/planning/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,95021)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/(dashboard)/planning/page.tsx"],m="/(dashboard)/planning/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/(dashboard)/planning/page",pathname:"/planning",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},13196:(e,s,t)=>{Promise.resolve().then(t.bind(t,81186))},45961:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},25390:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},12704:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},17418:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},13012:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},20439:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},62093:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},75695:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},57320:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},64989:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},38271:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},81186:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var a=t(95344),l=t(3729),r=t(22254),n=t(23673),i=t(5094),d=t(19591),o=t(60339),c=t(33468),m=t(54243),x=t(47210),u=t(25545),h=t(57320),p=t(7060);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,t(69224).Z)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);var j=t(38271),f=t(45961),N=t(62093),v=t(75695),b=t(64989),y=t(12704),w=t(25390);function k({goal:e,onEdit:s,onDelete:t,onStatusChange:r,onProgressUpdate:o,compact:c=!1,className:m}){let[k,O]=(0,l.useState)(!1),[D,Z]=(0,l.useState)(!1),_=Object(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e}())(e),C=Object(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e}())(e),E=e.targetDate?Object(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e}())(e.targetDate):null,U={not_started:{label:"Not Started",icon:u.Z,color:"#6b7280"},in_progress:{label:"In Progress",icon:h.Z,color:"#3b82f6"},completed:{label:"Completed",icon:p.Z,color:"#22c55e"},paused:{label:"Paused",icon:g,color:"#eab308"},cancelled:{label:"Cancelled",icon:j.Z,color:"#ef4444"}}[e.status],M=U.icon,S={high:{label:"High",color:"bg-red-500"},medium:{label:"Medium",color:"bg-yellow-500"},low:{label:"Low",color:"bg-green-500"}},z={major:{label:"Major Goal",color:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},supporting:{label:"Supporting",color:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},habit:{label:"Habit",color:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},experience:{label:"Experience",color:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"}},T=e=>{r?.(e),Z(!1)};return(0,a.jsxs)(n.Zb,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("transition-all duration-200 hover:shadow-md","completed"===e.status&&"bg-green-50 dark:bg-green-950/20",C&&"completed"!==e.status&&"border-red-200 dark:border-red-800",m),children:[a.jsx(n.Ol,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("pb-3",c&&"pb-2"),children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx(M,{className:"h-4 w-4 flex-shrink-0",style:{color:U.color}}),a.jsx(n.ll,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-lg truncate","completed"===e.status&&"line-through text-muted-foreground"),children:e.title})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx(d.C,{className:z[e.category].color,children:z[e.category].label}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-2 h-2 rounded-full",S[e.priority].color)}),a.jsx("span",{className:"text-xs text-muted-foreground",children:S[e.priority].label})]}),C&&"completed"!==e.status&&(0,a.jsxs)(d.C,{variant:"destructive",className:"text-xs",children:[a.jsx(f.Z,{className:"h-3 w-3 mr-1"}),"Overdue"]})]}),!c&&a.jsx(n.SZ,{className:"line-clamp-2",children:e.description})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[e.targetDate&&(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("div",{className:"text-xs text-muted-foreground",children:"Target"}),a.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm font-medium",C&&"completed"!==e.status&&"text-red-600 dark:text-red-400"),children:e.targetDate.toLocaleDateString()}),null!==E&&"completed"!==e.status&&a.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-xs",E<0?"text-red-600 dark:text-red-400":"text-muted-foreground"),children:E<0?`${Math.abs(E)} days overdue`:`${E} days left`})]}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(i.z,{variant:"ghost",size:"sm",onClick:()=>Z(!D),className:"h-8 w-8 p-0",children:a.jsx(N.Z,{className:"h-4 w-4"})}),D&&(0,a.jsxs)("div",{className:"absolute right-0 top-8 z-10 bg-background border rounded-md shadow-lg p-1 min-w-[120px]",children:[s&&(0,a.jsxs)(i.z,{variant:"ghost",size:"sm",onClick:()=>{s(),Z(!1)},className:"w-full justify-start",children:[a.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),"in_progress"!==e.status&&(0,a.jsxs)(i.z,{variant:"ghost",size:"sm",onClick:()=>T("in_progress"),className:"w-full justify-start",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Start"]}),"in_progress"===e.status&&(0,a.jsxs)(i.z,{variant:"ghost",size:"sm",onClick:()=>T("paused"),className:"w-full justify-start",children:[a.jsx(g,{className:"h-4 w-4 mr-2"}),"Pause"]}),"completed"!==e.status&&(0,a.jsxs)(i.z,{variant:"ghost",size:"sm",onClick:()=>T("completed"),className:"w-full justify-start",children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Complete"]}),"completed"===e.status&&(0,a.jsxs)(i.z,{variant:"ghost",size:"sm",onClick:()=>T("in_progress"),className:"w-full justify-start",children:[a.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Reopen"]}),t&&(0,a.jsxs)(i.z,{variant:"ghost",size:"sm",onClick:()=>{t(),Z(!1)},className:"w-full justify-start text-destructive hover:text-destructive",children:[a.jsx(j.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium",children:"Progress"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[_,"%"]})]}),a.jsx(x.E,{value:_,className:"h-2"})]}),a.jsx("div",{className:"flex flex-wrap gap-1",children:e.lifeAreas.map(e=>{let s=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}())(e);return s?(s.icon,(0,a.jsxs)(d.C,{variant:"outline",className:"text-xs",children:[a.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-2 h-2 rounded-full mr-1",s.bgColor)}),s.shortName]},e)):null})}),e.tags.length>0&&a.jsx("div",{className:"flex flex-wrap gap-1",children:e.tags.map(e=>a.jsx(d.C,{variant:"secondary",className:"text-xs",children:e},e))}),!c&&(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(i.z,{variant:"ghost",size:"sm",onClick:()=>O(!k),className:"p-0 h-auto font-medium text-primary hover:text-primary/80",children:k?(0,a.jsxs)(a.Fragment,{children:[a.jsx(y.Z,{className:"h-4 w-4 mr-1"}),"Hide Details"]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(w.Z,{className:"h-4 w-4 mr-1"}),"Show Details"]})}),k&&(0,a.jsxs)("div",{className:"space-y-4 pt-2 border-t",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-muted-foreground",children:"Specific"}),a.jsx("div",{children:e.specific})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-muted-foreground",children:"Measurable"}),a.jsx("div",{children:e.measurable})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-muted-foreground",children:"Achievable"}),a.jsx("div",{children:e.achievable})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-muted-foreground",children:"Relevant"}),a.jsx("div",{children:e.relevant})]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("div",{className:"font-medium text-muted-foreground",children:"Time-Bound"}),a.jsx("div",{children:e.timeBound})]}),e.actionItems.length>0&&(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("div",{className:"font-medium text-muted-foreground mb-1",children:"Action Items"}),(0,a.jsxs)("div",{className:"text-muted-foreground",children:[e.actionItems.filter(e=>"completed"===e.status).length," of ",e.actionItems.length," completed"]})]}),e.milestones.length>0&&(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("div",{className:"font-medium text-muted-foreground mb-1",children:"Milestones"}),(0,a.jsxs)("div",{className:"text-muted-foreground",children:[e.milestones.filter(e=>"completed"===e.status).length," of ",e.milestones.length," completed"]})]}),e.notes&&(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("div",{className:"font-medium text-muted-foreground mb-1",children:"Notes"}),a.jsx("div",{className:"text-muted-foreground",children:e.notes})]})]})]})]})]})}(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var O=t(51838),D=t(17910),Z=t(46064),_=t(35341),C=t(17418),E=t(13012),U=t(20439);function M(){let{user:e,isAuthenticated:s,isLoading:t}=(0,c.t)(),x=(0,r.useRouter)(),{toast:h}=(0,o.pm)(),[g,j]=(0,l.useState)({goals:[],year:new Date().getFullYear(),lastUpdated:new Date}),[N,v]=(0,l.useState)(!1),[b,y]=(0,l.useState)(null),[w,M]=(0,l.useState)(null),[S,z]=(0,l.useState)("grid"),[T,P]=(0,l.useState)("all"),[F,L]=(0,l.useState)("all");(0,l.useEffect)(()=>{t||s||x.push("/login")},[s,t,x]),(0,l.useEffect)(()=>{let e=localStorage.getItem("planning-data");if(e)try{let s=JSON.parse(e),t=s.goals.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),targetDate:e.targetDate?new Date(e.targetDate):void 0,completedDate:e.completedDate?new Date(e.completedDate):void 0}));j({...s,goals:t,lastUpdated:new Date(s.lastUpdated)})}catch(e){}},[]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{localStorage.setItem("planning-data",JSON.stringify(g))},2e3);return()=>clearTimeout(e)},[g]);let G=e=>{j(s=>({...s,goals:s.goals.filter(s=>s.id!==e),lastUpdated:new Date})),h({title:"Goal Deleted",description:"The goal has been deleted successfully."})},q=(e,s)=>{j(t=>({...t,goals:t.goals.map(t=>t.id===e?{...t,status:s,completedDate:"completed"===s?new Date:void 0,updatedAt:new Date}:t),lastUpdated:new Date}));let t=g.goals.find(s=>s.id===e);t&&h({title:"Goal Status Updated",description:`"${t.title}" is now ${s.replace("_"," ")}.`})},A=e=>{M(e),v(!0)},I=g.goals.filter(e=>("all"===T||e.status===T)&&("all"===F||e.category===F)),Y={total:g.goals.length,completed:g.goals.filter(e=>"completed"===e.status).length,inProgress:g.goals.filter(e=>"in_progress"===e.status).length,overdue:g.goals.filter(e=>e.targetDate&&new Date>e.targetDate&&"completed"!==e.status).length};return t?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):s&&e?N?(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[a.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,a.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(u.Z,{className:"h-6 w-6"}),a.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-sm text-muted-foreground",children:e.firstName||e.email}),a.jsx(i.z,{variant:"outline",size:"sm",onClick:()=>c.t.getState().logout(),children:"Sign Out"})]})]})}),a.jsx("main",{className:"container py-8",children:a.jsx(m.Y,{goal:b||void 0,template:w||void 0,onSave:e=>{j(s=>{let t=s.goals.findIndex(s=>s.id===e.id),a=t>=0?s.goals.map((s,a)=>a===t?e:s):[...s.goals,e];return{...s,goals:a,lastUpdated:new Date}}),h({title:b?"Goal Updated":"Goal Created",description:`"${e.title}" has been ${b?"updated":"created"} successfully.`}),v(!1),y(null),M(null)},onCancel:()=>{v(!1),y(null),M(null)}})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[a.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,a.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(u.Z,{className:"h-6 w-6"}),a.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-sm text-muted-foreground",children:e.firstName||e.email}),a.jsx(i.z,{variant:"outline",size:"sm",onClick:()=>c.t.getState().logout(),children:"Sign Out"})]})]})}),a.jsx("main",{className:"container py-8",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:[g.year," Planning Session"]}),a.jsx("p",{className:"text-muted-foreground",children:"Set meaningful goals and create action plans for the year ahead"})]}),(0,a.jsxs)(i.z,{onClick:()=>v(!0),children:[a.jsx(O.Z,{className:"mr-2 h-4 w-4"}),"New Goal"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Total Goals"}),a.jsx("p",{className:"text-2xl font-bold",children:Y.total})]}),a.jsx(D.Z,{className:"h-8 w-8 text-muted-foreground"})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Completed"}),a.jsx("p",{className:"text-2xl font-bold",children:Y.completed})]}),a.jsx(p.Z,{className:"h-8 w-8 text-green-500"})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"In Progress"}),a.jsx("p",{className:"text-2xl font-bold",children:Y.inProgress})]}),a.jsx(Z.Z,{className:"h-8 w-8 text-blue-500"})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Overdue"}),a.jsx("p",{className:"text-2xl font-bold",children:Y.overdue})]}),a.jsx(f.Z,{className:"h-8 w-8 text-red-500"})]})})})]}),0===g.goals.length&&(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(_.Z,{className:"mr-2 h-5 w-5"}),"Goal Templates"]}),a.jsx(n.SZ,{children:"Get started with these proven goal templates"})]}),a.jsx(n.aY,{children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Object(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>(0,a.jsxs)(n.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",children:[(0,a.jsxs)(n.Ol,{className:"pb-3",children:[a.jsx(n.ll,{className:"text-base",children:e.title}),a.jsx(n.SZ,{className:"text-sm",children:e.description})]}),(0,a.jsxs)(n.aY,{className:"pt-0",children:[a.jsx("div",{className:"flex flex-wrap gap-1 mb-3",children:e.lifeAreas.map(e=>{let s=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).find(s=>s.id===e);return s?a.jsx(d.C,{variant:"outline",className:"text-xs",children:s.shortName},e):null})}),a.jsx(i.z,{size:"sm",className:"w-full",onClick:()=>A(e),children:"Use Template"})]})]},e.id))})})]}),g.goals.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(C.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("select",{value:T,onChange:e=>P(e.target.value),className:"border border-input bg-background px-3 py-1 rounded-md text-sm",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"not_started",children:"Not Started"}),a.jsx("option",{value:"in_progress",children:"In Progress"}),a.jsx("option",{value:"completed",children:"Completed"}),a.jsx("option",{value:"paused",children:"Paused"})]})]}),(0,a.jsxs)("select",{value:F,onChange:e=>L(e.target.value),className:"border border-input bg-background px-3 py-1 rounded-md text-sm",children:[a.jsx("option",{value:"all",children:"All Categories"}),a.jsx("option",{value:"major",children:"Major Goals"}),a.jsx("option",{value:"supporting",children:"Supporting Goals"}),a.jsx("option",{value:"habit",children:"Habit Goals"}),a.jsx("option",{value:"experience",children:"Experience Goals"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(i.z,{variant:"grid"===S?"default":"outline",size:"sm",onClick:()=>z("grid"),children:a.jsx(E.Z,{className:"h-4 w-4"})}),a.jsx(i.z,{variant:"list"===S?"default":"outline",size:"sm",onClick:()=>z("list"),children:a.jsx(U.Z,{className:"h-4 w-4"})})]})]}),a.jsx("div",{className:`grid gap-6 ${"grid"===S?"grid-cols-1 lg:grid-cols-2":"grid-cols-1"}`,children:I.map(e=>a.jsx(k,{goal:e,onEdit:()=>{y(e),v(!0)},onDelete:()=>G(e.id),onStatusChange:s=>q(e.id,s),compact:"list"===S},e.id))}),0===I.length&&a.jsx(n.Zb,{children:(0,a.jsxs)(n.aY,{className:"p-8 text-center",children:[a.jsx(D.Z,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No goals match your filters"}),a.jsx("p",{className:"text-muted-foreground mb-4",children:"Try adjusting your filters or create a new goal"}),(0,a.jsxs)(i.z,{onClick:()=>v(!0),children:[a.jsx(O.Z,{className:"mr-2 h-4 w-4"}),"Create Goal"]})]})})]})]})})]}):null}(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()},920:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>l,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(dashboard)/planning/page.tsx`),{__esModule:l,$$typeof:r}=a,n=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,8356,3828,2372,28,839],()=>t(65690));module.exports=a})();