(()=>{var e={};e.id=4219,e.ids=[4219],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16350:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o});var t=a(50482),l=a(69108),i=a(62563),n=a.n(i),r=a(68300),c={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>r[e]);a.d(s,c);let o=["",{children:["(dashboard)",{children:["planning",{children:["session",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,11968)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/planning/session/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,95021)),"/mnt/persist/workspace/frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/(dashboard)/planning/session/page.tsx"],m="/(dashboard)/planning/session/page",x={require:a,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/(dashboard)/planning/session/page",pathname:"/planning/session",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},83374:(e,s,a)=>{Promise.resolve().then(a.bind(a,47218))},63024:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35299:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},50340:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55794:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},51354:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},31498:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},76755:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},47218:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>_});var t=a(95344),l=a(3729),i=a(22254),n=a(23673),r=a(5094),c=a(47210),o=a(19591),d=a(60339),m=a(33468),x=a(46540),u=a(33668),h=a(2690),p=a(53148),g=a(76755),j=a(51354),v=a(14513),f=a(51838),N=a(55794),y=a(17910),b=a(35341),w=a(31498);function k({vision:e,onSave:s,onCancel:a,className:i}){let[c,d]=(0,l.useState)({overallVision:e?.overallVision||"",lifeAreaVisions:e?.lifeAreaVisions||{},coreValues:e?.coreValues||[],idealDay:e?.idealDay||"",legacyStatement:e?.legacyStatement||""}),[m,k]=(0,l.useState)(""),[C,Z]=(0,l.useState)(null),S=e=>{d(s=>({...s,overallVision:e}))},V=(e,s)=>{d(a=>({...a,lifeAreaVisions:{...a.lifeAreaVisions,[e]:s}}))},O=()=>{m.trim()&&!c.coreValues?.includes(m.trim())&&(d(e=>({...e,coreValues:[...e.coreValues||[],m.trim()]})),k(""))},D=e=>{d(s=>({...s,coreValues:s.coreValues?.filter(s=>s!==e)||[]}))},_=e=>{d(s=>({...s,idealDay:e}))},A=e=>{d(s=>({...s,legacyStatement:e}))},z=c.overallVision&&c.overallVision.length>20;return(0,t.jsxs)(n.Zb,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("max-w-4xl mx-auto",i),children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(p.Z,{className:"mr-2 h-5 w-5"}),"Future Vision & Values"]}),t.jsx(n.SZ,{children:"Create a compelling vision of your ideal future and clarify your core values"})]}),(0,t.jsxs)(n.aY,{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(g.Z,{className:"h-5 w-5 text-primary"}),t.jsx(u._,{htmlFor:"overallVision",className:"text-base font-semibold",children:"Overall Life Vision"})]}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Describe your ideal life 5-10 years from now. What does success look like? How do you want to feel? What impact do you want to have?"}),t.jsx(h.g,{id:"overallVision",placeholder:"In 5-10 years, I envision myself living a life where...",value:c.overallVision,onChange:e=>S(e.target.value),rows:6,className:"resize-none"}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[c.overallVision?.length||0," characters (minimum 20 recommended)"]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(j.Z,{className:"h-5 w-5 text-primary"}),t.jsx(u._,{className:"text-base font-semibold",children:"Core Values"})]}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"What principles and values are most important to you? These should guide your decisions and goals."}),t.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:c.coreValues?.map(e=>t.jsxs(o.C,{variant:"secondary",className:"flex items-center space-x-1",children:[t.jsx("span",{children:e}),t.jsx("button",{type:"button",onClick:()=>D(e),className:"ml-1 hover:text-destructive",children:t.jsx(v.Z,{className:"h-3 w-3"})})]},e))}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx(x.I,{placeholder:"Add a core value (e.g., Integrity, Growth, Family)",value:m,onChange:e=>k(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),O())}),t.jsx(r.z,{type:"button",variant:"outline",onClick:O,children:t.jsx(f.Z,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("p",{className:"text-sm font-medium",children:"Suggested Values:"}),t.jsx("div",{className:"flex flex-wrap gap-2",children:["Integrity","Growth","Family","Health","Creativity","Adventure","Service","Excellence","Freedom","Connection","Learning","Impact"].filter(e=>!c.coreValues?.includes(e)).map(e=>(0,t.jsxs)(r.z,{variant:"outline",size:"sm",onClick:()=>{d(s=>({...s,coreValues:[...s.coreValues||[],e]}))},className:"text-xs",children:["+ ",e]},e))})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(N.Z,{className:"h-5 w-5 text-primary"}),t.jsx(u._,{htmlFor:"idealDay",className:"text-base font-semibold",children:"Ideal Day Description"})]}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Describe your perfect day from morning to evening. What activities energize you? How do you spend your time?"}),t.jsx(h.g,{id:"idealDay",placeholder:"My ideal day starts with... and includes...",value:c.idealDay,onChange:e=>_(e.target.value),rows:4,className:"resize-none"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(y.Z,{className:"h-5 w-5 text-primary"}),t.jsx(u._,{htmlFor:"legacyStatement",className:"text-base font-semibold",children:"Legacy Statement"})]}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"How do you want to be remembered? What impact do you want to have on others and the world?"}),t.jsx(h.g,{id:"legacyStatement",placeholder:"I want to be remembered as someone who...",value:c.legacyStatement,onChange:e=>A(e.target.value),rows:3,className:"resize-none"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(b.Z,{className:"h-5 w-5 text-primary"}),t.jsx("h3",{className:"text-base font-semibold",children:"Life Area Visions"})]}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Describe your vision for each life area. What does success look like in each area?"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>{let s=e.icon,a=C===e.id;return(0,t.jsxs)(n.Zb,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("cursor-pointer transition-all",a&&"ring-2 ring-primary"),onClick:()=>Z(a?null:e.id),children:[t.jsx(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-1 rounded",e.bgColor),children:t.jsx(s,{className:"h-4 w-4 text-white"})}),t.jsx(n.ll,{className:"text-sm",children:e.name})]})}),a&&t.jsx(n.aY,{className:"pt-0",children:t.jsx(h.g,{placeholder:`Describe your vision for ${e.name.toLowerCase()}...`,value:c.lifeAreaVisions?.[e.id]||"",onChange:s=>V(e.id,s.target.value),rows:3,className:"resize-none",onClick:e=>e.stopPropagation()})}),!a&&c.lifeAreaVisions?.[e.id]&&t.jsx(n.aY,{className:"pt-0",children:t.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2",children:c.lifeAreaVisions[e.id]})})]},e.id)})})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4 border-t",children:[t.jsx(r.z,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,t.jsxs)(r.z,{onClick:()=>{s({...c,completedAt:new Date})},disabled:!z,children:[t.jsx(w.Z,{className:"mr-2 h-4 w-4"}),"Save Vision"]})]})]})]})}(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var C=a(54243),Z=a(50340),S=a(7060),V=a(25545),O=a(63024),D=a(35299);function _(){let{user:e,isAuthenticated:s,isLoading:a}=(0,m.t)(),x=(0,i.useRouter)(),{toast:u}=(0,d.pm)(),[h,g]=(0,l.useState)({goals:[],currentStep:"assessment-review",startedAt:new Date}),[j,v]=(0,l.useState)(!1),[f,N]=(0,l.useState)(null);(0,l.useEffect)(()=>{a||s||x.push("/login")},[s,a,x]),(0,l.useEffect)(()=>{let e=localStorage.getItem("assessment-data");if(e)try{let s=JSON.parse(e);g(e=>({...e,assessmentData:s}))}catch(e){}},[]);let w=[{id:"assessment-review",title:"Assessment Review",description:"Review your life areas assessment",icon:Z.Z,completed:!!h.assessmentData},{id:"vision",title:"Future Vision",description:"Create your vision and values",icon:p.Z,completed:!!h.vision?.overallVision},{id:"goal-setting",title:"Goal Setting",description:"Set SMART goals for the year",icon:y.Z,completed:h.goals.length>=3},{id:"review",title:"Review & Finalize",description:"Review your complete plan",icon:S.Z,completed:!1}],_=w.findIndex(e=>e.id===h.currentStep),A=(_+1)/w.length*100,z=()=>{let e=_+1,s=w[e];e<w.length&&s?g(e=>({...e,currentStep:s.id})):E()},E=()=>{let e={...Object(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e}())(new Date().getFullYear()),futureVision:h.vision,goals:h.goals,status:"active",createdAt:h.startedAt,updatedAt:new Date};localStorage.setItem("current-planning-session",JSON.stringify(e)),u({title:"Planning Session Complete!",description:"Your annual planning session has been saved successfully."}),x.push("/planning")},F=e=>{g(s=>({...s,vision:e})),u({title:"Vision Saved",description:"Your future vision has been saved successfully."}),z()},M=e=>{g(s=>{let a=null!==f?s.goals.map((s,a)=>a===f?e:s):[...s.goals,e];return{...s,goals:a}}),u({title:null!==f?"Goal Updated":"Goal Added",description:`"${e.title}" has been ${null!==f?"updated":"added"} to your plan.`}),v(!1),N(null)},P=e=>{g(s=>({...s,goals:s.goals.filter((s,a)=>a!==e)})),u({title:"Goal Removed",description:"The goal has been removed from your plan."})};return a?t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):s&&e?(0,t.jsxs)("div",{className:"min-h-screen bg-background",children:[t.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(V.Z,{className:"h-6 w-6"}),t.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("span",{className:"text-sm text-muted-foreground",children:e.firstName||e.email}),t.jsx(r.z,{variant:"outline",size:"sm",onClick:()=>m.t.getState().logout(),children:"Sign Out"})]})]})}),t.jsx("main",{className:"container py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Annual Planning Session"}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Create your comprehensive life plan for ",new Date().getFullYear()]})]}),(0,t.jsxs)(r.z,{variant:"outline",onClick:()=>x.push("/planning"),children:[t.jsx(O.Z,{className:"mr-2 h-4 w-4"}),"Exit Session"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[Math.round(A),"% Complete"]})]}),t.jsx(c.E,{value:A,className:"h-2"})]}),t.jsx("div",{className:"flex items-center justify-between",children:w.map((e,s)=>{let a=e.icon,l=e.id===h.currentStep,i=e.completed;return(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("div",{className:`flex items-center space-x-2 ${l?"text-primary":i?"text-green-600":"text-muted-foreground"}`,children:[t.jsx("div",{className:`p-2 rounded-full ${l?"bg-primary text-primary-foreground":i?"bg-green-600 text-white":"bg-muted"}`,children:t.jsx(a,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"hidden md:block",children:[t.jsx("div",{className:"font-medium text-sm",children:e.title}),t.jsx("div",{className:"text-xs text-muted-foreground",children:e.description})]})]}),s<w.length-1&&t.jsx("div",{className:"w-8 h-px bg-muted mx-4"})]},e.id)})})]}),(()=>{switch(h.currentStep){case"assessment-review":return(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(Z.Z,{className:"mr-2 h-5 w-5"}),"Assessment Review"]}),t.jsx(n.SZ,{children:"Let's review your life areas assessment to inform your planning"})]}),t.jsx(n.aY,{className:"space-y-6",children:h.assessmentData?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold",children:Object.values(h.assessmentData.ratings).filter(e=>e>0).length}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Areas Assessed"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold",children:(Object.values(h.assessmentData.ratings).reduce((e,s)=>e+s,0)/Object.values(h.assessmentData.ratings).filter(e=>e>0).length||0).toFixed(1)}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Average Rating"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold",children:Object.values(h.assessmentData.ratings).filter(e=>e<=3&&e>0).length}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Improvement Areas"})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx("h4",{className:"font-medium",children:"Key Insights for Planning:"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[t.jsx("li",{children:"• Focus on areas with ratings of 3 or below for maximum impact"}),t.jsx("li",{children:"• Leverage your strong areas (5+ ratings) to support growth in weaker areas"}),t.jsx("li",{children:"• Consider how different life areas connect and influence each other"}),t.jsx("li",{children:"• Set realistic goals that address your most important improvement areas"})]})]})]}):(0,t.jsxs)("div",{className:"text-center space-y-4",children:[t.jsx("p",{className:"text-muted-foreground",children:"No assessment data found. We recommend completing your life areas assessment first."}),t.jsx(r.z,{onClick:()=>x.push("/assessment"),children:"Complete Assessment"})]})})]});case"vision":return t.jsx(k,{vision:h.vision,onSave:F,onCancel:()=>x.push("/planning")});case"goal-setting":if(j)return t.jsx(C.Y,{goal:null!==f?h.goals[f]:void 0,onSave:M,onCancel:()=>{v(!1),N(null)}});return(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(y.Z,{className:"mr-2 h-5 w-5"}),"Goal Setting"]}),t.jsx(n.SZ,{children:"Create 3-7 meaningful goals for the year ahead"})]}),(0,t.jsxs)(n.aY,{className:"space-y-6",children:[0===h.goals.length&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center",children:[t.jsx(b.Z,{className:"mr-2 h-4 w-4"}),"Recommended Goal Templates"]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Object(function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e}()).slice(0,3).map(e=>(0,t.jsxs)(n.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",children:[(0,t.jsxs)(n.Ol,{className:"pb-3",children:[t.jsx(n.ll,{className:"text-sm",children:e.title}),t.jsx(n.SZ,{className:"text-xs",children:e.description})]}),t.jsx(n.aY,{className:"pt-0",children:t.jsx(r.z,{size:"sm",className:"w-full",onClick:()=>{M({id:`goal-${Date.now()}`,title:e.title,description:e.description,category:e.category,priority:"medium",specific:e.smartTemplate.specific,measurable:e.smartTemplate.measurable,achievable:e.smartTemplate.achievable,relevant:e.smartTemplate.relevant,timeBound:e.smartTemplate.timeBound,lifeAreas:e.lifeAreas,status:"not_started",progress:0,actionItems:[],milestones:[],tags:e.tags,notes:"",createdAt:new Date,updatedAt:new Date})},children:"Use Template"})})]},e.id))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"font-medium",children:["Your Goals (",h.goals.length,")"]}),(0,t.jsxs)(r.z,{onClick:()=>v(!0),children:[t.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Add Goal"]})]}),h.goals.length>0?t.jsx("div",{className:"space-y-3",children:h.goals.map((e,s)=>t.jsx(n.Zb,{children:t.jsx(n.aY,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("h5",{className:"font-medium",children:e.title}),t.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[t.jsx(o.C,{variant:"outline",children:e.category}),(0,t.jsxs)(o.C,{variant:"outline",children:[e.priority," priority"]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2 ml-4",children:[t.jsx(r.z,{variant:"outline",size:"sm",onClick:()=>{N(s),v(!0)},children:"Edit"}),t.jsx(r.z,{variant:"outline",size:"sm",onClick:()=>P(s),children:"Remove"})]})]})})},s))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx(y.Z,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No goals yet"}),t.jsx("p",{className:"text-muted-foreground mb-4",children:"Start by adding your first goal for the year"}),(0,t.jsxs)(r.z,{onClick:()=>v(!0),children:[t.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Create First Goal"]})]})]}),h.goals.length>=3&&(0,t.jsxs)("div",{className:"bg-green-50 dark:bg-green-950/20 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(S.Z,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("span",{className:"font-medium text-green-800 dark:text-green-200",children:["Great! You have ",h.goals.length," goals ready."]})]}),t.jsx("p",{className:"text-sm text-green-600 dark:text-green-300 mt-1",children:"You can continue to the review step or add more goals if needed."})]})]})]});case"review":return(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[t.jsx(S.Z,{className:"mr-2 h-5 w-5"}),"Review & Finalize"]}),t.jsx(n.SZ,{children:"Review your complete planning session before finalizing"})]}),(0,t.jsxs)(n.aY,{className:"space-y-6",children:[h.vision?.overallVision&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center",children:[t.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"Your Vision"]}),t.jsx(n.Zb,{className:"bg-muted/50",children:(0,t.jsxs)(n.aY,{className:"p-4",children:[t.jsx("p",{className:"text-sm",children:h.vision.overallVision}),h.vision.coreValues&&h.vision.coreValues.length>0&&(0,t.jsxs)("div",{className:"mt-3",children:[t.jsx("p",{className:"text-xs font-medium text-muted-foreground mb-2",children:"Core Values:"}),t.jsx("div",{className:"flex flex-wrap gap-1",children:h.vision.coreValues.map(e=>t.jsx(o.C,{variant:"secondary",className:"text-xs",children:e},e))})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center",children:[t.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Your Goals (",h.goals.length,")"]}),t.jsx("div",{className:"space-y-2",children:h.goals.map((e,s)=>t.jsx(n.Zb,{className:"bg-muted/50",children:t.jsx(n.aY,{className:"p-4",children:t.jsx("div",{className:"flex items-start justify-between",children:(0,t.jsxs)("div",{children:[t.jsx("h5",{className:"font-medium",children:e.title}),t.jsx("p",{className:"text-sm text-muted-foreground",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[t.jsx(o.C,{variant:"outline",children:e.category}),(0,t.jsxs)(o.C,{variant:"outline",children:[e.priority," priority"]})]})]})})})},s))})]}),(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg",children:[t.jsx("h4",{className:"font-medium text-blue-800 dark:text-blue-200 mb-2",children:"What happens next?"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-600 dark:text-blue-300 space-y-1",children:[t.jsx("li",{children:"• Your planning session will be saved and activated"}),t.jsx("li",{children:"• You can start working on your goals immediately"}),t.jsx("li",{children:"• Track progress and update goals throughout the year"}),t.jsx("li",{children:"• Schedule regular reviews to stay on track"})]})]})]})]});default:return null}})(),(0,t.jsxs)("div",{className:"flex justify-between pt-4 border-t",children:[(0,t.jsxs)(r.z,{variant:"outline",onClick:()=>{let e=_-1,s=w[e];e>=0&&s&&g(e=>({...e,currentStep:s.id}))},disabled:0===_,children:[t.jsx(O.Z,{className:"mr-2 h-4 w-4"}),"Previous"]}),(0,t.jsxs)(r.z,{onClick:z,disabled:"assessment-review"===h.currentStep&&!h.assessmentData||"vision"===h.currentStep&&!h.vision?.overallVision||"goal-setting"===h.currentStep&&h.goals.length<3,children:[_===w.length-1?"Complete Session":"Next",t.jsx(D.Z,{className:"ml-2 h-4 w-4"})]})]})]})})]}):null}!function(){var e=Error("Cannot find module '@/lib/planning'");throw e.code="MODULE_NOT_FOUND",e}()},11968:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});let t=(0,a(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/src/app/(dashboard)/planning/session/page.tsx`),{__esModule:l,$$typeof:i}=t,n=t.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,8356,3828,2372,28,839],()=>a(16350));module.exports=t})();