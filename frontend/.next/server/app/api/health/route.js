"use strict";(()=>{var e={};e.id=8829,e.ids=[8829],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60198:(e,t,n)=>{n.r(t),n.d(t,{headerHooks:()=>w,originalPathname:()=>g,patchFetch:()=>y,requestAsyncStorage:()=>m,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>O});var r={};n.r(r),n.d(r,{GET:()=>p});var o=n(95419),a=n(69108),i=n(99678),s=n(78070);!function(){var e=Error("Cannot find module '@/lib/service-urls'");throw e.code="MODULE_NOT_FOUND",e}();let u=Date.now();async function c(){try{if(Object(function(){var e=Error("Cannot find module '@/lib/service-urls'");throw e.code="MODULE_NOT_FOUND",e}()).getServiceUrl("database"))return"connected";return"unknown"}catch(e){return"disconnected"}}async function d(){try{if(Object(function(){var e=Error("Cannot find module '@/lib/service-urls'");throw e.code="MODULE_NOT_FOUND",e}()).getServiceUrl("redis"))return"connected";return"unknown"}catch(e){return"disconnected"}}function l(){if("undefined"!=typeof process&&process.memoryUsage){let e=process.memoryUsage();return{used:Math.round(e.heapUsed/1024/1024),total:Math.round(e.heapTotal/1024/1024),percentage:Math.round(e.heapUsed/e.heapTotal*100)}}return{used:0,total:0,percentage:0}}async function p(e){try{let e=Object(function(){var e=Error("Cannot find module '@/lib/service-urls'");throw e.code="MODULE_NOT_FOUND",e}()).getEnvironmentInfo(),[t,n]=await Promise.all([c(),d()]),r=Math.floor((Date.now()-u)/1e3),o=l(),a="healthy";"disconnected"===t?a="unhealthy":("unknown"===t||"disconnected"===n)&&(a="degraded");let i={status:a,timestamp:new Date().toISOString(),environment:e.environment,version:process.env.npm_package_version||"1.0.0",services:{database:t,redis:n,api:"operational"},uptime:r,memory:o},p="healthy"===a?200:"degraded"===a?200:503;return s.Z.json(i,{status:p})}catch(t){let e={status:"unhealthy",timestamp:new Date().toISOString(),environment:"unknown",version:"unknown",services:{database:"unknown",redis:"unknown",api:"error"},uptime:Math.floor((Date.now()-u)/1e3),memory:l()};return s.Z.json(e,{status:503})}}let h=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/mnt/persist/workspace/frontend/src/app/api/health/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:v,serverHooks:f,headerHooks:w,staticGenerationBailout:O}=h,g="/api/health/route";function y(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:v})}}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1638,6206],()=>n(60198));module.exports=r})();