"use strict";(()=>{var e={};e.id=5945,e.ids=[5945],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60956:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>c,originalPathname:()=>v,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>m});var r={};a.r(r),a.d(r,{GET:()=>u});var s=a(95419),i=a(69108),n=a(99678),o=a(78070);async function u(e){try{return o.Z.json({status:"alive",timestamp:new Date().toISOString(),message:"8,760 Hours platform is alive and responding"},{status:200})}catch(e){return o.Z.json({status:"dead",timestamp:new Date().toISOString(),message:"Liveness check failed"},{status:503})}}let p=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/health/live/route",pathname:"/api/health/live",filename:"route",bundlePath:"app/api/health/live/route"},resolvedPagePath:"/mnt/persist/workspace/frontend/src/app/api/health/live/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:h,headerHooks:c,staticGenerationBailout:m}=p,v="/api/health/live/route";function g(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206],()=>a(60956));module.exports=r})();