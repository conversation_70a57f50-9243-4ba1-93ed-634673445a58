"use strict";(()=>{var e={};e.id=7758,e.ids=[7758],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},81902:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>l,originalPathname:()=>y,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>c,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>m});var a={};r.r(a),r.d(a,{GET:()=>u});var s=r(95419),o=r(69108),n=r(99678),i=r(78070);async function u(e){try{return i.Z.json({status:"ready",timestamp:new Date().toISOString(),message:"8,760 Hours platform is ready to serve requests"},{status:200})}catch(e){return i.Z.json({status:"error",timestamp:new Date().toISOString(),message:"Readiness check failed"},{status:503})}}let p=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/health/ready/route",pathname:"/api/health/ready",filename:"route",bundlePath:"app/api/health/ready/route"},resolvedPagePath:"/mnt/persist/workspace/frontend/src/app/api/health/ready/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:c,headerHooks:l,staticGenerationBailout:m}=p,y="/api/health/ready/route";function g(){return(0,n.patchFetch)({serverHooks:c,staticGenerationAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206],()=>r(81902));module.exports=a})();