(()=>{var e={};e.id=4382,e.ids=[4382],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},99648:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),i=t.n(l),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["[locale]",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,60900)),"/mnt/persist/workspace/frontend/src/app/[locale]/analytics/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["/mnt/persist/workspace/frontend/src/app/[locale]/analytics/page.tsx"],x="/[locale]/analytics/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/analytics/page",pathname:"/[locale]/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},30743:(e,s,t)=>{Promise.resolve().then(t.bind(t,92)),Promise.resolve().then(t.bind(t,86062))},5700:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]])},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51354:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},35341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},79200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},92:(e,s,t)=>{"use strict";t.r(s),t.d(s,{AnalyticsDashboard:()=>P});var a=t(95344),r=t(3729),l=t(68962),i=t(23673),n=t(5094),c=t(19591),d=t(51467),o=t(38157);function x({data:e}){return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-6 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded"}),a.jsx("span",{children:"Current"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-3 h-3 bg-gray-300 rounded"}),a.jsx("span",{children:"Previous"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-3 h-3 bg-green-500 rounded"}),a.jsx("span",{children:"Target"})]})]}),a.jsx("div",{className:"space-y-3",children:e.slice(0,8).map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-gray-900 w-32 truncate",children:e.area}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:[e.current,"/7"]}),a.jsx(c.C,{variant:"up"===e.trend?"default":"down"===e.trend?"destructive":"secondary",className:"text-xs",children:"up"===e.trend?"↗":"down"===e.trend?"↘":"→"})]})]}),(0,a.jsxs)("div",{className:"relative h-6 bg-gray-100 rounded-full overflow-hidden",children:[a.jsx("div",{className:"absolute top-0 left-0 h-full bg-gray-300 rounded-full transition-all duration-500",style:{width:`${e.previous/7*100}%`}}),a.jsx("div",{className:"absolute top-0 left-0 h-full bg-blue-500 rounded-full transition-all duration-700 delay-200",style:{width:`${e.current/7*100}%`}}),a.jsx("div",{className:"absolute top-0 h-full w-1 bg-green-500 transition-all duration-500 delay-400",style:{left:`${e.target/7*100}%`}}),(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-between px-2 text-xs font-medium text-white",children:[a.jsx("span",{children:e.current}),(0,a.jsxs)("span",{className:"text-green-200",children:["Target: ",e.target]})]})]})]},s))}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"text-lg font-bold text-blue-600",children:(e.reduce((e,s)=>e+s.current,0)/e.length).toFixed(1)}),a.jsx("div",{className:"text-xs text-blue-600",children:"Average Current"})]}),(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[a.jsx("div",{className:"text-lg font-bold text-green-600",children:e.filter(e=>"up"===e.trend).length}),a.jsx("div",{className:"text-xs text-green-600",children:"Improving Areas"})]}),(0,a.jsxs)("div",{className:"p-3 bg-purple-50 rounded-lg",children:[a.jsx("div",{className:"text-lg font-bold text-purple-600",children:e.filter(e=>e.current>=e.target).length}),a.jsx("div",{className:"text-xs text-purple-600",children:"Targets Met"})]})]})]})}function m({data:e}){let s=Math.min(...e.map(e=>e.average)),t=Math.max(...e.map(e=>e.average)),r=(e=>{let a=320/(e.length-1);return e.map((e,r)=>{let l=160-(e.average-s)/(t-s)*120;return`${0===r?"M":"L"} ${40+r*a} ${l}`}).join(" ")})(e),l=320/(e.length-1);return(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"relative",children:(0,a.jsxs)("svg",{width:400,height:200,className:"overflow-visible",children:[[1,2,3,4,5,6,7].map(e=>(0,a.jsxs)("g",{children:[a.jsx("line",{x1:40,y1:160-(e-s)/(t-s)*120,x2:360,y2:160-(e-s)/(t-s)*120,stroke:"#f3f4f6",strokeWidth:"1"}),a.jsx("text",{x:30,y:160-(e-s)/(t-s)*120+4,className:"text-xs fill-gray-500",textAnchor:"end",children:e})]},e)),a.jsx("path",{d:r,stroke:"#3b82f6",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),a.jsx("path",{d:`${r} L 360 160 L 40 160 Z`,fill:"url(#gradient)",opacity:"0.1"}),e.map((e,r)=>{let i=40+r*l,n=160-(e.average-s)/(t-s)*120;return(0,a.jsxs)("g",{children:[a.jsx("circle",{cx:i,cy:n,r:"4",fill:"#3b82f6",stroke:"white",strokeWidth:"2"}),a.jsx("text",{x:i,y:180,className:"text-xs fill-gray-600",textAnchor:"middle",children:e.month}),a.jsx("text",{x:i,y:n-10,className:"text-xs fill-gray-900 font-medium",textAnchor:"middle",children:e.average.toFixed(1)})]},r)}),a.jsx("defs",{children:(0,a.jsxs)("linearGradient",{id:"gradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[a.jsx("stop",{offset:"0%",stopColor:"#3b82f6",stopOpacity:"0.3"}),a.jsx("stop",{offset:"100%",stopColor:"#3b82f6",stopOpacity:"0"})]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"text-lg font-bold text-blue-600",children:e[e.length-1]?.average.toFixed(1)}),a.jsx("div",{className:"text-xs text-blue-600",children:"Current Average"})]}),(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-green-600",children:["+",(e[e.length-1]?.average-e[0]?.average||0).toFixed(1)]}),a.jsx("div",{className:"text-xs text-green-600",children:"Total Improvement"})]}),(0,a.jsxs)("div",{className:"p-3 bg-purple-50 rounded-lg",children:[a.jsx("div",{className:"text-lg font-bold text-purple-600",children:e.reduce((e,s)=>e+s.assessments,0)}),a.jsx("div",{className:"text-xs text-purple-600",children:"Total Assessments"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"Trend Analysis"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e[e.length-1]?.average>e[0]?.average?a.jsx(c.C,{variant:"default",className:"bg-green-100 text-green-800",children:"\uD83D\uDCC8 Positive Trend"}):a.jsx(c.C,{variant:"secondary",className:"bg-orange-100 text-orange-800",children:"\uD83D\uDCC9 Needs Attention"}),e.filter((s,t)=>t>0&&s.average>e[t-1].average).length>=e.length/2&&a.jsx(c.C,{variant:"outline",className:"border-blue-200 text-blue-700",children:"\uD83C\uDFAF Consistent Growth"}),e[e.length-1]?.average>=6&&a.jsx(c.C,{variant:"outline",className:"border-purple-200 text-purple-700",children:"⭐ High Performance"})]})]})]})}var h=t(47210),u=t(17910),p=t(7060),g=t(46064),j=t(25545),f=t(55794),v=t(66138);function y(){let e={totalGoals:12,completedGoals:4,averageCompletionTime:45,successRate:75,goals:[{id:"1",title:"Improve Physical Fitness",category:"Health & Fitness",status:"active",progress:85,targetDate:"2024-12-31",daysRemaining:120,milestones:4,completedMilestones:3},{id:"2",title:"Learn Spanish",category:"Education & Learning",status:"active",progress:60,targetDate:"2024-10-15",daysRemaining:45,milestones:6,completedMilestones:3},{id:"3",title:"Build Emergency Fund",category:"Finances",status:"completed",progress:100,targetDate:"2024-06-30",daysRemaining:0,milestones:5,completedMilestones:5},{id:"4",title:"Improve Work-Life Balance",category:"Career & Work",status:"active",progress:40,targetDate:"2024-11-30",daysRemaining:90,milestones:3,completedMilestones:1}]},s=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"active":return"bg-blue-100 text-blue-800";case"paused":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Goals"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.totalGoals})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(p.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.completedGoals})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(g.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Success Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.successRate,"%"]})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:a.jsx(j.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Avg Completion"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.averageCompletionTime,"d"]})]})]})})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Goal Progress Tracking"}),a.jsx(i.SZ,{children:"Monitor your active goals and their completion status"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-6",children:e.goals.map(e=>(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-medium text-gray-900",children:e.title}),a.jsx("p",{className:"text-sm text-gray-600",children:e.category})]}),a.jsx(c.C,{className:s(e.status),children:e.status})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Progress"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.progress,"%"]})]}),a.jsx(h.E,{value:e.progress,className:"h-2"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(f.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("span",{className:"text-gray-600",children:["Due: ",new Date(e.targetDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(j.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-gray-600",children:e.daysRemaining>0?`${e.daysRemaining} days left`:"Completed"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(u.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("span",{className:"text-gray-600",children:[e.completedMilestones,"/",e.milestones," milestones"]})]})]}),a.jsx("div",{className:"flex items-center space-x-1",children:Array.from({length:e.milestones},(s,t)=>a.jsx("div",{className:`h-2 flex-1 rounded ${t<e.completedMilestones?"bg-green-500":"bg-gray-200"}`},t))})]})]},e.id))})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Goals by Category"}),a.jsx(i.SZ,{children:"Distribution of goals across life areas"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:[{category:"Health & Fitness",count:3,color:"bg-red-500"},{category:"Career & Work",count:2,color:"bg-blue-500"},{category:"Finances",count:2,color:"bg-green-500"},{category:"Education & Learning",count:2,color:"bg-purple-500"},{category:"Relationships",count:2,color:"bg-pink-500"},{category:"Personal Growth",count:1,color:"bg-yellow-500"}].map((s,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:`w-4 h-4 rounded ${s.color}`}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-gray-900",children:s.category}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[s.count," goals"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-1 mt-1",children:a.jsx("div",{className:`h-1 rounded-full ${s.color}`,style:{width:`${s.count/e.totalGoals*100}%`}})})]})]},t))})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Goal Completion Timeline"}),a.jsx(i.SZ,{children:"Upcoming goal deadlines and milestones"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:[{title:"Spanish fluency milestone",date:"2024-09-15",type:"milestone",urgent:!0},{title:"Fitness goal deadline",date:"2024-10-01",type:"deadline",urgent:!1},{title:"Career development review",date:"2024-10-15",type:"milestone",urgent:!1},{title:"Financial planning update",date:"2024-11-01",type:"milestone",urgent:!1}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:`p-2 rounded-lg ${e.urgent?"bg-red-100":"bg-blue-100"}`,children:"deadline"===e.type?a.jsx(v.Z,{className:`h-4 w-4 ${e.urgent?"text-red-600":"text-blue-600"}`}):a.jsx(u.Z,{className:`h-4 w-4 ${e.urgent?"text-red-600":"text-blue-600"}`})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium text-gray-900",children:e.title}),a.jsx("p",{className:"text-sm text-gray-600",children:new Date(e.date).toLocaleDateString()})]}),e.urgent&&a.jsx(c.C,{variant:"destructive",className:"text-xs",children:"Urgent"})]},s))})})]})]})]})}var N=t(45961),b=t(79200),w=t(51354),Z=t(65187),A=t(5700),C=t(76755),D=t(35341);function O({data:e}){let s=(()=>{let s=[],t=e.lifeAreasData.filter(e=>"up"===e.trend).length,a=e.lifeAreasData.filter(e=>"down"===e.trend).length;e.lifeAreasData.filter(e=>"stable"===e.trend).length,e.lifeAreasData.sort((e,s)=>s.current-e.current).slice(0,3),e.lifeAreasData.sort((e,s)=>e.current-s.current).slice(0,3),t>=6&&s.push({type:"positive",icon:g.Z,title:"Excellent Progress Momentum",description:`You're improving in ${t} life areas! This shows great consistency in your growth efforts.`,action:"Keep up the momentum by maintaining your current habits.",priority:"high"}),a>=3&&s.push({type:"warning",icon:N.Z,title:"Areas Needing Attention",description:`${a} areas are showing decline. Consider focusing on these to prevent further regression.`,action:"Review your goals and strategies for these areas.",priority:"high"}),e.streakDays>=30&&s.push({type:"achievement",icon:b.Z,title:"Consistency Champion",description:`${e.streakDays} days of consistent tracking! This habit is key to long-term success.`,action:"Celebrate this milestone and aim for the next level.",priority:"medium"});let r=e.lifeAreasData.reduce((e,s)=>e+s.current,0)/e.lifeAreasData.length,l=e.lifeAreasData.reduce((e,s)=>e+Math.pow(s.current-r,2),0)/e.lifeAreasData.length;return l<1?s.push({type:"positive",icon:w.Z,title:"Well-Balanced Life",description:"Your life areas are well-balanced with consistent ratings across all domains.",action:"Focus on gradual improvement across all areas.",priority:"low"}):l>3&&s.push({type:"suggestion",icon:u.Z,title:"Focus on Balance",description:"Some life areas are significantly higher/lower than others. Consider balancing your attention.",action:"Allocate more time to your lowest-rated areas.",priority:"medium"}),e.goalsAchieved>=10&&s.push({type:"achievement",icon:Z.Z,title:"Goal Achievement Master",description:`${e.goalsAchieved} goals achieved! You're excellent at turning plans into reality.`,action:"Consider setting more ambitious goals for greater growth.",priority:"medium"}),s})(),t=e=>{switch(e){case"positive":return"border-green-200 bg-green-50";case"achievement":return"border-purple-200 bg-purple-50";case"warning":return"border-red-200 bg-red-50";case"suggestion":return"border-blue-200 bg-blue-50";default:return"border-gray-200 bg-gray-50"}},r=e=>{switch(e){case"positive":return"text-green-600";case"achievement":return"text-purple-600";case"warning":return"text-red-600";case"suggestion":return"text-blue-600";default:return"text-gray-600"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[a.jsx(A.Z,{className:"h-5 w-5 text-purple-600"}),a.jsx("span",{children:"Personalized Insights"})]}),a.jsx(i.SZ,{children:"AI-powered analysis of your progress patterns and recommendations"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:s.map((e,s)=>a.jsx("div",{className:`p-4 border rounded-lg ${t(e.type)}`,children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"p-2 rounded-lg bg-white",children:a.jsx(e.icon,{className:`h-5 w-5 ${r(e.type)}`})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("h3",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsxs)(c.C,{variant:"high"===e.priority?"destructive":"medium"===e.priority?"default":"secondary",children:[e.priority," priority"]})]}),a.jsx("p",{className:"text-sm text-gray-700 mb-2",children:e.description}),(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["\uD83D\uDCA1 ",e.action]})]})]})},s))})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Strengths & Achievements"}),a.jsx(i.SZ,{children:"Areas where you're excelling"})]}),a.jsx(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[e.lifeAreasData.filter(e=>e.current>=6).slice(0,4).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-green-900",children:e.area}),a.jsx("p",{className:"text-sm text-green-700",children:"Excellent performance"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.C,{variant:"secondary",className:"bg-green-100 text-green-800",children:[e.current,"/7"]}),a.jsx(C.Z,{className:"h-4 w-4 text-green-600"})]})]},s)),e.streakDays>=7&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-purple-900",children:"Consistency Streak"}),(0,a.jsxs)("p",{className:"text-sm text-purple-700",children:[e.streakDays," days of tracking"]})]}),a.jsx(b.Z,{className:"h-5 w-5 text-purple-600"})]})]})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Growth Opportunities"}),a.jsx(i.SZ,{children:"Areas with the most potential for improvement"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:e.lifeAreasData.filter(e=>e.current<e.target).sort((e,s)=>s.target-s.current-(e.target-e.current)).slice(0,4).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-blue-900",children:e.area}),(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:[e.target-e.current," points to target"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.C,{variant:"outline",className:"border-blue-200 text-blue-800",children:[e.current," → ",e.target]}),a.jsx(u.Z,{className:"h-4 w-4 text-blue-600"})]})]},s))})})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Recommended Actions"}),a.jsx(i.SZ,{children:"Specific steps to accelerate your progress"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[{title:"Schedule Monthly Review",description:"Set up a recurring monthly assessment to track progress consistently.",action:"Create Calendar Event",icon:p.Z,color:"blue"},{title:"Focus on Bottom 3 Areas",description:"Allocate 70% of your improvement efforts to your lowest-rated areas.",action:"Create Action Plan",icon:u.Z,color:"orange"},{title:"Leverage Your Strengths",description:"Use your strong areas to support growth in weaker areas.",action:"Explore Connections",icon:D.Z,color:"purple"},{title:"Set Micro-Goals",description:"Break down large goals into weekly micro-goals for better momentum.",action:"Plan This Week",icon:b.Z,color:"green"}].map((e,s)=>a.jsx("div",{className:"p-4 border rounded-lg hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:`p-2 bg-${e.color}-100 rounded-lg`,children:a.jsx(e.icon,{className:`h-5 w-5 text-${e.color}-600`})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-medium text-gray-900 mb-1",children:e.title}),a.jsx("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),a.jsx(n.z,{variant:"outline",size:"sm",className:"w-full",children:e.action})]})]})},s))})})]})]})}var k=t(96885),M=t(33733),_=t(50340);function P(){let{common:e,lifeAreas:s}=(0,l.Q)(),[t,h]=(0,r.useState)("12months"),[p,j]=(0,r.useState)("all"),f={totalAssessments:12,averageImprovement:18,goalsAchieved:15,streakDays:45,lifeAreasData:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>({area:e.name,current:Math.floor(3*Math.random())+4,previous:Math.floor(3*Math.random())+3,target:Math.floor(2*Math.random())+6,trend:Math.random()>.3?"up":Math.random()>.5?"stable":"down"})),monthlyProgress:[{month:"Jan",average:4.2,assessments:1},{month:"Feb",average:4.5,assessments:1},{month:"Mar",average:4.8,assessments:1},{month:"Apr",average:5.1,assessments:1},{month:"May",average:5.3,assessments:1},{month:"Jun",average:5.6,assessments:1}]};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Analytics & Insights"}),a.jsx("p",{className:"mt-2 text-gray-600",children:"Comprehensive analysis of your life planning journey and progress trends"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(o.Ph,{value:t,onValueChange:h,children:[a.jsx(o.i4,{className:"w-40",children:a.jsx(o.ki,{})}),(0,a.jsxs)(o.Bw,{children:[a.jsx(o.Ql,{value:"3months",children:"Last 3 months"}),a.jsx(o.Ql,{value:"6months",children:"Last 6 months"}),a.jsx(o.Ql,{value:"12months",children:"Last 12 months"}),a.jsx(o.Ql,{value:"all",children:"All time"})]})]}),(0,a.jsxs)(n.z,{variant:"outline",children:[a.jsx(k.Z,{className:"mr-2 h-4 w-4"}),"Export Report"]}),(0,a.jsxs)(n.z,{variant:"outline",children:[a.jsx(M.Z,{className:"mr-2 h-4 w-4"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(_.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Assessments"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:f.totalAssessments})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(g.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Avg Improvement"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["+",f.averageImprovement,"%"]})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(Z.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Goals Achieved"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:f.goalsAchieved})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:a.jsx(b.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Current Streak"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[f.streakDays," days"]})]})]})})})]}),(0,a.jsxs)(d.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,a.jsxs)(d.dr,{className:"grid w-full grid-cols-4",children:[a.jsx(d.SP,{value:"overview",children:"Overview"}),a.jsx(d.SP,{value:"life-areas",children:"Life Areas"}),a.jsx(d.SP,{value:"goals",children:"Goals"}),a.jsx(d.SP,{value:"insights",children:"Insights"})]}),(0,a.jsxs)(d.nU,{value:"overview",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Progress Trend"}),a.jsx(i.SZ,{children:"Your overall life satisfaction over time"})]}),a.jsx(i.aY,{children:a.jsx(m,{data:f.monthlyProgress})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Life Areas Overview"}),a.jsx(i.SZ,{children:"Current ratings across all 12 life areas"})]}),a.jsx(i.aY,{children:a.jsx(x,{data:f.lifeAreasData})})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Recent Activity"}),a.jsx(i.SZ,{children:"Your latest assessments and achievements"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:[{type:"assessment",title:"Completed monthly assessment",description:"Overall rating improved by 0.3 points",date:"2 days ago",icon:_.Z,color:"blue"},{type:"goal",title:"Achieved fitness milestone",description:"Completed 30-day workout challenge",date:"1 week ago",icon:Z.Z,color:"green"},{type:"planning",title:"Updated quarterly goals",description:"Revised 3 goals based on progress",date:"2 weeks ago",icon:u.Z,color:"purple"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:`p-2 bg-${e.color}-100 rounded-lg`,children:a.jsx(e.icon,{className:`h-4 w-4 text-${e.color}-600`})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium text-gray-900",children:e.title}),a.jsx("p",{className:"text-sm text-gray-600",children:e.description}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:e.date})]})]},s))})})]})]}),(0,a.jsxs)(d.nU,{value:"life-areas",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Life Areas Analysis"}),(0,a.jsxs)(o.Ph,{value:p,onValueChange:j,children:[a.jsx(o.i4,{className:"w-48",children:a.jsx(o.ki,{})}),(0,a.jsxs)(o.Bw,{children:[a.jsx(o.Ql,{value:"all",children:"All Areas"}),Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>a.jsx(o.Ql,{value:e.id,children:e.name},e.id))]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Detailed Life Areas Breakdown"}),a.jsx(i.SZ,{children:"Compare current, previous, and target ratings"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:f.lifeAreasData.slice(0,6).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"w-32 text-sm font-medium text-gray-900",children:e.area}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,a.jsxs)("span",{children:["Current: ",e.current]}),(0,a.jsxs)("span",{children:["Target: ",e.target]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.current/7*100}%`}})})]}),a.jsx("div",{className:"w-16 text-center",children:a.jsx(c.C,{variant:"up"===e.trend?"default":"down"===e.trend?"destructive":"secondary",children:"up"===e.trend?"↗":"down"===e.trend?"↘":"→"})})]},s))})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Top Performers"})}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-3",children:f.lifeAreasData.sort((e,s)=>s.current-e.current).slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-gray-900",children:e.area}),(0,a.jsxs)(c.C,{variant:"secondary",children:[e.current,"/7"]})]},s))})})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Focus Areas"})}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-3",children:f.lifeAreasData.sort((e,s)=>e.current-s.current).slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-gray-900",children:e.area}),(0,a.jsxs)(c.C,{variant:"outline",children:[e.current,"/7"]})]},s))})})]})]})]})]}),a.jsx(d.nU,{value:"goals",className:"space-y-6",children:a.jsx(y,{})}),a.jsx(d.nU,{value:"insights",className:"space-y-6",children:a.jsx(O,{data:f})})]})]})}!function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()},38157:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>u,Ph:()=>d,Ql:()=>p,i4:()=>x,ki:()=>o});var a=t(95344),r=t(3729),l=t(32116),i=t(25390),n=t(12704),c=t(62312);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let d=l.fC;l.ZA;let o=l.B4,x=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.xz,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(l.JO,{asChild:!0,children:a.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.xz.displayName;let m=r.forwardRef(({className:e,...s},t)=>a.jsx(l.u_,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));m.displayName=l.u_.displayName;let h=r.forwardRef(({className:e,...s},t)=>a.jsx(l.$G,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(i.Z,{className:"h-4 w-4"})}));h.displayName=l.$G.displayName;let u=r.forwardRef(({className:e,children:s,position:t="popper",...r},i)=>a.jsx(l.h_,{children:(0,a.jsxs)(l.VY,{ref:i,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(m,{}),a.jsx(l.l_,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(h,{})]})}));u.displayName=l.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(l.__,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.__.displayName;let p=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.ck,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(l.wU,{children:a.jsx(c.Z,{className:"h-4 w-4"})})}),a.jsx(l.eT,{children:s})]}));p.displayName=l.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(l.Z0,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.Z0.displayName},60900:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m,generateMetadata:()=>x});var a=t(25036),r=t(13285),l=t(97385),i=t(86843);let n=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/analytics/analytics-dashboard.tsx`),{__esModule:c,$$typeof:d}=n;n.default;let o=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/analytics/analytics-dashboard.tsx#AnalyticsDashboard`);async function x({params:{locale:e}}){return await (0,r.Z)({locale:e,namespace:"common"}),{title:"Analytics - 8,760 Hours",description:"Comprehensive analytics and insights for your life planning journey."}}function m(){return a.jsx(l.c,{children:a.jsx("div",{className:"max-w-7xl mx-auto",children:a.jsx(o,{})})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,8356,3828,1476,1498,1110,6583,8536,5843,9669,79],()=>t(99648));module.exports=a})();