(()=>{var e={};e.id=5061,e.ids=[5061],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93537:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var n=r(50482),a=r(69108),s=r(62563),o=r.n(s),i=r(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60192)),"/mnt/persist/workspace/frontend/src/app/[locale]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/[locale]/page.tsx"],u="/[locale]/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36952:(e,t,r)=>{Promise.resolve().then(r.bind(r,53283)),Promise.resolve().then(r.t.bind(r,61476,23)),Promise.resolve().then(r.bind(r,58114))},82958:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58114:(e,t,r)=>{"use strict";r.r(t),r.d(t,{InlineLanguageSwitcher:()=>e6,LanguageGrid:()=>e9,LanguageSwitcher:()=>e8});var n=r(95344),a=r(3729),s=r(5094),o=r(85222),i=r(31405),l=r(98462),c=r(33183),d=r(62409),u=r(77411),m=r(3975),p=r(44155),x=r(1106),f=r(27386),h=r(99048),g=r(37574),j=r(31179),v=r(43234),w=r(34504),y=r(32751),N=r(2256),b=r(45904),D=r(37792),M=["Enter"," "],C=["ArrowUp","PageDown","End"],k=["ArrowDown","PageUp","Home",...C],_={ltr:[...M,"ArrowRight"],rtl:[...M,"ArrowLeft"]},O={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[E,R,F]=(0,u.B)(P),[T,L]=(0,l.b)(P,[F,g.D7,w.Pc]),A=(0,g.D7)(),Z=(0,w.Pc)(),[I,U]=T(P),[S,z]=T(P),K=e=>{let{__scopeMenu:t,open:r=!1,children:s,dir:o,onOpenChange:i,modal:l=!0}=e,c=A(t),[d,u]=a.useState(null),p=a.useRef(!1),x=(0,N.W)(i),f=(0,m.gm)(o);return a.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,n.jsx)(g.fC,{...c,children:(0,n.jsx)(I,{scope:t,open:r,onOpenChange:x,content:d,onContentChange:u,children:(0,n.jsx)(S,{scope:t,onClose:a.useCallback(()=>x(!1),[x]),isUsingKeyboardRef:p,dir:f,modal:l,children:s})})})};K.displayName=P;var G=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e,s=A(r);return(0,n.jsx)(g.ee,{...s,...a,ref:t})});G.displayName="MenuAnchor";var $="MenuPortal",[V,q]=T($,{forceMount:void 0}),B=e=>{let{__scopeMenu:t,forceMount:r,children:a,container:s}=e,o=U($,t);return(0,n.jsx)(V,{scope:t,forceMount:r,children:(0,n.jsx)(v.z,{present:r||o.open,children:(0,n.jsx)(j.h,{asChild:!0,container:s,children:a})})})};B.displayName=$;var W="MenuContent",[H,X]=T(W),Q=a.forwardRef((e,t)=>{let r=q(W,e.__scopeMenu),{forceMount:a=r.forceMount,...s}=e,o=U(W,e.__scopeMenu),i=z(W,e.__scopeMenu);return(0,n.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(v.z,{present:a||o.open,children:(0,n.jsx)(E.Slot,{scope:e.__scopeMenu,children:i.modal?(0,n.jsx)(Y,{...s,ref:t}):(0,n.jsx)(J,{...s,ref:t})})})})}),Y=a.forwardRef((e,t)=>{let r=U(W,e.__scopeMenu),s=a.useRef(null),l=(0,i.e)(t,s);return a.useEffect(()=>{let e=s.current;if(e)return(0,b.Ry)(e)},[]),(0,n.jsx)(et,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),J=a.forwardRef((e,t)=>{let r=U(W,e.__scopeMenu);return(0,n.jsx)(et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ee=(0,y.Z8)("MenuContent.ScrollLock"),et=a.forwardRef((e,t)=>{let{__scopeMenu:r,loop:s=!1,trapFocus:l,onOpenAutoFocus:c,onCloseAutoFocus:d,disableOutsidePointerEvents:u,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:j,onFocusOutside:v,onInteractOutside:y,onDismiss:N,disableOutsideScroll:b,...M}=e,_=U(W,r),O=z(W,r),P=A(r),E=Z(r),F=R(r),[T,L]=a.useState(null),I=a.useRef(null),S=(0,i.e)(t,I,_.onContentChange),K=a.useRef(0),G=a.useRef(""),$=a.useRef(0),V=a.useRef(null),q=a.useRef("right"),B=a.useRef(0),X=b?D.Z:a.Fragment,Q=e=>{let t=G.current+e,r=F().filter(e=>!e.disabled),n=document.activeElement,a=r.find(e=>e.ref.current===n)?.textValue,s=function(e,t,r){var n;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,s=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===a.length&&(s=s.filter(e=>e!==r));let o=s.find(e=>e.toLowerCase().startsWith(a.toLowerCase()));return o!==r?o:void 0}(r.map(e=>e.textValue),t,a),o=r.find(e=>e.textValue===s)?.ref.current;(function e(t){G.current=t,window.clearTimeout(K.current),""!==t&&(K.current=window.setTimeout(()=>e(""),1e3))})(t),o&&setTimeout(()=>o.focus())};a.useEffect(()=>()=>window.clearTimeout(K.current),[]),(0,x.EW)();let Y=a.useCallback(e=>q.current===V.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,a=!1;for(let e=0,s=t.length-1;e<t.length;s=e++){let o=t[e],i=t[s],l=o.x,c=o.y,d=i.x,u=i.y;c>n!=u>n&&r<(d-l)*(n-c)/(u-c)+l&&(a=!a)}return a}({x:e.clientX,y:e.clientY},t)}(e,V.current?.area),[]);return(0,n.jsx)(H,{scope:r,searchRef:G,onItemEnter:a.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),onItemLeave:a.useCallback(e=>{Y(e)||(I.current?.focus(),L(null))},[Y]),onTriggerLeave:a.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),pointerGraceTimerRef:$,onPointerGraceIntentChange:a.useCallback(e=>{V.current=e},[]),children:(0,n.jsx)(X,{...b?{as:ee,allowPinchZoom:!0}:void 0,children:(0,n.jsx)(f.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(c,e=>{e.preventDefault(),I.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,n.jsx)(p.XB,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:h,onPointerDownOutside:j,onFocusOutside:v,onInteractOutside:y,onDismiss:N,children:(0,n.jsx)(w.fC,{asChild:!0,...E,dir:O.dir,orientation:"vertical",loop:s,currentTabStopId:T,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.M)(m,e=>{O.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,n.jsx)(g.VY,{role:"menu","aria-orientation":"vertical","data-state":ek(_.open),"data-radix-menu-content":"",dir:O.dir,...P,...M,ref:S,style:{outline:"none",...M.style},onKeyDown:(0,o.M)(M.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Q(e.key));let a=I.current;if(e.target!==a||!k.includes(e.key))return;e.preventDefault();let s=F().filter(e=>!e.disabled).map(e=>e.ref.current);C.includes(e.key)&&s.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(s)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(K.current),G.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eP(e=>{let t=e.target,r=B.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>B.current?"right":"left";q.current=t,B.current=e.clientX}}))})})})})})})});Q.displayName=W;var er=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e;return(0,n.jsx)(d.WV.div,{role:"group",...a,ref:t})});er.displayName="MenuGroup";var en=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e;return(0,n.jsx)(d.WV.div,{...a,ref:t})});en.displayName="MenuLabel";var ea="MenuItem",es="menu.itemSelect",eo=a.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:s,...l}=e,c=a.useRef(null),u=z(ea,e.__scopeMenu),m=X(ea,e.__scopeMenu),p=(0,i.e)(t,c),x=a.useRef(!1);return(0,n.jsx)(ei,{...l,ref:p,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(es,{bubbles:!0,cancelable:!0});e.addEventListener(es,e=>s?.(e),{once:!0}),(0,d.jH)(e,t),t.defaultPrevented?x.current=!1:u.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),x.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{x.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==m.searchRef.current;!r&&(!t||" "!==e.key)&&M.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=ea;var ei=a.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:s=!1,textValue:l,...c}=e,u=X(ea,r),m=Z(r),p=a.useRef(null),x=(0,i.e)(t,p),[f,h]=a.useState(!1),[g,j]=a.useState("");return a.useEffect(()=>{let e=p.current;e&&j((e.textContent??"").trim())},[c.children]),(0,n.jsx)(E.ItemSlot,{scope:r,disabled:s,textValue:l??g,children:(0,n.jsx)(w.ck,{asChild:!0,...m,focusable:!s,children:(0,n.jsx)(d.WV.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...c,ref:x,onPointerMove:(0,o.M)(e.onPointerMove,eP(e=>{s?u.onItemLeave(e):(u.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eP(e=>u.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>h(!0)),onBlur:(0,o.M)(e.onBlur,()=>h(!1))})})})}),el=a.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:a,...s}=e;return(0,n.jsx)(eh,{scope:e.__scopeMenu,checked:r,children:(0,n.jsx)(eo,{role:"menuitemcheckbox","aria-checked":e_(r)?"mixed":r,...s,ref:t,"data-state":eO(r),onSelect:(0,o.M)(s.onSelect,()=>a?.(!!e_(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ec="MenuRadioGroup",[ed,eu]=T(ec,{value:void 0,onValueChange:()=>{}}),em=a.forwardRef((e,t)=>{let{value:r,onValueChange:a,...s}=e,o=(0,N.W)(a);return(0,n.jsx)(ed,{scope:e.__scopeMenu,value:r,onValueChange:o,children:(0,n.jsx)(er,{...s,ref:t})})});em.displayName=ec;var ep="MenuRadioItem",ex=a.forwardRef((e,t)=>{let{value:r,...a}=e,s=eu(ep,e.__scopeMenu),i=r===s.value;return(0,n.jsx)(eh,{scope:e.__scopeMenu,checked:i,children:(0,n.jsx)(eo,{role:"menuitemradio","aria-checked":i,...a,ref:t,"data-state":eO(i),onSelect:(0,o.M)(a.onSelect,()=>s.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ex.displayName=ep;var ef="MenuItemIndicator",[eh,eg]=T(ef,{checked:!1}),ej=a.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:a,...s}=e,o=eg(ef,r);return(0,n.jsx)(v.z,{present:a||e_(o.checked)||!0===o.checked,children:(0,n.jsx)(d.WV.span,{...s,ref:t,"data-state":eO(o.checked)})})});ej.displayName=ef;var ev=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e;return(0,n.jsx)(d.WV.div,{role:"separator","aria-orientation":"horizontal",...a,ref:t})});ev.displayName="MenuSeparator";var ew=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e,s=A(r);return(0,n.jsx)(g.Eh,{...s,...a,ref:t})});ew.displayName="MenuArrow";var[ey,eN]=T("MenuSub"),eb="MenuSubTrigger",eD=a.forwardRef((e,t)=>{let r=U(eb,e.__scopeMenu),s=z(eb,e.__scopeMenu),l=eN(eb,e.__scopeMenu),c=X(eb,e.__scopeMenu),d=a.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:m}=c,p={__scopeMenu:e.__scopeMenu},x=a.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return a.useEffect(()=>x,[x]),a.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),m(null)}},[u,m]),(0,n.jsx)(G,{asChild:!0,...p,children:(0,n.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":ek(r.open),...e,ref:(0,i.F)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eP(t=>{c.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||d.current||(c.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{r.onOpenChange(!0),x()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eP(e=>{x();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,a="right"===n,s=t[a?"left":"right"],o=t[a?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:s,y:t.top},{x:o,y:t.top},{x:o,y:t.bottom},{x:s,y:t.bottom}],side:n}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==c.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&_[s.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eD.displayName=eb;var eM="MenuSubContent",eC=a.forwardRef((e,t)=>{let r=q(W,e.__scopeMenu),{forceMount:s=r.forceMount,...l}=e,c=U(W,e.__scopeMenu),d=z(W,e.__scopeMenu),u=eN(eM,e.__scopeMenu),m=a.useRef(null),p=(0,i.e)(t,m);return(0,n.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(v.z,{present:s||c.open,children:(0,n.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,n.jsx)(et,{id:u.contentId,"aria-labelledby":u.triggerId,...l,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{d.isUsingKeyboardRef.current&&m.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==u.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=O[d.dir].includes(e.key);t&&r&&(c.onOpenChange(!1),u.trigger?.focus(),e.preventDefault())})})})})})});function ek(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function eO(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eP(e){return t=>"mouse"===t.pointerType?e(t):void 0}eC.displayName=eM;var eE="DropdownMenu",[eR,eF]=(0,l.b)(eE,[L]),eT=L(),[eL,eA]=eR(eE),eZ=e=>{let{__scopeDropdownMenu:t,children:r,dir:s,open:o,defaultOpen:i,onOpenChange:l,modal:d=!0}=e,u=eT(t),m=a.useRef(null),[p,x]=(0,c.T)({prop:o,defaultProp:i??!1,onChange:l,caller:eE});return(0,n.jsx)(eL,{scope:t,triggerId:(0,h.M)(),triggerRef:m,contentId:(0,h.M)(),open:p,onOpenChange:x,onOpenToggle:a.useCallback(()=>x(e=>!e),[x]),modal:d,children:(0,n.jsx)(K,{...u,open:p,onOpenChange:x,dir:s,modal:d,children:r})})};eZ.displayName=eE;var eI="DropdownMenuTrigger",eU=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:a=!1,...s}=e,l=eA(eI,r),c=eT(r);return(0,n.jsx)(G,{asChild:!0,...c,children:(0,n.jsx)(d.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":a?"":void 0,disabled:a,...s,ref:(0,i.F)(t,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{a||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!a&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eU.displayName=eI;var eS=e=>{let{__scopeDropdownMenu:t,...r}=e,a=eT(t);return(0,n.jsx)(B,{...a,...r})};eS.displayName="DropdownMenuPortal";var ez="DropdownMenuContent",eK=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...s}=e,i=eA(ez,r),l=eT(r),c=a.useRef(!1);return(0,n.jsx)(Q,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...s,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{c.current||i.triggerRef.current?.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eK.displayName=ez,a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(er,{...s,...a,ref:t})}).displayName="DropdownMenuGroup";var eG=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(en,{...s,...a,ref:t})});eG.displayName="DropdownMenuLabel";var e$=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(eo,{...s,...a,ref:t})});e$.displayName="DropdownMenuItem";var eV=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(el,{...s,...a,ref:t})});eV.displayName="DropdownMenuCheckboxItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(em,{...s,...a,ref:t})}).displayName="DropdownMenuRadioGroup";var eq=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(ex,{...s,...a,ref:t})});eq.displayName="DropdownMenuRadioItem";var eB=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(ej,{...s,...a,ref:t})});eB.displayName="DropdownMenuItemIndicator";var eW=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(ev,{...s,...a,ref:t})});eW.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(ew,{...s,...a,ref:t})}).displayName="DropdownMenuArrow";var eH=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(eD,{...s,...a,ref:t})});eH.displayName="DropdownMenuSubTrigger";var eX=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=eT(r);return(0,n.jsx)(eC,{...s,...a,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eX.displayName="DropdownMenuSubContent";var eQ=r(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eY=(0,eQ.Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var eJ=r(62312),e0=r(82958);(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e})(),a.forwardRef(({className:e,inset:t,children:r,...a},s)=>(0,n.jsxs)(eH,{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...a,children:[r,n.jsx(eY,{className:"ml-auto h-4 w-4"})]})).displayName=eH.displayName,a.forwardRef(({className:e,...t},r)=>n.jsx(eX,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=eX.displayName;let e2=a.forwardRef(({className:e,sideOffset:t=4,...r},a)=>n.jsx(eS,{children:n.jsx(eK,{ref:a,sideOffset:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));e2.displayName=eK.displayName;let e1=a.forwardRef(({className:e,inset:t,...r},a)=>n.jsx(e$,{ref:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...r}));e1.displayName=e$.displayName,a.forwardRef(({className:e,children:t,checked:r,...a},s)=>(0,n.jsxs)(eV,{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...a,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(eB,{children:n.jsx(eJ.Z,{className:"h-4 w-4"})})}),t]})).displayName=eV.displayName,a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(eq,{ref:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(eB,{children:n.jsx(e0.Z,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=eq.displayName,a.forwardRef(({className:e,inset:t,...r},a)=>n.jsx(eG,{ref:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...r})).displayName=eG.displayName,a.forwardRef(({className:e,...t},r)=>n.jsx(eW,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=eW.displayName;var e5=r(19591),e4=r(68962);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let e3=(0,eQ.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);function e8({variant:e="default",className:t}){let{locale:r,changeLocale:a,getAvailableLocales:o,common:i}=(0,e4.Q)(),l=o(),c=l.find(e=>e.isActive);return"icon-only"===e?(0,n.jsxs)(eZ,{children:[n.jsx(eU,{asChild:!0,children:n.jsx(s.z,{variant:"ghost",size:"icon",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("h-9 w-9",t),"aria-label":i("changeLanguage"),children:n.jsx(e3,{className:"h-4 w-4"})})}),n.jsx(e2,{align:"end",className:"w-48",children:l.map(e=>(0,n.jsxs)(e1,{onClick:()=>a(e.code),className:"flex items-center justify-between cursor-pointer",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx("span",{className:"text-lg",children:e.flag}),n.jsx("span",{children:e.name})]}),e.isActive&&n.jsx(eJ.Z,{className:"h-4 w-4"})]},e.code))})]}):"compact"===e?(0,n.jsxs)(eZ,{children:[n.jsx(eU,{asChild:!0,children:(0,n.jsxs)(s.z,{variant:"outline",size:"sm",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("h-8 px-2",t),children:[n.jsx("span",{className:"mr-1",children:c?.flag}),n.jsx("span",{className:"text-xs font-medium",children:c?.code.toUpperCase()})]})}),n.jsx(e2,{align:"end",className:"w-48",children:l.map(e=>(0,n.jsxs)(e1,{onClick:()=>a(e.code),className:"flex items-center justify-between cursor-pointer",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx("span",{className:"text-lg",children:e.flag}),n.jsx("span",{children:e.name})]}),e.isActive&&n.jsx(eJ.Z,{className:"h-4 w-4"})]},e.code))})]}):(0,n.jsxs)(eZ,{children:[n.jsx(eU,{asChild:!0,children:n.jsx(s.z,{variant:"outline",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("justify-between min-w-[140px]",t),children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(e3,{className:"h-4 w-4"}),n.jsx("span",{className:"text-sm",children:c?.flag}),n.jsx("span",{className:"text-sm font-medium",children:c?.name})]})})}),(0,n.jsxs)(e2,{align:"end",className:"w-56",children:[n.jsx("div",{className:"px-2 py-1.5",children:n.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:i("selectLanguage")})}),l.map(e=>(0,n.jsxs)(e1,{onClick:()=>a(e.code),className:"flex items-center justify-between cursor-pointer py-2",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[n.jsx("span",{className:"text-lg",children:e.flag}),(0,n.jsxs)("div",{className:"flex flex-col",children:[n.jsx("span",{className:"text-sm font-medium",children:e.name}),n.jsx("span",{className:"text-xs text-muted-foreground",children:e.code.toUpperCase()})]})]}),e.isActive&&(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[n.jsx(e5.C,{variant:"secondary",className:"text-xs",children:i("current")}),n.jsx(eJ.Z,{className:"h-4 w-4 text-primary"})]})]},e.code))]})]})}function e6({className:e}){let{changeLocale:t,getAvailableLocales:r}=(0,e4.Q)(),a=r();return n.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-wrap gap-2",e),children:a.map(e=>(0,n.jsxs)(s.z,{variant:e.isActive?"default":"outline",size:"sm",onClick:()=>t(e.code),className:"h-8 px-3",children:[n.jsx("span",{className:"mr-1",children:e.flag}),n.jsx("span",{className:"text-xs font-medium",children:e.code.toUpperCase()})]},e.code))})}function e9({className:e}){let{changeLocale:t,getAvailableLocales:r,common:a}=(0,e4.Q)(),o=r();return n.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("grid grid-cols-2 md:grid-cols-3 gap-3",e),children:o.map(e=>(0,n.jsxs)(s.z,{variant:e.isActive?"default":"outline",onClick:()=>t(e.code),className:"h-16 flex-col space-y-1 relative",children:[e.isActive&&n.jsx(eJ.Z,{className:"absolute top-2 right-2 h-4 w-4"}),n.jsx("span",{className:"text-2xl",children:e.flag}),(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("div",{className:"text-sm font-medium",children:e.name}),n.jsx("div",{className:"text-xs text-muted-foreground",children:e.code.toUpperCase()})]})]},e.code))})}!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},68962:(e,t,r)=>{"use strict";r.d(t,{Q:()=>c});var n=r(83877),a=r(22254),s=r(41026);let o=["en","es","fr","de","ja","zh","af","zu"],i={en:"English",es:"Espa\xf1ol",fr:"Fran\xe7ais",de:"Deutsch",ja:"日本語",zh:"中文",af:"Afrikaans",zu:"isiZulu"},l={en:"\uD83C\uDDFA\uD83C\uDDF8",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",ja:"\uD83C\uDDEF\uD83C\uDDF5",zh:"\uD83C\uDDE8\uD83C\uDDF3",af:"\uD83C\uDDFF\uD83C\uDDE6",zu:"\uD83C\uDDFF\uD83C\uDDE6"};function c(){let e=(0,n.useTranslations)(),t=(0,n.useLocale)(),r=(0,a.useRouter)(),s=(0,a.usePathname)(),c=(t,r)=>{try{return e(t,r)}catch(e){return t}};return{t:c,locale:t,changeLocale:e=>{let n=s.replace(`/${t}`,"")||"/";r.push(`/${e}${n}`)},getAvailableLocales:()=>o.map(e=>({code:e,name:i[e],flag:l[e],isActive:e===t})),formatDate:(e,r)=>new Intl.DateTimeFormat(t,r).format(e),formatNumber:(e,r)=>new Intl.NumberFormat(t,r).format(e),formatCurrency:(e,r="USD")=>new Intl.NumberFormat(t,{style:"currency",currency:r}).format(e),formatRelativeTime:e=>{let r=new Intl.RelativeTimeFormat(t,{numeric:"auto"}),n=new Date,a=Math.floor((e.getTime()-n.getTime())/1e3);if(60>Math.abs(a))return r.format(a,"second");let s=Math.floor(a/60);if(60>Math.abs(s))return r.format(s,"minute");let o=Math.floor(s/60);if(24>Math.abs(o))return r.format(o,"hour");let i=Math.floor(o/24);if(30>Math.abs(i))return r.format(i,"day");let l=Math.floor(i/30);return 12>Math.abs(l)?r.format(l,"month"):r.format(Math.floor(l/12),"year")},isRTL:()=>["ar","he","fa"].includes(t),common:(e,t)=>c(`common.${e}`,t),navigation:(e,t)=>c(`navigation.${e}`,t),auth:(e,t)=>c(`auth.${e}`,t),validation:(e,t)=>c(`validation.${e}`,t),lifeAreas:(e,t)=>c(`lifeAreas.${e}`,t),assessment:(e,t)=>c(`assessment.${e}`,t)}}(0,s.cF)(async({locale:e})=>(o.includes(e)||(0,a.notFound)(),{locale:e,messages:(await r(98491)(`./${e}.json`)).default}))},60192:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>L,generateMetadata:()=>F});var n=r(25036),a=r(40002),s=r(66689),o=(0,a.cache)(function(e,t){return(0,s.eX)({...e,namespace:t})}),i=r(399),l=r(13285),c=r(40646),d=r.n(c),u=r(38634),m=r(44551),p=r(31842),x=r(86843);let f=(0,x.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/i18n/language-switcher.tsx`),{__esModule:h,$$typeof:g}=f;f.default;let j=(0,x.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/i18n/language-switcher.tsx#LanguageSwitcher`);(0,x.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/i18n/language-switcher.tsx#InlineLanguageSwitcher`),(0,x.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/i18n/language-switcher.tsx#LanguageGrid`);var v=r(59508);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let w=(0,v.Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var y=r(6682);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let N=(0,v.Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),b=(0,v.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),D=(0,v.Z)("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]);var M=r(37505);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let C=(0,v.Z)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]]),k=(0,v.Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]);var _=r(61419),O=r(81389),P=r(17236),E=r(81861),R=r(94832);async function F({params:{locale:e}}){let t=await (0,l.Z)({locale:e,namespace:"metadata"});return{title:t("title"),description:t("description")}}let T=[w,y.Z,N,b,D,M.Z,C,y.Z,k,w,_.Z,M.Z];function L({params:{locale:e}}){return(0,n.jsxs)("div",{className:"min-h-screen bg-background",children:[n.jsx("header",{className:"border-b bg-background/95 backdrop-blur",children:(0,n.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(O.Z,{className:"h-8 w-8 text-primary"}),n.jsx("span",{className:"text-xl font-bold",children:"8,760 Hours"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx(j,{variant:"compact"}),n.jsx(d(),{href:`/${e}/login`,children:n.jsx(u.z,{variant:"outline",children:n.jsx(A,{namespace:"auth",keyPrefix:"signIn"})})}),n.jsx(d(),{href:`/${e}/register`,children:n.jsx(u.z,{children:n.jsx(A,{namespace:"auth",keyPrefix:"signUp"})})})]})]})}),n.jsx("section",{className:"py-20 px-4",children:(0,n.jsxs)("div",{className:"container max-w-6xl mx-auto text-center",children:[n.jsx(p.C,{variant:"secondary",className:"mb-4",children:n.jsx(A,{namespace:"common",keyPrefix:"newFeature"})}),n.jsx("h1",{className:"text-4xl md:text-6xl font-bold tracking-tight mb-6",children:n.jsx(A,{namespace:"hero",keyPrefix:"title"})}),n.jsx("p",{className:"text-xl text-muted-foreground mb-8 max-w-3xl mx-auto",children:n.jsx(A,{namespace:"hero",keyPrefix:"description"})}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[n.jsx(d(),{href:`/${e}/register`,children:(0,n.jsxs)(u.z,{size:"lg",className:"text-lg px-8",children:[n.jsx(A,{namespace:"hero",keyPrefix:"getStarted"}),n.jsx(P.Z,{className:"ml-2 h-5 w-5"})]})}),n.jsx(d(),{href:`/${e}/demo`,children:n.jsx(u.z,{variant:"outline",size:"lg",className:"text-lg px-8",children:n.jsx(A,{namespace:"hero",keyPrefix:"watchDemo"})})})]})]})}),n.jsx("section",{className:"py-20 px-4 bg-muted/50",children:(0,n.jsxs)("div",{className:"container max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[n.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:n.jsx(A,{namespace:"features",keyPrefix:"title"})}),n.jsx("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:n.jsx(A,{namespace:"features",keyPrefix:"description"})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[n.jsx(m.Zb,{children:(0,n.jsxs)(m.Ol,{children:[n.jsx(E.Z,{className:"h-12 w-12 text-primary mb-4"}),n.jsx(m.ll,{children:n.jsx(A,{namespace:"features",keyPrefix:"assess.title"})}),n.jsx(m.SZ,{children:n.jsx(A,{namespace:"features",keyPrefix:"assess.description"})})]})}),n.jsx(m.Zb,{children:(0,n.jsxs)(m.Ol,{children:[n.jsx(_.Z,{className:"h-12 w-12 text-primary mb-4"}),n.jsx(m.ll,{children:n.jsx(A,{namespace:"features",keyPrefix:"plan.title"})}),n.jsx(m.SZ,{children:n.jsx(A,{namespace:"features",keyPrefix:"plan.description"})})]})}),n.jsx(m.Zb,{children:(0,n.jsxs)(m.Ol,{children:[n.jsx(R.Z,{className:"h-12 w-12 text-primary mb-4"}),n.jsx(m.ll,{children:n.jsx(A,{namespace:"features",keyPrefix:"achieve.title"})}),n.jsx(m.SZ,{children:n.jsx(A,{namespace:"features",keyPrefix:"achieve.description"})})]})})]})]})}),n.jsx("section",{className:"py-20 px-4",children:(0,n.jsxs)("div",{className:"container max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[n.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:n.jsx(A,{namespace:"lifeAreas",keyPrefix:"title"})}),n.jsx("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:n.jsx(A,{namespace:"lifeAreas",keyPrefix:"description"})})]}),n.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:T.map((e,t)=>(0,n.jsxs)(m.Zb,{className:"text-center p-4 hover:shadow-md transition-shadow",children:[n.jsx(e,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),n.jsx("p",{className:"text-sm font-medium",children:n.jsx(A,{namespace:"lifeAreas",keyPrefix:`area${t+1}`})})]},t))})]})}),n.jsx("section",{className:"py-20 px-4 bg-primary text-primary-foreground",children:(0,n.jsxs)("div",{className:"container max-w-4xl mx-auto text-center",children:[n.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:n.jsx(A,{namespace:"cta",keyPrefix:"title"})}),n.jsx("p",{className:"text-xl mb-8 opacity-90",children:n.jsx(A,{namespace:"cta",keyPrefix:"description"})}),n.jsx(d(),{href:`/${e}/register`,children:(0,n.jsxs)(u.z,{size:"lg",variant:"secondary",className:"text-lg px-8",children:[n.jsx(A,{namespace:"cta",keyPrefix:"startNow"}),n.jsx(P.Z,{className:"ml-2 h-5 w-5"})]})})]})}),n.jsx("footer",{className:"border-t py-12 px-4",children:n.jsx("div",{className:"container max-w-6xl mx-auto",children:(0,n.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-4 md:mb-0",children:[n.jsx(O.Z,{className:"h-6 w-6 text-primary"}),n.jsx("span",{className:"font-bold",children:"8,760 Hours"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx(j,{variant:"compact"}),n.jsx("p",{className:"text-sm text-muted-foreground",children:n.jsx(A,{namespace:"footer",keyPrefix:"copyright"})})]})]})})})]})}function A({namespace:e,keyPrefix:t}){let r=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n]=t;return o(function(e,t){try{return(0,a.use)(t)}catch(t){throw t instanceof TypeError&&t.message.includes("Cannot read properties of null (reading 'use')")?Error("`".concat(e,"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"),{cause:t}):t}}("useTranslations",(0,i.Z)()),n)}(e);return n.jsx(n.Fragment,{children:r(t)})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,8356,3828,1476,1498,1110,6583,8536,9669,4610],()=>r(93537));module.exports=n})();