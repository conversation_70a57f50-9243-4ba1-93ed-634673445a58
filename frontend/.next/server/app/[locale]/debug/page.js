(()=>{var e={};e.id=6077,e.ids=[6077],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94001:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=s(50482),n=s(69108),o=s(62563),a=s.n(o),l=s(68300),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(r,i);let c=["",{children:["[locale]",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62858)),"/mnt/persist/workspace/frontend/src/app/[locale]/debug/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/[locale]/debug/page.tsx"],x="/[locale]/debug/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/[locale]/debug/page",pathname:"/[locale]/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89164:(e,r,s)=>{Promise.resolve().then(s.bind(s,15153))},25545:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1222:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},79200:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},15153:(e,r,s)=>{"use strict";s.r(r),s.d(r,{ErrorTestingPage:()=>b});var t=s(95344),n=s(3729),o=s(23673),a=s(5094),l=s(19591),i=s(46540),c=s(33668),d=s(2690),x=s(81596),u=s(79480),m=s(33733);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,s(69224).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);var g=s(25545),p=s(79200),j=s(45961);function f({shouldThrow:e}){if(e)throw Error("This is a test React component error!");return t.jsx("div",{className:"p-4 bg-green-50 border border-green-200 rounded",children:"Component rendered successfully!"})}function b(){let[e,r]=(0,n.useState)(!1),[s,b]=(0,n.useState)("Custom test error"),[v,N]=(0,n.useState)(1e3),{logError:y,logWarning:w,logCritical:E}=Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}())(),O=async()=>{try{await fetch("/api/nonexistent-endpoint")}catch(e){y(e,{type:"network",endpoint:"/api/nonexistent-endpoint"})}};return t.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Error Testing & Debug Tools"}),t.jsx("p",{className:"text-gray-600",children:"Test various error scenarios and debug the error handling system"}),t.jsx(l.C,{variant:"destructive",className:"mt-2",children:"Development Only"})]}),(0,t.jsxs)(o.Zb,{children:[t.jsx(o.Ol,{children:(0,t.jsxs)(o.ll,{className:"flex items-center space-x-2",children:[t.jsx(u.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Error Statistics"})]})}),t.jsx(o.aY,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-red-600",children:Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).getErrors().length}),t.jsx("div",{className:"text-sm text-red-600",children:"Memory Errors"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-orange-600",children:Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).getErrorsFromStorage().length}),t.jsx("div",{className:"text-sm text-orange-600",children:"Stored Errors"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-blue-600",children:Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).getErrors().length+Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).getErrorsFromStorage().length}),t.jsx("div",{className:"text-sm text-blue-600",children:"Total Errors"})]}),t.jsx("div",{className:"text-center p-4",children:(0,t.jsxs)(a.z,{onClick:()=>{Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).clearErrors(),window.location.reload()},variant:"outline",size:"sm",children:[t.jsx(m.Z,{className:"h-4 w-4 mr-1"}),"Clear All"]})})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)(o.Zb,{children:[(0,t.jsxs)(o.Ol,{children:[(0,t.jsxs)(o.ll,{className:"flex items-center space-x-2",children:[t.jsx(h,{className:"h-5 w-5 text-red-600"}),t.jsx("span",{children:"JavaScript Errors"})]}),t.jsx(o.SZ,{children:"Test various JavaScript error types"})]}),(0,t.jsxs)(o.aY,{className:"space-y-4",children:[t.jsx(a.z,{onClick:()=>{throw Error("Test JavaScript error from button click")},variant:"destructive",className:"w-full",children:"Throw JavaScript Error"}),t.jsx(a.z,{onClick:()=>{null.someProperty.nestedProperty="value"},variant:"destructive",className:"w-full",children:"Throw TypeError"}),t.jsx(a.z,{onClick:()=>{undefinedVariable.someMethod()},variant:"destructive",className:"w-full",children:"Throw ReferenceError"}),t.jsx(a.z,{onClick:()=>{},variant:"outline",className:"w-full",children:"Console Error"})]})]}),(0,t.jsxs)(o.Zb,{children:[(0,t.jsxs)(o.Ol,{children:[(0,t.jsxs)(o.ll,{className:"flex items-center space-x-2",children:[t.jsx(g.Z,{className:"h-5 w-5 text-orange-600"}),t.jsx("span",{children:"Async Errors"})]}),t.jsx(o.SZ,{children:"Test asynchronous error scenarios"})]}),(0,t.jsxs)(o.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(c._,{htmlFor:"delay",children:"Async Delay (ms)"}),t.jsx(i.I,{id:"delay",type:"number",value:v,onChange:e=>N(Number(e.target.value)),min:"100",max:"5000"})]}),t.jsx(a.z,{onClick:()=>{setTimeout(()=>{throw Error(`Test async error after ${v}ms delay`)},v)},variant:"destructive",className:"w-full",children:"Throw Async Error"}),t.jsx(a.z,{onClick:()=>{Promise.reject(Error("Test unhandled promise rejection"))},variant:"destructive",className:"w-full",children:"Promise Rejection"}),t.jsx(a.z,{onClick:O,variant:"outline",className:"w-full",children:"Network Error"})]})]}),(0,t.jsxs)(o.Zb,{children:[(0,t.jsxs)(o.Ol,{children:[(0,t.jsxs)(o.ll,{className:"flex items-center space-x-2",children:[t.jsx(p.Z,{className:"h-5 w-5 text-blue-600"}),t.jsx("span",{children:"React Component Errors"})]}),t.jsx(o.SZ,{children:"Test React component error boundaries"})]}),(0,t.jsxs)(o.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",id:"reactError",checked:e,onChange:e=>r(e.target.checked)}),t.jsx(c._,{htmlFor:"reactError",children:"Throw React Error"})]}),t.jsx(x.SV,{fallback:x.ku,children:t.jsx(f,{shouldThrow:e})})]})]}),(0,t.jsxs)(o.Zb,{children:[(0,t.jsxs)(o.Ol,{children:[(0,t.jsxs)(o.ll,{className:"flex items-center space-x-2",children:[t.jsx(j.Z,{className:"h-5 w-5 text-purple-600"}),t.jsx("span",{children:"Custom Error Logging"})]}),t.jsx(o.SZ,{children:"Test custom error logging with different severity levels"})]}),(0,t.jsxs)(o.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(c._,{htmlFor:"customMessage",children:"Custom Error Message"}),t.jsx(d.g,{id:"customMessage",value:s,onChange:e=>b(e.target.value),rows:2})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[t.jsx(a.z,{onClick:()=>{w(Error("This is a warning-level error"),{type:"warning",severity:"low"})},variant:"outline",size:"sm",children:"Log Warning"}),t.jsx(a.z,{onClick:()=>{y(Error(s),{type:"custom",userAction:"test-button-click",timestamp:new Date().toISOString()})},variant:"outline",size:"sm",children:"Log Error"}),t.jsx(a.z,{onClick:()=>{E(Error("This is a critical error!"),{type:"critical",impact:"high",requiresImmediate:!0})},variant:"destructive",size:"sm",children:"Log Critical"}),t.jsx(a.z,{onClick:()=>Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).simulateError(s),variant:"outline",size:"sm",children:"Simulate Error"})]})]})]})]}),(0,t.jsxs)(o.Zb,{children:[t.jsx(o.Ol,{children:t.jsx(o.ll,{children:"How to Use"})}),t.jsx(o.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Debug Panel"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Click the red bug icon in the bottom-right corner to open the debug panel and view all captured errors with detailed information."})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Console Logging"}),t.jsx("p",{className:"text-sm text-gray-600",children:"All errors are automatically logged to the browser console with enhanced formatting and context information."})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Error Persistence"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Errors are stored in localStorage and persist across page reloads for debugging purposes."})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Error Boundaries"}),t.jsx("p",{className:"text-sm text-gray-600",children:"React component errors are caught by error boundaries and display user-friendly fallback interfaces."})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[t.jsx("h3",{className:"font-medium text-blue-900 mb-2",children:"Console Commands"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsxs)("p",{children:[t.jsx("code",{children:"errorLogger.getErrors()"})," - View all errors in memory"]}),(0,t.jsxs)("p",{children:[t.jsx("code",{children:"errorLogger.clearErrors()"})," - Clear all errors"]}),(0,t.jsxs)("p",{children:[t.jsx("code",{children:"errorLogger.exportErrors()"})," - Export errors as JSON"]}),(0,t.jsxs)("p",{children:[t.jsx("code",{children:"errorLogger.simulateError()"})," - Simulate a test error"]})]})]})]})})]})]})})}!function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()},46540:(e,r,s)=>{"use strict";s.d(r,{I:()=>i});var t=s(95344),n=s(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var o=s(66138),a=s(1222),l=s(53148);let i=n.forwardRef(({className:e,type:r,label:s,error:i,helperText:c,leftIcon:d,rightIcon:x,variant:u="default",id:m,required:h,...g},p)=>{let[j,f]=n.useState(!1),[b]=n.useState(()=>m||`input-${Math.random().toString(36).substr(2,9)}`),v="password"===r,N=v&&j?"text":r,y=!!i||"error"===u;return(0,t.jsxs)("div",{className:"space-y-2",children:[s&&(0,t.jsxs)("label",{htmlFor:b,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[s,h&&t.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",children:[d&&t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:d})}),t.jsx("input",{type:N,id:b,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",y&&"border-destructive focus-visible:ring-destructive","success"===u&&"border-green-500 focus-visible:ring-green-500",d&&"pl-10",(x||v||y)&&"pr-10",e),ref:p,"aria-invalid":y,"aria-describedby":i?`${b}-error`:c?`${b}-helper`:void 0,...g}),y&&t.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:t.jsx(o.Z,{className:"h-4 w-4 text-destructive"})}),v&&t.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{f(!j)},"aria-label":j?"Hide password":"Show password",children:j?t.jsx(a.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):t.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),x&&!y&&!v&&t.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:t.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:x})})]}),i&&t.jsx("p",{id:`${b}-error`,className:"text-sm text-destructive",role:"alert",children:i}),c&&!i&&t.jsx("p",{id:`${b}-helper`,className:"text-sm text-muted-foreground",children:c})]})});i.displayName="Input"},33668:(e,r,s)=>{"use strict";s.d(r,{_:()=>c});var t=s(95344),n=s(3729),o=s(62409),a=n.forwardRef((e,r)=>(0,t.jsx)(o.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));a.displayName="Label";var l=s(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let i=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef(({className:e,...r},s)=>t.jsx(a,{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(i(),e),...r}));c.displayName=a.displayName},2690:(e,r,s)=>{"use strict";s.d(r,{g:()=>o});var t=s(95344),n=s(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=n.forwardRef(({className:e,...r},s)=>t.jsx("textarea",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));o.displayName="Textarea"},62858:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c,metadata:()=>i});var t=s(25036),n=s(86843);let o=(0,n.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/debug/error-testing-page.tsx`),{__esModule:a,$$typeof:l}=o;o.default,(0,n.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/debug/error-testing-page.tsx#ErrorTestingPage`);let i={title:"Debug & Error Testing - 8,760 Hours",description:"Debug tools and error testing for development."};function c(){return t.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Page Not Found"}),t.jsx("p",{className:"text-gray-600",children:"This page is only available in development mode."})]})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[1638,8356,3828,1498,9669],()=>s(94001));module.exports=t})();