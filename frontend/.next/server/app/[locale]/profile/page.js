(()=>{var e={};e.id=9950,e.ids=[9950],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},67024:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=t(50482),a=t(69108),n=t(62563),i=t.n(n),l=t(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o=["",{children:["[locale]",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38416)),"/mnt/persist/workspace/frontend/src/app/[locale]/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/[locale]/profile/page.tsx"],x="/[locale]/profile/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/profile/page",pathname:"/[locale]/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},81373:(e,s,t)=>{Promise.resolve().then(t.bind(t,86062)),Promise.resolve().then(t.bind(t,35445))},50340:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},63211:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},2273:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},48120:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},98200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},21096:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},20783:(e,s,t)=>{e.exports=t(61476)},86062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{DashboardLayout:()=>b});var r=t(95344),a=t(3729),n=t(20783),i=t.n(n),l=t(22254);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var c=t(5094),o=t(2273),d=t(50340),x=t(17910),m=t(46064),h=t(21096),u=t(63211),p=t(18822),f=t(13746),g=t(14513),j=t(48120),N=t(98200);let v=[{name:"Dashboard",href:"/dashboard",icon:o.Z},{name:"Assessment",href:"/assessment",icon:d.Z},{name:"Planning",href:"/planning",icon:x.Z},{name:"Progress",href:"/progress",icon:m.Z},{name:"Analytics",href:"/analytics",icon:h.Z},{name:"Reviews",href:"/reviews",icon:u.Z}],y=[{name:"Profile",href:"/profile",icon:p.Z},{name:"Settings",href:"/settings",icon:f.Z}];function b({children:e}){let[s,t]=(0,a.useState)(!1),n=(0,l.usePathname)();return(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("fixed inset-0 z-50 lg:hidden",s?"block":"hidden"),children:[r.jsx("div",{className:"fixed inset-0 bg-black/50",onClick:()=>t(!1)}),(0,r.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-card",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[r.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"}),r.jsx(c.z,{variant:"ghost",size:"sm",onClick:()=>t(!1),children:r.jsx(g.Z,{className:"h-5 w-5"})})]}),r.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:v.map(e=>{let s=n.startsWith(e.href);return(0,r.jsxs)(i(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),onClick:()=>t(!1),children:[r.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",s?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})]}),r.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,r.jsxs)("div",{className:"flex flex-col flex-grow bg-card border-r border-border",children:[r.jsx("div",{className:"flex h-16 items-center px-4",children:r.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"})}),r.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:v.map(e=>{let s=n.startsWith(e.href);return(0,r.jsxs)(i(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),children:[r.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",s?"text-primary-foreground":"text-muted-foreground group-hover:text-foreground")}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-border p-2",children:[y.map(e=>(0,r.jsxs)(i(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[r.jsx(e.icon,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),e.name]},e.name)),(0,r.jsxs)("button",{className:"group flex w-full items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[r.jsx(j.Z,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),"Sign out"]})]})]})}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[(0,r.jsxs)("div",{className:"sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-border bg-background px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8",children:[r.jsx(c.z,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>t(!0),children:r.jsx(N.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:[r.jsx("div",{className:"flex flex-1"}),r.jsx("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:r.jsx("div",{className:"relative","data-testid":"user-menu-trigger",children:(0,r.jsxs)(c.z,{variant:"ghost",size:"sm",className:"flex items-center space-x-2",children:[r.jsx("div",{className:"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-sm font-medium text-white",children:"JD"})}),r.jsx("span",{className:"hidden lg:block text-sm font-medium text-foreground",children:"John Doe"})]})})})]})]}),r.jsx("main",{className:"py-8 px-4 sm:px-6 lg:px-8",children:e})]})]})}},35445:(e,s,t)=>{"use strict";t.r(s),t.d(s,{ProfileContent:()=>_});var r=t(95344),a=t(3729),n=t(22254),i=t(23673),l=t(5094),c=t(46540),o=t(33668),d=t(2690),x=t(12772),m=t(19591),h=t(50909),u=t(60339),p=t(33468),f=t(31498),g=t(75695),j=t(18822),N=t(13746),v=t(33037),y=t(70009),b=t(51765),w=t(17910),k=t(46064),Z=t(25545),C=t(55794),P=t(23485);function _(){let{user:e,isAuthenticated:s,isLoading:t}=(0,p.t)(),_=(0,n.useRouter)(),{toast:O}=(0,u.pm)(),[D,E]=(0,a.useState)(!1),[M,z]=(0,a.useState)(!1),[S,F]=(0,a.useState)({firstName:"",lastName:"",email:"",phone:"",location:"",timezone:"UTC",bio:"",website:"",joinedDate:new Date,preferences:{emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,darkMode:!0,language:"en"},stats:{assessmentsCompleted:0,goalsSet:0,progressEntries:0,daysActive:0}});(0,a.useEffect)(()=>{t||s||_.push("/login")},[s,t,_]),(0,a.useEffect)(()=>{e&&F(s=>({...s,firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",joinedDate:e.createdAt?new Date(e.createdAt):new Date}))},[e]);let A=async()=>{z(!0);try{await new Promise(e=>setTimeout(e,1e3)),O({title:"Profile Updated",description:"Your profile has been saved successfully."}),E(!1)}catch(e){O({title:"Save Failed",description:"Failed to save your profile. Please try again.",variant:"destructive"})}finally{z(!1)}},U=(e,s)=>{F(t=>({...t,[e]:s}))},L=(e,s)=>{F(t=>({...t,preferences:{...t.preferences,[e]:s}}))};return t?r.jsx("div",{className:"flex items-center justify-center py-8",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):s&&e?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Profile"}),r.jsx("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences"})]}),r.jsx("div",{className:"flex items-center space-x-2",children:D?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.z,{variant:"outline",onClick:()=>E(!1),disabled:M,children:"Cancel"}),(0,r.jsxs)(l.z,{onClick:A,disabled:M,children:[r.jsx(f.Z,{className:"mr-2 h-4 w-4"}),M?"Saving...":"Save Changes"]})]}):(0,r.jsxs)(l.z,{onClick:()=>E(!0),children:[r.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Edit Profile"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(j.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Basic Information"})]}),r.jsx(i.SZ,{children:"Your personal details and contact information"})]}),(0,r.jsxs)(i.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{htmlFor:"firstName",children:"First Name"}),r.jsx(c.I,{id:"firstName",value:S.firstName,onChange:e=>U("firstName",e.target.value),disabled:!D})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{htmlFor:"lastName",children:"Last Name"}),r.jsx(c.I,{id:"lastName",value:S.lastName,onChange:e=>U("lastName",e.target.value),disabled:!D})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{htmlFor:"email",children:"Email"}),r.jsx(c.I,{id:"email",type:"email",value:S.email,onChange:e=>U("email",e.target.value),disabled:!D})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{htmlFor:"phone",children:"Phone"}),r.jsx(c.I,{id:"phone",value:S.phone,onChange:e=>U("phone",e.target.value),disabled:!D,placeholder:"+****************"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{htmlFor:"location",children:"Location"}),r.jsx(c.I,{id:"location",value:S.location,onChange:e=>U("location",e.target.value),disabled:!D,placeholder:"City, Country"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{htmlFor:"bio",children:"Bio"}),r.jsx(d.g,{id:"bio",value:S.bio,onChange:e=>U("bio",e.target.value),disabled:!D,placeholder:"Tell us about yourself...",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{htmlFor:"website",children:"Website"}),r.jsx(c.I,{id:"website",value:S.website,onChange:e=>U("website",e.target.value),disabled:!D,placeholder:"https://yourwebsite.com"})]})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(N.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Preferences"})]}),r.jsx(i.SZ,{children:"Customize your experience and notification settings"})]}),(0,r.jsxs)(i.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium flex items-center space-x-2",children:[r.jsx(v.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Notifications"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(o._,{children:"Email Notifications"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive updates and reminders via email"})]}),r.jsx(x.r,{checked:S.preferences.emailNotifications,onCheckedChange:e=>L("emailNotifications",e),disabled:!D})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(o._,{children:"Push Notifications"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Get instant notifications in your browser"})]}),r.jsx(x.r,{checked:S.preferences.pushNotifications,onCheckedChange:e=>L("pushNotifications",e),disabled:!D})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(o._,{children:"Weekly Reports"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive weekly progress summaries"})]}),r.jsx(x.r,{checked:S.preferences.weeklyReports,onCheckedChange:e=>L("weeklyReports",e),disabled:!D})]})]})]}),r.jsx(h.Z,{}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Appearance"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(o._,{children:"Dark Mode"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Use dark theme for better eye comfort"})]}),r.jsx(x.r,{checked:S.preferences.darkMode,onCheckedChange:e=>L("darkMode",e),disabled:!D})]})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"h-24 w-24 rounded-full bg-primary flex items-center justify-center",children:r.jsx("span",{className:"text-2xl font-bold text-primary-foreground",children:(S.firstName?.[0]||e.email?.[0]||"U").toUpperCase()})}),D&&r.jsx(l.z,{size:"sm",variant:"outline",className:"absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0",children:r.jsx(b.Z,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h3",{className:"font-semibold text-foreground",children:S.firstName&&S.lastName?`${S.firstName} ${S.lastName}`:e.email}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Member since ",S.joinedDate.toLocaleDateString()]})]})]})})}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{className:"text-lg",children:"Your Progress"}),r.jsx(i.SZ,{children:"Overview of your life planning journey"})]}),r.jsx(i.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg",children:[r.jsx(w.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:S.stats.assessmentsCompleted}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Assessments"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg",children:[r.jsx(k.Z,{className:"h-6 w-6 text-green-600 dark:text-green-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:S.stats.goalsSet}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Goals Set"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg",children:[r.jsx(Z.Z,{className:"h-6 w-6 text-purple-600 dark:text-purple-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:S.stats.progressEntries}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Progress Entries"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg",children:[r.jsx(C.Z,{className:"h-6 w-6 text-orange-600 dark:text-orange-400 mx-auto mb-1"}),r.jsx("div",{className:"text-2xl font-bold text-foreground",children:S.stats.daysActive}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Days Active"})]})]})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"text-lg flex items-center space-x-2",children:[r.jsx(P.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Account Status"})]})}),(0,r.jsxs)(i.aY,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm",children:"Account Type"}),r.jsx(m.C,{variant:"default",children:"Free"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm",children:"Email Verified"}),r.jsx(m.C,{variant:"outline",className:"text-green-600 border-green-600",children:"Verified"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm",children:"Two-Factor Auth"}),r.jsx(m.C,{variant:"outline",className:"text-orange-600 border-orange-600",children:"Not Enabled"})]}),r.jsx(h.Z,{}),r.jsx(l.z,{variant:"outline",size:"sm",className:"w-full",children:"Upgrade to Pro"})]})]})]})]})]}):null}},13285:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});var r=t(40002),a=t(66689),n=t(399),i=(0,r.cache)(async function(e){let s,t;"string"==typeof e?s=e:e&&(t=e.locale,s=e.namespace);let r=await (0,n.Z)(t);return(0,a.eX)({...r,namespace:s,messages:r.messages})})},38416:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m,generateMetadata:()=>x});var r=t(25036),a=t(13285),n=t(97385),i=t(86843);let l=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/profile/profile-content.tsx`),{__esModule:c,$$typeof:o}=l;l.default;let d=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/profile/profile-content.tsx#ProfileContent`);async function x({params:{locale:e}}){let s=await (0,a.Z)({locale:e,namespace:"profile"});return{title:s("title"),description:s("description")}}function m(){return r.jsx(n.c,{children:r.jsx(d,{})})}},97385:(e,s,t)=>{"use strict";t.d(s,{c:()=>l});var r=t(86843);let a=(0,r.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let l=(0,r.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx#DashboardLayout`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,8356,3828,1476,1498,2372,9669,12],()=>t(67024));module.exports=r})();