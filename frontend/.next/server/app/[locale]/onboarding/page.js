(()=>{var e={};e.id=7179,e.ids=[7179],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84679:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(50482),a=t(69108),n=t(62563),l=t.n(n),i=t(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let c=["",{children:["[locale]",{children:["onboarding",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,61700)),"/mnt/persist/workspace/frontend/src/app/[locale]/onboarding/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/[locale]/onboarding/page.tsx"],m="/[locale]/onboarding/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/onboarding/page",pathname:"/[locale]/onboarding",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},38139:(e,s,t)=>{Promise.resolve().then(t.bind(t,33169))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35299:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},50340:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62312:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},35341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},57320:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},17910:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},33169:(e,s,t)=>{"use strict";t.r(s),t.d(s,{OnboardingFlow:()=>w});var r=t(95344),a=t(3729),n=t(68962),l=t(23673),i=t(5094),o=t(47210),c=t(19591),d=t(46540),m=t(2690),u=t(33668),x=t(80833);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,t(69224).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var f=t(50340),p=t(17910),g=t(7060),j=t(57320),y=t(35341),v=t(63024),b=t(35299),N=t(25545);function w(){let{common:e,lifeAreas:s}=(0,n.Q)(),[t,w]=(0,a.useState)("welcome"),[k,D]=(0,a.useState)({name:"",primaryGoal:"",interestedAreas:[],timeCommitment:"",experience:"",notifications:!0}),C=[{id:"welcome",title:"Welcome",description:"Welcome to 8,760 Hours"},{id:"methodology",title:"Methodology",description:"Learn our approach"},{id:"life-areas",title:"Life Areas",description:"Explore the 12 areas"},{id:"expectations",title:"Your Goals",description:"Set expectations"},{id:"ready",title:"Ready",description:"Start your journey"}],E=C.findIndex(e=>e.id===t),M=(E+1)/C.length*100,O=()=>{let e=E+1;e<C.length&&w(C[e].id)},Z=()=>{let e=E-1;e>=0&&w(C[e].id)};return"welcome"===t?r.jsx("div",{className:"max-w-4xl mx-auto space-y-8",children:(0,r.jsxs)("div",{className:"text-center space-y-6",children:[r.jsx("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-full",children:r.jsx(h,{className:"h-10 w-10 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Welcome to 8,760 Hours"}),r.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Transform your life with systematic planning. Every year has 8,760 hours – let's make them count by creating a comprehensive plan for meaningful growth."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12",children:[r.jsx(l.Zb,{className:"text-center",children:(0,r.jsxs)(l.aY,{className:"p-6",children:[r.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4",children:r.jsx(f.Z,{className:"h-6 w-6 text-blue-600"})}),r.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Assess"}),r.jsx("p",{className:"text-sm text-gray-600",children:"Evaluate your current satisfaction across 12 key life areas"})]})}),r.jsx(l.Zb,{className:"text-center",children:(0,r.jsxs)(l.aY,{className:"p-6",children:[r.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4",children:r.jsx(p.Z,{className:"h-6 w-6 text-green-600"})}),r.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Plan"}),r.jsx("p",{className:"text-sm text-gray-600",children:"Create meaningful goals and actionable plans for growth"})]})}),r.jsx(l.Zb,{className:"text-center",children:(0,r.jsxs)(l.aY,{className:"p-6",children:[r.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4",children:r.jsx(g.Z,{className:"h-6 w-6 text-purple-600"})}),r.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Achieve"}),r.jsx("p",{className:"text-sm text-gray-600",children:"Track progress and celebrate your wins throughout the year"})]})})]}),r.jsx("div",{className:"pt-8",children:(0,r.jsxs)(i.z,{size:"lg",className:"px-8",onClick:O,children:[r.jsx(j.Z,{className:"mr-2 h-5 w-5"}),"Get Started"]})})]})}):"methodology"===t?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"The 8,760 Hours Methodology"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Step ",E+1," of ",C.length]})]}),(0,r.jsxs)(c.C,{variant:"outline",className:"text-sm",children:[Math.round(M),"% Complete"]})]}),r.jsx(o.E,{value:M,className:"h-2"})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-5 w-5 text-yellow-500"}),r.jsx("span",{children:"Our Systematic Approach"})]}),r.jsx(l.SZ,{children:"Understanding the foundation of effective life planning"})]}),r.jsx(l.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-900 mb-3",children:"Why 8,760 Hours?"}),r.jsx("p",{className:"text-gray-600 text-sm leading-relaxed",children:"Every year contains exactly 8,760 hours. This finite number reminds us that time is precious and should be invested intentionally. Our methodology helps you allocate these hours across all areas of life for balanced, meaningful growth."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-900 mb-3",children:"The 12 Life Areas Framework"}),r.jsx("p",{className:"text-gray-600 text-sm leading-relaxed",children:"We organize life into 12 fundamental areas that cover every aspect of human experience. This comprehensive approach ensures nothing important gets overlooked in your planning."})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-900 mb-3",children:"Systematic Planning Process"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:r.jsx("span",{className:"text-xs font-medium text-blue-600",children:"1"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium text-gray-900 text-sm",children:"Assess Current State"}),r.jsx("p",{className:"text-gray-600 text-xs",children:"Rate satisfaction in each life area"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:r.jsx("span",{className:"text-xs font-medium text-green-600",children:"2"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium text-gray-900 text-sm",children:"Set Strategic Goals"}),r.jsx("p",{className:"text-gray-600 text-xs",children:"Create meaningful, achievable objectives"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:r.jsx("span",{className:"text-xs font-medium text-purple-600",children:"3"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium text-gray-900 text-sm",children:"Track & Adjust"}),r.jsx("p",{className:"text-gray-600 text-xs",children:"Monitor progress and adapt as needed"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[r.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCA1 Key Insight"}),r.jsx("p",{className:"text-sm text-blue-800",children:"Success comes from consistent small improvements across all life areas, not just focusing on one area at a time."})]})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(i.z,{variant:"outline",onClick:Z,children:[r.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Previous"]}),(0,r.jsxs)(i.z,{onClick:O,children:["Continue",r.jsx(b.Z,{className:"ml-2 h-4 w-4"})]})]})]}):"life-areas"===t?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"The 12 Life Areas"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Step ",E+1," of ",C.length]})]}),(0,r.jsxs)(c.C,{variant:"outline",className:"text-sm",children:[Math.round(M),"% Complete"]})]}),r.jsx(o.E,{value:M,className:"h-2"})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[r.jsx(l.ll,{children:"Explore All Areas of Life"}),r.jsx(l.SZ,{children:"These 12 areas cover every aspect of human experience and fulfillment"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>r.jsx("div",{className:"p-4 border rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-lg flex-shrink-0",children:r.jsx(e.icon,{className:"h-5 w-5 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium text-gray-900 text-sm mb-1",children:s(`${e.key}.name`)}),r.jsx("p",{className:"text-xs text-gray-600 leading-relaxed",children:s(`${e.key}.description`)})]})]})},e.id))}),(0,r.jsxs)("div",{className:"mt-6 bg-green-50 rounded-lg p-4",children:[r.jsx("h4",{className:"font-medium text-green-900 mb-2",children:"\uD83C\uDFAF Balanced Approach"}),r.jsx("p",{className:"text-sm text-green-800",children:"The magic happens when you improve multiple areas simultaneously. Progress in one area often supports growth in others, creating positive momentum."})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(i.z,{variant:"outline",onClick:Z,children:[r.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Previous"]}),(0,r.jsxs)(i.z,{onClick:O,children:["Continue",r.jsx(b.Z,{className:"ml-2 h-4 w-4"})]})]})]}):"expectations"===t?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Tell Us About Your Goals"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Step ",E+1," of ",C.length]})]}),(0,r.jsxs)(c.C,{variant:"outline",className:"text-sm",children:[Math.round(M),"% Complete"]})]}),r.jsx(o.E,{value:M,className:"h-2"})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[r.jsx(l.ll,{children:"Personalize Your Experience"}),r.jsx(l.SZ,{children:"Help us tailor the platform to your needs and goals"})]}),(0,r.jsxs)(l.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(u._,{htmlFor:"name",children:"What should we call you?"}),r.jsx(d.I,{id:"name",placeholder:"Your first name",value:k.name,onChange:e=>D(s=>({...s,name:e.target.value}))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(u._,{htmlFor:"primaryGoal",children:"What's your main goal for this year?"}),r.jsx(m.g,{id:"primaryGoal",placeholder:"e.g., Improve work-life balance, get healthier, advance my career...",value:k.primaryGoal,onChange:e=>D(s=>({...s,primaryGoal:e.target.value})),rows:3})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(u._,{children:"Which life areas interest you most? (Select 3-5)"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).slice(0,8).map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(x.X,{id:e.id,checked:k.interestedAreas.includes(e.id),onCheckedChange:s=>{s?D(s=>({...s,interestedAreas:[...s.interestedAreas,e.id]})):D(s=>({...s,interestedAreas:s.interestedAreas.filter(s=>s!==e.id)}))}}),r.jsx(u._,{htmlFor:e.id,className:"text-sm cursor-pointer",children:e.name})]},e.id))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(u._,{children:"How much time can you dedicate to planning each week?"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[{value:"15-30min",label:"15-30 minutes",icon:N.Z},{value:"30-60min",label:"30-60 minutes",icon:N.Z},{value:"1hour+",label:"1+ hours",icon:N.Z}].map(e=>r.jsx("button",{type:"button",onClick:()=>D(s=>({...s,timeCommitment:e.value})),className:`p-4 border rounded-lg text-left transition-colors ${k.timeCommitment===e.value?"border-primary bg-primary/5":"border-gray-200 hover:border-gray-300"}`,children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(e.icon,{className:"h-5 w-5 text-gray-500"}),r.jsx("span",{className:"font-medium text-gray-900",children:e.label})]})},e.value))})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(i.z,{variant:"outline",onClick:Z,children:[r.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Previous"]}),(0,r.jsxs)(i.z,{onClick:O,disabled:!k.name||!k.primaryGoal,children:["Continue",r.jsx(b.Z,{className:"ml-2 h-4 w-4"})]})]})]}):r.jsx("div",{className:"max-w-4xl mx-auto space-y-8",children:(0,r.jsxs)("div",{className:"text-center space-y-6",children:[r.jsx("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full",children:r.jsx(g.Z,{className:"h-10 w-10 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:["You're All Set, ",k.name,"!"]}),r.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Welcome to your systematic life planning journey. Let's start by assessing your current satisfaction across all life areas."})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 max-w-2xl mx-auto",children:[r.jsx("h3",{className:"font-semibold text-blue-900 mb-3",children:"Your Next Steps:"}),(0,r.jsxs)("div",{className:"space-y-2 text-left",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:r.jsx("span",{className:"text-xs font-medium text-blue-600",children:"1"})}),r.jsx("span",{className:"text-sm text-blue-800",children:"Complete your life areas assessment (15-20 minutes)"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:r.jsx("span",{className:"text-xs font-medium text-blue-600",children:"2"})}),r.jsx("span",{className:"text-sm text-blue-800",children:"Review your results and insights"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:r.jsx("span",{className:"text-xs font-medium text-blue-600",children:"3"})}),r.jsx("span",{className:"text-sm text-blue-800",children:"Create your annual planning session"})]})]})]}),r.jsx("div",{className:"pt-4",children:(0,r.jsxs)(i.z,{size:"lg",className:"px-8",onClick:()=>{},children:[r.jsx(p.Z,{className:"mr-2 h-5 w-5"}),"Start My Assessment"]})})]})})}!function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()},80833:(e,s,t)=>{"use strict";t.d(s,{X:()=>O});var r=t(95344),a=t(3729),n=t(31405),l=t(98462),i=t(85222),o=t(33183),c=t(92062),d=t(63085),m=t(43234),u=t(62409),x="Checkbox",[h,f]=(0,l.b)(x),[p,g]=h(x);function j(e){let{__scopeCheckbox:s,checked:t,children:n,defaultChecked:l,disabled:i,form:c,name:d,onCheckedChange:m,required:u,value:h="on",internal_do_not_use_render:f}=e,[g,j]=(0,o.T)({prop:t,defaultProp:l??!1,onChange:m,caller:x}),[y,v]=a.useState(null),[b,N]=a.useState(null),w=a.useRef(!1),k=!y||!!c||!!y.closest("form"),D={checked:g,disabled:i,setChecked:j,control:y,setControl:v,name:d,form:c,value:h,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!C(l)&&l,isFormControl:k,bubbleInput:b,setBubbleInput:N};return(0,r.jsx)(p,{scope:s,...D,children:"function"==typeof f?f(D):n})}var y="CheckboxTrigger",v=a.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...l},o)=>{let{control:c,value:d,disabled:m,checked:x,required:h,setControl:f,setChecked:p,hasConsumerStoppedPropagationRef:j,isFormControl:v,bubbleInput:b}=g(y,e),N=(0,n.e)(o,f),w=a.useRef(x);return a.useEffect(()=>{let e=c?.form;if(e){let s=()=>p(w.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[c,p]),(0,r.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":C(x)?"mixed":x,"aria-required":h,"data-state":E(x),"data-disabled":m?"":void 0,disabled:m,value:d,...l,ref:N,onKeyDown:(0,i.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(t,e=>{p(e=>!!C(e)||!e),b&&v&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})})});v.displayName=y;var b=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:a,checked:n,defaultChecked:l,required:i,disabled:o,value:c,onCheckedChange:d,form:m,...u}=e;return(0,r.jsx)(j,{__scopeCheckbox:t,checked:n,defaultChecked:l,disabled:o,required:i,onCheckedChange:d,name:a,form:m,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v,{...u,ref:s,__scopeCheckbox:t}),e&&(0,r.jsx)(D,{__scopeCheckbox:t})]})})});b.displayName=x;var N="CheckboxIndicator",w=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:a,...n}=e,l=g(N,t);return(0,r.jsx)(m.z,{present:a||C(l.checked)||!0===l.checked,children:(0,r.jsx)(u.WV.span,{"data-state":E(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:s,style:{pointerEvents:"none",...e.style}})})});w.displayName=N;var k="CheckboxBubbleInput",D=a.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:l,hasConsumerStoppedPropagationRef:i,checked:o,defaultChecked:m,required:x,disabled:h,name:f,value:p,form:j,bubbleInput:y,setBubbleInput:v}=g(k,e),b=(0,n.e)(t,v),N=(0,c.D)(o),w=(0,d.t)(l);a.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!i.current;if(N!==o&&e){let t=new Event("click",{bubbles:s});y.indeterminate=C(o),e.call(y,!C(o)&&o),y.dispatchEvent(t)}},[y,N,o,i]);let D=a.useRef(!C(o)&&o);return(0,r.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:m??D.current,required:x,disabled:h,name:f,value:p,form:j,...s,tabIndex:-1,ref:b,style:{...s.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function E(e){return C(e)?"indeterminate":e?"checked":"unchecked"}D.displayName=k;var M=t(62312);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let O=a.forwardRef(({className:e,...s},t)=>r.jsx(b,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:r.jsx(w,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center justify-center text-current"),children:r.jsx(M.Z,{className:"h-4 w-4"})})}));O.displayName=b.displayName},46540:(e,s,t)=>{"use strict";t.d(s,{I:()=>o});var r=t(95344),a=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var n=t(66138),l=t(1222),i=t(53148);let o=a.forwardRef(({className:e,type:s,label:t,error:o,helperText:c,leftIcon:d,rightIcon:m,variant:u="default",id:x,required:h,...f},p)=>{let[g,j]=a.useState(!1),[y]=a.useState(()=>x||`input-${Math.random().toString(36).substr(2,9)}`),v="password"===s,b=v&&g?"text":s,N=!!o||"error"===u;return(0,r.jsxs)("div",{className:"space-y-2",children:[t&&(0,r.jsxs)("label",{htmlFor:y,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[t,h&&r.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[d&&r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:d})}),r.jsx("input",{type:b,id:y,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",N&&"border-destructive focus-visible:ring-destructive","success"===u&&"border-green-500 focus-visible:ring-green-500",d&&"pl-10",(m||v||N)&&"pr-10",e),ref:p,"aria-invalid":N,"aria-describedby":o?`${y}-error`:c?`${y}-helper`:void 0,...f}),N&&r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(n.Z,{className:"h-4 w-4 text-destructive"})}),v&&r.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{j(!g)},"aria-label":g?"Hide password":"Show password",children:g?r.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):r.jsx(i.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),m&&!N&&!v&&r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:m})})]}),o&&r.jsx("p",{id:`${y}-error`,className:"text-sm text-destructive",role:"alert",children:o}),c&&!o&&r.jsx("p",{id:`${y}-helper`,className:"text-sm text-muted-foreground",children:c})]})});o.displayName="Input"},33668:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var r=t(95344),a=t(3729),n=t(62409),l=a.forwardRef((e,s)=>(0,r.jsx)(n.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var i=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...s},t)=>r.jsx(l,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o(),e),...s}));c.displayName=l.displayName},47210:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(95344),a=t(3729),n=t(47370);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=a.forwardRef(({className:e,value:s,...t},a)=>r.jsx(n.fC,{ref:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:r.jsx(n.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));l.displayName=n.fC.displayName},2690:(e,s,t)=>{"use strict";t.d(s,{g:()=>n});var r=t(95344),a=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=a.forwardRef(({className:e,...s},t)=>r.jsx("textarea",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},68962:(e,s,t)=>{"use strict";t.d(s,{Q:()=>c});var r=t(83877),a=t(22254),n=t(41026);let l=["en","es","fr","de","ja","zh","af","zu"],i={en:"English",es:"Espa\xf1ol",fr:"Fran\xe7ais",de:"Deutsch",ja:"日本語",zh:"中文",af:"Afrikaans",zu:"isiZulu"},o={en:"\uD83C\uDDFA\uD83C\uDDF8",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",ja:"\uD83C\uDDEF\uD83C\uDDF5",zh:"\uD83C\uDDE8\uD83C\uDDF3",af:"\uD83C\uDDFF\uD83C\uDDE6",zu:"\uD83C\uDDFF\uD83C\uDDE6"};function c(){let e=(0,r.useTranslations)(),s=(0,r.useLocale)(),t=(0,a.useRouter)(),n=(0,a.usePathname)(),c=(s,t)=>{try{return e(s,t)}catch(e){return s}};return{t:c,locale:s,changeLocale:e=>{let r=n.replace(`/${s}`,"")||"/";t.push(`/${e}${r}`)},getAvailableLocales:()=>l.map(e=>({code:e,name:i[e],flag:o[e],isActive:e===s})),formatDate:(e,t)=>new Intl.DateTimeFormat(s,t).format(e),formatNumber:(e,t)=>new Intl.NumberFormat(s,t).format(e),formatCurrency:(e,t="USD")=>new Intl.NumberFormat(s,{style:"currency",currency:t}).format(e),formatRelativeTime:e=>{let t=new Intl.RelativeTimeFormat(s,{numeric:"auto"}),r=new Date,a=Math.floor((e.getTime()-r.getTime())/1e3);if(60>Math.abs(a))return t.format(a,"second");let n=Math.floor(a/60);if(60>Math.abs(n))return t.format(n,"minute");let l=Math.floor(n/60);if(24>Math.abs(l))return t.format(l,"hour");let i=Math.floor(l/24);if(30>Math.abs(i))return t.format(i,"day");let o=Math.floor(i/30);return 12>Math.abs(o)?t.format(o,"month"):t.format(Math.floor(o/12),"year")},isRTL:()=>["ar","he","fa"].includes(s),common:(e,s)=>c(`common.${e}`,s),navigation:(e,s)=>c(`navigation.${e}`,s),auth:(e,s)=>c(`auth.${e}`,s),validation:(e,s)=>c(`validation.${e}`,s),lifeAreas:(e,s)=>c(`lifeAreas.${e}`,s),assessment:(e,s)=>c(`assessment.${e}`,s)}}(0,n.cF)(async({locale:e})=>(l.includes(e)||(0,a.notFound)(),{locale:e,messages:(await t(98491)(`./${e}.json`)).default}))},61700:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m,generateMetadata:()=>d});var r=t(25036),a=t(13285),n=t(86843);let l=(0,n.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/onboarding/onboarding-flow.tsx`),{__esModule:i,$$typeof:o}=l;l.default;let c=(0,n.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/onboarding/onboarding-flow.tsx#OnboardingFlow`);async function d({params:{locale:e}}){return await (0,a.Z)({locale:e,namespace:"common"}),{title:"Get Started - 8,760 Hours",description:"Welcome to systematic life planning. Learn our methodology and start your journey."}}function m(){return r.jsx("div",{className:"min-h-screen bg-gray-50",children:r.jsx("div",{className:"py-12",children:r.jsx(c,{})})})}},47370:(e,s,t)=>{"use strict";t.d(s,{fC:()=>v,z$:()=>b});var r=t(3729),a=t(98462),n=t(62409),l=t(95344),i="Progress",[o,c]=(0,a.b)(i),[d,m]=o(i),u=r.forwardRef((e,s)=>{let{__scopeProgress:t,value:r=null,max:a,getValueLabel:i=f,...o}=e;(a||0===a)&&j(a);let c=j(a)?a:100;null!==r&&y(r,c);let m=y(r,c)?r:null,u=g(m)?i(m,c):void 0;return(0,l.jsx)(d,{scope:t,value:m,max:c,children:(0,l.jsx)(n.WV.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":u,role:"progressbar","data-state":p(m,c),"data-value":m??void 0,"data-max":c,...o,ref:s})})});u.displayName=i;var x="ProgressIndicator",h=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,a=m(x,t);return(0,l.jsx)(n.WV.div,{"data-state":p(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:s})});function f(e,s){return`${Math.round(e/s*100)}%`}function p(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function g(e){return"number"==typeof e}function j(e){return g(e)&&!isNaN(e)&&e>0}function y(e,s){return g(e)&&!isNaN(e)&&e<=s&&e>=0}h.displayName=x;var v=u,b=h},92062:(e,s,t)=>{"use strict";t.d(s,{D:()=>a});var r=t(3729);function a(e){let s=r.useRef({value:e,previous:e});return r.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}},63085:(e,s,t)=>{"use strict";t.d(s,{t:()=>n});var r=t(3729),a=t(16069);function n(e){let[s,t]=r.useState(void 0);return(0,a.b)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let s=new ResizeObserver(s=>{let r,a;if(!Array.isArray(s)||!s.length)return;let n=s[0];if("borderBoxSize"in n){let e=n.borderBoxSize,s=Array.isArray(e)?e[0]:e;r=s.inlineSize,a=s.blockSize}else r=e.offsetWidth,a=e.offsetHeight;t({width:r,height:a})});return s.observe(e,{box:"border-box"}),()=>s.unobserve(e)}t(void 0)},[e]),s}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,8356,3828,1498,1110,9669],()=>t(84679));module.exports=r})();