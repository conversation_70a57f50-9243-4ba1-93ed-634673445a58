(()=>{var e={};e.id=1019,e.ids=[1019],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7371:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(50482),a=t(69108),n=t(62563),i=t.n(n),l=t(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["[locale]",{children:["planning",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,33736)),"/mnt/persist/workspace/frontend/src/app/[locale]/planning/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/[locale]/planning/page.tsx"],u="/[locale]/planning/page",x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/planning/page",pathname:"/[locale]/planning",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76:(e,s,t)=>{Promise.resolve().then(t.bind(t,86062)),Promise.resolve().then(t.bind(t,79977))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35299:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},50340:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},63211:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},55794:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},62312:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2273:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},35341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},48120:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},98200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},21096:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},57320:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},64989:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},13746:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17910:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},18822:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20783:(e,s,t)=>{e.exports=t(61476)},86062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{DashboardLayout:()=>N});var r=t(95344),a=t(3729),n=t(20783),i=t.n(n),l=t(22254);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var o=t(5094),c=t(2273),d=t(50340),u=t(17910),x=t(46064),m=t(21096),h=t(63211),f=t(18822),p=t(13746),g=t(14513),y=t(48120),v=t(98200);let j=[{name:"Dashboard",href:"/dashboard",icon:c.Z},{name:"Assessment",href:"/assessment",icon:d.Z},{name:"Planning",href:"/planning",icon:u.Z},{name:"Progress",href:"/progress",icon:x.Z},{name:"Analytics",href:"/analytics",icon:m.Z},{name:"Reviews",href:"/reviews",icon:h.Z}],b=[{name:"Profile",href:"/profile",icon:f.Z},{name:"Settings",href:"/settings",icon:p.Z}];function N({children:e}){let[s,t]=(0,a.useState)(!1),n=(0,l.usePathname)();return(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("fixed inset-0 z-50 lg:hidden",s?"block":"hidden"),children:[r.jsx("div",{className:"fixed inset-0 bg-black/50",onClick:()=>t(!1)}),(0,r.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-card",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[r.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"}),r.jsx(o.z,{variant:"ghost",size:"sm",onClick:()=>t(!1),children:r.jsx(g.Z,{className:"h-5 w-5"})})]}),r.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:j.map(e=>{let s=n.startsWith(e.href);return(0,r.jsxs)(i(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),onClick:()=>t(!1),children:[r.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",s?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})]}),r.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,r.jsxs)("div",{className:"flex flex-col flex-grow bg-card border-r border-border",children:[r.jsx("div",{className:"flex h-16 items-center px-4",children:r.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"})}),r.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:j.map(e=>{let s=n.startsWith(e.href);return(0,r.jsxs)(i(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),children:[r.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",s?"text-primary-foreground":"text-muted-foreground group-hover:text-foreground")}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-border p-2",children:[b.map(e=>(0,r.jsxs)(i(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[r.jsx(e.icon,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),e.name]},e.name)),(0,r.jsxs)("button",{className:"group flex w-full items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[r.jsx(y.Z,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),"Sign out"]})]})]})}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[(0,r.jsxs)("div",{className:"sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-border bg-background px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8",children:[r.jsx(o.z,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>t(!0),children:r.jsx(v.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:[r.jsx("div",{className:"flex flex-1"}),r.jsx("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:r.jsx("div",{className:"relative","data-testid":"user-menu-trigger",children:(0,r.jsxs)(o.z,{variant:"ghost",size:"sm",className:"flex items-center space-x-2",children:[r.jsx("div",{className:"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-sm font-medium text-white",children:"JD"})}),r.jsx("span",{className:"hidden lg:block text-sm font-medium text-foreground",children:"John Doe"})]})})})]})]}),r.jsx("main",{className:"py-8 px-4 sm:px-6 lg:px-8",children:e})]})]})}},79977:(e,s,t)=>{"use strict";t.r(s),t.d(s,{PlanningFlow:()=>k});var r=t(95344),a=t(3729),n=t(68962),i=t(23673),l=t(5094),o=t(47210),c=t(19591),d=t(46540),u=t(2690),x=t(33668),m=t(80833);!function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}();var h=t(35341),f=t(17910),p=t(55794),g=t(46064),y=t(7060),v=t(25545),j=t(57320),b=t(63024),N=t(64989),w=t(35299);function k(){let{common:e}=(0,n.Q)(),[s,t]=(0,a.useState)("overview"),[k,D]=(0,a.useState)({yearlyTheme:"",lifePhilosophy:"",idealFutureVision:"",majorFocusAreas:[],goals:[]}),Z=[{id:"overview",title:"Planning Overview",description:"Get started with your annual planning"},{id:"vision",title:"Vision & Philosophy",description:"Define your life vision and core philosophy"},{id:"goals",title:"Goal Setting",description:"Create SMART goals for your priority areas"},{id:"review",title:"Review & Finalize",description:"Review and finalize your annual plan"},{id:"complete",title:"Plan Complete",description:"Your planning session is complete"}],C=Z.findIndex(e=>e.id===s),O=(C+1)/Z.length*100,M=()=>{let e=C+1;e<Z.length&&t(Z[e].id)},E=()=>{let e=C-1;e>=0&&t(Z[e].id)};return"overview"===s?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h1",{className:"text-4xl font-bold text-gray-900",children:"Annual Planning Session"}),r.jsx("p",{className:"mt-4 text-xl text-gray-600 max-w-2xl mx-auto",children:"Transform your assessment insights into actionable goals and create your roadmap for the year ahead"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:r.jsx(h.Z,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Vision Setting"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"Step 1"})]})]})})}),r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:r.jsx(f.Z,{className:"h-6 w-6 text-green-600"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Goal Creation"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"Step 2"})]})]})})}),r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:r.jsx(p.Z,{className:"h-6 w-6 text-purple-600"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Timeline"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"12 Months"})]})]})})}),r.jsx(i.Zb,{children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:r.jsx(g.Z,{className:"h-6 w-6 text-orange-600"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Progress"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"0%"})]})]})})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{children:"Your Planning Journey"}),r.jsx(i.SZ,{children:"Follow our proven methodology to create a comprehensive life plan"})]}),(0,r.jsxs)(i.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h3",{className:"font-semibold text-gray-900",children:"What You'll Create"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-4 w-4 text-green-500"}),r.jsx("span",{children:"Your yearly theme and life philosophy"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-4 w-4 text-green-500"}),r.jsx("span",{children:"3-5 major goals for the year"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-4 w-4 text-green-500"}),r.jsx("span",{children:"Actionable milestones and deadlines"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-4 w-4 text-green-500"}),r.jsx("span",{children:"Progress tracking system"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h3",{className:"font-semibold text-gray-900",children:"Time Investment"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(v.Z,{className:"h-4 w-4 text-blue-500"}),r.jsx("span",{children:"Vision Setting: 15-20 minutes"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(v.Z,{className:"h-4 w-4 text-blue-500"}),r.jsx("span",{children:"Goal Creation: 30-45 minutes"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(v.Z,{className:"h-4 w-4 text-blue-500"}),r.jsx("span",{children:"Review & Finalize: 10-15 minutes"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[r.jsx(v.Z,{className:"h-4 w-4 text-blue-500"}),r.jsx("span",{children:"Total: 60-90 minutes"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[r.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCA1 Pro Tip"}),r.jsx("p",{className:"text-sm text-blue-800",children:"Take your time with each step. Quality planning now saves hours of confusion later. You can always come back and refine your goals as the year progresses."})]}),r.jsx("div",{className:"flex justify-center",children:(0,r.jsxs)(l.z,{size:"lg",className:"px-8",onClick:M,children:[r.jsx(j.Z,{className:"mr-2 h-5 w-5"}),"Start Planning Session"]})})]})]})]}):"vision"===s?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Vision & Philosophy"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Step ",C+1," of ",Z.length]})]}),(0,r.jsxs)(c.C,{variant:"outline",className:"text-sm",children:[Math.round(O),"% Complete"]})]}),r.jsx(o.E,{value:O,className:"h-2"})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(h.Z,{className:"h-5 w-5 text-yellow-500"}),r.jsx("span",{children:"Define Your Vision"})]}),r.jsx(i.SZ,{children:"Create the foundation for your annual planning with clear vision and values"})]}),(0,r.jsxs)(i.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(x._,{htmlFor:"yearlyTheme",className:"text-base font-medium",children:"What's your theme for this year? (Optional)"}),r.jsx(d.I,{id:"yearlyTheme",placeholder:"e.g., 'Year of Growth', 'Building Foundations', 'Adventure & Discovery'",value:k.yearlyTheme,onChange:e=>D(s=>({...s,yearlyTheme:e.target.value}))}),r.jsx("p",{className:"text-sm text-gray-500",children:"A simple phrase that captures the essence of what you want this year to be about"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(x._,{htmlFor:"lifePhilosophy",className:"text-base font-medium",children:"What's your core life philosophy?"}),r.jsx(u.g,{id:"lifePhilosophy",placeholder:"Describe your fundamental beliefs about how to live a meaningful life...",value:k.lifePhilosophy,onChange:e=>D(s=>({...s,lifePhilosophy:e.target.value})),rows:4,className:"resize-none"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(x._,{htmlFor:"idealFutureVision",className:"text-base font-medium",children:"Describe your ideal life 3-5 years from now"}),r.jsx(u.g,{id:"idealFutureVision",placeholder:"Paint a vivid picture of your ideal future across all life areas...",value:k.idealFutureVision,onChange:e=>D(s=>({...s,idealFutureVision:e.target.value})),rows:5,className:"resize-none"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(x._,{className:"text-base font-medium",children:"Which life areas will you focus on this year? (Select 3-5)"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(m.X,{id:e.id,checked:k.majorFocusAreas.includes(e.id),onCheckedChange:s=>{s?D(s=>({...s,majorFocusAreas:[...s.majorFocusAreas,e.id]})):D(s=>({...s,majorFocusAreas:s.majorFocusAreas.filter(s=>s!==e.id)}))}}),r.jsx(x._,{htmlFor:e.id,className:"text-sm font-medium cursor-pointer",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(e.icon,{className:"h-4 w-4 text-gray-500"}),r.jsx("span",{children:e.name})]})})]},e.id))}),r.jsx("p",{className:"text-sm text-gray-500",children:"Choose the areas where you want to see the most growth this year"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(l.z,{variant:"outline",onClick:E,children:[r.jsx(b.Z,{className:"mr-2 h-4 w-4"}),"Previous"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(l.z,{variant:"ghost",onClick:()=>{t("overview"),D({yearlyTheme:"",lifePhilosophy:"",idealFutureVision:"",majorFocusAreas:[],goals:[]})},children:[r.jsx(N.Z,{className:"mr-2 h-4 w-4"}),"Restart"]}),(0,r.jsxs)(l.z,{onClick:M,disabled:0===k.majorFocusAreas.length,children:["Continue to Goals",r.jsx(w.Z,{className:"ml-2 h-4 w-4"})]})]})]})]}):r.jsx("div",{className:"max-w-4xl mx-auto space-y-6",children:(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{children:"Planning Session"}),(0,r.jsxs)(i.SZ,{children:["Step ",C+1," of ",Z.length," - ",Z[C]?.title]})]}),(0,r.jsxs)(i.aY,{children:[r.jsx("p",{children:"This step is under development..."}),(0,r.jsxs)("div",{className:"flex space-x-4 mt-4",children:[r.jsx(l.z,{variant:"outline",onClick:E,disabled:0===C,children:"Previous"}),r.jsx(l.z,{onClick:M,disabled:C===Z.length-1,children:"Next"})]})]})]})})}},80833:(e,s,t)=>{"use strict";t.d(s,{X:()=>M});var r=t(95344),a=t(3729),n=t(31405),i=t(98462),l=t(85222),o=t(33183),c=t(92062),d=t(63085),u=t(43234),x=t(62409),m="Checkbox",[h,f]=(0,i.b)(m),[p,g]=h(m);function y(e){let{__scopeCheckbox:s,checked:t,children:n,defaultChecked:i,disabled:l,form:c,name:d,onCheckedChange:u,required:x,value:h="on",internal_do_not_use_render:f}=e,[g,y]=(0,o.T)({prop:t,defaultProp:i??!1,onChange:u,caller:m}),[v,j]=a.useState(null),[b,N]=a.useState(null),w=a.useRef(!1),k=!v||!!c||!!v.closest("form"),D={checked:g,disabled:l,setChecked:y,control:v,setControl:j,name:d,form:c,value:h,hasConsumerStoppedPropagationRef:w,required:x,defaultChecked:!Z(i)&&i,isFormControl:k,bubbleInput:b,setBubbleInput:N};return(0,r.jsx)(p,{scope:s,...D,children:"function"==typeof f?f(D):n})}var v="CheckboxTrigger",j=a.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...i},o)=>{let{control:c,value:d,disabled:u,checked:m,required:h,setControl:f,setChecked:p,hasConsumerStoppedPropagationRef:y,isFormControl:j,bubbleInput:b}=g(v,e),N=(0,n.e)(o,f),w=a.useRef(m);return a.useEffect(()=>{let e=c?.form;if(e){let s=()=>p(w.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[c,p]),(0,r.jsx)(x.WV.button,{type:"button",role:"checkbox","aria-checked":Z(m)?"mixed":m,"aria-required":h,"data-state":C(m),"data-disabled":u?"":void 0,disabled:u,value:d,...i,ref:N,onKeyDown:(0,l.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(t,e=>{p(e=>!!Z(e)||!e),b&&j&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});j.displayName=v;var b=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:a,checked:n,defaultChecked:i,required:l,disabled:o,value:c,onCheckedChange:d,form:u,...x}=e;return(0,r.jsx)(y,{__scopeCheckbox:t,checked:n,defaultChecked:i,disabled:o,required:l,onCheckedChange:d,name:a,form:u,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j,{...x,ref:s,__scopeCheckbox:t}),e&&(0,r.jsx)(D,{__scopeCheckbox:t})]})})});b.displayName=m;var N="CheckboxIndicator",w=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:a,...n}=e,i=g(N,t);return(0,r.jsx)(u.z,{present:a||Z(i.checked)||!0===i.checked,children:(0,r.jsx)(x.WV.span,{"data-state":C(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:s,style:{pointerEvents:"none",...e.style}})})});w.displayName=N;var k="CheckboxBubbleInput",D=a.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:i,hasConsumerStoppedPropagationRef:l,checked:o,defaultChecked:u,required:m,disabled:h,name:f,value:p,form:y,bubbleInput:v,setBubbleInput:j}=g(k,e),b=(0,n.e)(t,j),N=(0,c.D)(o),w=(0,d.t)(i);a.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!l.current;if(N!==o&&e){let t=new Event("click",{bubbles:s});v.indeterminate=Z(o),e.call(v,!Z(o)&&o),v.dispatchEvent(t)}},[v,N,o,l]);let D=a.useRef(!Z(o)&&o);return(0,r.jsx)(x.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??D.current,required:m,disabled:h,name:f,value:p,form:y,...s,tabIndex:-1,ref:b,style:{...s.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function Z(e){return"indeterminate"===e}function C(e){return Z(e)?"indeterminate":e?"checked":"unchecked"}D.displayName=k;var O=t(62312);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let M=a.forwardRef(({className:e,...s},t)=>r.jsx(b,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:r.jsx(w,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center justify-center text-current"),children:r.jsx(O.Z,{className:"h-4 w-4"})})}));M.displayName=b.displayName},46540:(e,s,t)=>{"use strict";t.d(s,{I:()=>o});var r=t(95344),a=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var n=t(66138),i=t(1222),l=t(53148);let o=a.forwardRef(({className:e,type:s,label:t,error:o,helperText:c,leftIcon:d,rightIcon:u,variant:x="default",id:m,required:h,...f},p)=>{let[g,y]=a.useState(!1),[v]=a.useState(()=>m||`input-${Math.random().toString(36).substr(2,9)}`),j="password"===s,b=j&&g?"text":s,N=!!o||"error"===x;return(0,r.jsxs)("div",{className:"space-y-2",children:[t&&(0,r.jsxs)("label",{htmlFor:v,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[t,h&&r.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[d&&r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:d})}),r.jsx("input",{type:b,id:v,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",N&&"border-destructive focus-visible:ring-destructive","success"===x&&"border-green-500 focus-visible:ring-green-500",d&&"pl-10",(u||j||N)&&"pr-10",e),ref:p,"aria-invalid":N,"aria-describedby":o?`${v}-error`:c?`${v}-helper`:void 0,...f}),N&&r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(n.Z,{className:"h-4 w-4 text-destructive"})}),j&&r.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{y(!g)},"aria-label":g?"Hide password":"Show password",children:g?r.jsx(i.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):r.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),u&&!N&&!j&&r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:u})})]}),o&&r.jsx("p",{id:`${v}-error`,className:"text-sm text-destructive",role:"alert",children:o}),c&&!o&&r.jsx("p",{id:`${v}-helper`,className:"text-sm text-muted-foreground",children:c})]})});o.displayName="Input"},33668:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var r=t(95344),a=t(3729),n=t(62409),i=a.forwardRef((e,s)=>(0,r.jsx)(n.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var l=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...s},t)=>r.jsx(i,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o(),e),...s}));c.displayName=i.displayName},47210:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var r=t(95344),a=t(3729),n=t(47370);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let i=a.forwardRef(({className:e,value:s,...t},a)=>r.jsx(n.fC,{ref:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:r.jsx(n.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));i.displayName=n.fC.displayName},2690:(e,s,t)=>{"use strict";t.d(s,{g:()=>n});var r=t(95344),a=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=a.forwardRef(({className:e,...s},t)=>r.jsx("textarea",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},68962:(e,s,t)=>{"use strict";t.d(s,{Q:()=>c});var r=t(83877),a=t(22254),n=t(41026);let i=["en","es","fr","de","ja","zh","af","zu"],l={en:"English",es:"Espa\xf1ol",fr:"Fran\xe7ais",de:"Deutsch",ja:"日本語",zh:"中文",af:"Afrikaans",zu:"isiZulu"},o={en:"\uD83C\uDDFA\uD83C\uDDF8",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",ja:"\uD83C\uDDEF\uD83C\uDDF5",zh:"\uD83C\uDDE8\uD83C\uDDF3",af:"\uD83C\uDDFF\uD83C\uDDE6",zu:"\uD83C\uDDFF\uD83C\uDDE6"};function c(){let e=(0,r.useTranslations)(),s=(0,r.useLocale)(),t=(0,a.useRouter)(),n=(0,a.usePathname)(),c=(s,t)=>{try{return e(s,t)}catch(e){return s}};return{t:c,locale:s,changeLocale:e=>{let r=n.replace(`/${s}`,"")||"/";t.push(`/${e}${r}`)},getAvailableLocales:()=>i.map(e=>({code:e,name:l[e],flag:o[e],isActive:e===s})),formatDate:(e,t)=>new Intl.DateTimeFormat(s,t).format(e),formatNumber:(e,t)=>new Intl.NumberFormat(s,t).format(e),formatCurrency:(e,t="USD")=>new Intl.NumberFormat(s,{style:"currency",currency:t}).format(e),formatRelativeTime:e=>{let t=new Intl.RelativeTimeFormat(s,{numeric:"auto"}),r=new Date,a=Math.floor((e.getTime()-r.getTime())/1e3);if(60>Math.abs(a))return t.format(a,"second");let n=Math.floor(a/60);if(60>Math.abs(n))return t.format(n,"minute");let i=Math.floor(n/60);if(24>Math.abs(i))return t.format(i,"hour");let l=Math.floor(i/24);if(30>Math.abs(l))return t.format(l,"day");let o=Math.floor(l/30);return 12>Math.abs(o)?t.format(o,"month"):t.format(Math.floor(o/12),"year")},isRTL:()=>["ar","he","fa"].includes(s),common:(e,s)=>c(`common.${e}`,s),navigation:(e,s)=>c(`navigation.${e}`,s),auth:(e,s)=>c(`auth.${e}`,s),validation:(e,s)=>c(`validation.${e}`,s),lifeAreas:(e,s)=>c(`lifeAreas.${e}`,s),assessment:(e,s)=>c(`assessment.${e}`,s)}}(0,n.cF)(async({locale:e})=>(i.includes(e)||(0,a.notFound)(),{locale:e,messages:(await t(98491)(`./${e}.json`)).default}))},33736:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x,generateMetadata:()=>u});var r=t(25036),a=t(13285),n=t(97385),i=t(86843);let l=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/planning/planning-flow.tsx`),{__esModule:o,$$typeof:c}=l;l.default;let d=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/planning/planning-flow.tsx#PlanningFlow`);async function u({params:{locale:e}}){let s=await (0,a.Z)({locale:e,namespace:"planning"});return{title:s("title"),description:s("description")}}function x(){return r.jsx(n.c,{children:r.jsx("div",{className:"max-w-6xl mx-auto",children:r.jsx(d,{})})})}},97385:(e,s,t)=>{"use strict";t.d(s,{c:()=>l});var r=t(86843);let a=(0,r.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let l=(0,r.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx#DashboardLayout`)},47370:(e,s,t)=>{"use strict";t.d(s,{fC:()=>j,z$:()=>b});var r=t(3729),a=t(98462),n=t(62409),i=t(95344),l="Progress",[o,c]=(0,a.b)(l),[d,u]=o(l),x=r.forwardRef((e,s)=>{let{__scopeProgress:t,value:r=null,max:a,getValueLabel:l=f,...o}=e;(a||0===a)&&y(a);let c=y(a)?a:100;null!==r&&v(r,c);let u=v(r,c)?r:null,x=g(u)?l(u,c):void 0;return(0,i.jsx)(d,{scope:t,value:u,max:c,children:(0,i.jsx)(n.WV.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":g(u)?u:void 0,"aria-valuetext":x,role:"progressbar","data-state":p(u,c),"data-value":u??void 0,"data-max":c,...o,ref:s})})});x.displayName=l;var m="ProgressIndicator",h=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,a=u(m,t);return(0,i.jsx)(n.WV.div,{"data-state":p(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:s})});function f(e,s){return`${Math.round(e/s*100)}%`}function p(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function g(e){return"number"==typeof e}function y(e){return g(e)&&!isNaN(e)&&e>0}function v(e,s){return g(e)&&!isNaN(e)&&e<=s&&e>=0}h.displayName=m;var j=x,b=h},92062:(e,s,t)=>{"use strict";t.d(s,{D:()=>a});var r=t(3729);function a(e){let s=r.useRef({value:e,previous:e});return r.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}},63085:(e,s,t)=>{"use strict";t.d(s,{t:()=>n});var r=t(3729),a=t(16069);function n(e){let[s,t]=r.useState(void 0);return(0,a.b)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let s=new ResizeObserver(s=>{let r,a;if(!Array.isArray(s)||!s.length)return;let n=s[0];if("borderBoxSize"in n){let e=n.borderBoxSize,s=Array.isArray(e)?e[0]:e;r=s.inlineSize,a=s.blockSize}else r=e.offsetWidth,a=e.offsetHeight;t({width:r,height:a})});return s.observe(e,{box:"border-box"}),()=>s.unobserve(e)}t(void 0)},[e]),s}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,8356,3828,1476,1498,1110,9669],()=>t(7371));module.exports=r})();