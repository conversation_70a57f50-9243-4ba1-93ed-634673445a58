(()=>{var e={};e.id=1067,e.ids=[1067],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},72703:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var s=t(50482),n=t(69108),a=t(62563),i=t.n(a),o=t(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["[locale]",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,37134)),"/mnt/persist/workspace/frontend/src/app/[locale]/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/[locale]/login/page.tsx"],u="/[locale]/login/page",m={require:t,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/[locale]/login/page",pathname:"/[locale]/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},45763:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,61476,23)),Promise.resolve().then(t.bind(t,9881))},62312:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},20783:(e,r,t)=>{e.exports=t(61476)},9881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{LoginForm:()=>g});var s=t(95344);t(3729);var n=t(60708),a=t(85453),i=t(3389),o=t(20783),l=t.n(o),d=t(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,d.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),u=(0,d.Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var m=t(5094),f=t(46540),p=t(80833),x=t(33668);let h=i.Ry({email:i.Z_().email("Please enter a valid email address").min(1,"Email is required"),password:i.Z_().min(8,"Password must be at least 8 characters").min(1,"Password is required"),rememberMe:i.O7().default(!1)});function g({onSubmit:e,loading:r=!1,error:t,className:i}){let{register:o,handleSubmit:d,formState:{errors:g,isValid:b},clearErrors:v,setValue:y,watch:N}=(0,n.cI)({resolver:(0,a.F)(h),mode:"onChange",defaultValues:{rememberMe:!1}}),j=N("rememberMe"),w=async r=>{try{await e(r)}catch(e){}},O=()=>{t&&v()};return(0,s.jsxs)("div",{className:i,children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Sign in to your account"}),s.jsx("p",{className:"mt-2 text-muted-foreground",children:"Enter your credentials to access your dashboard"})]}),(0,s.jsxs)("form",{onSubmit:d(w),className:"space-y-6",children:[t&&s.jsx("div",{className:"bg-destructive/15 border border-destructive/20 text-destructive px-4 py-3 rounded-lg",role:"alert","data-testid":"error-message",children:s.jsx("p",{className:"text-sm",children:t})}),s.jsx(f.I,{...o("email"),type:"email",label:"Email address",placeholder:"Enter your email",leftIcon:s.jsx(c,{className:"h-4 w-4"}),error:g.email?.message,onChange:e=>{o("email").onChange(e),O()},"data-testid":"email-input",autoComplete:"email",required:!0}),s.jsx(f.I,{...o("password"),type:"password",label:"Password",placeholder:"Enter your password",leftIcon:s.jsx(u,{className:"h-4 w-4"}),error:g.password?.message,onChange:e=>{o("password").onChange(e),O()},"data-testid":"password-input",autoComplete:"current-password",required:!0}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(p.X,{id:"remember-me",checked:j,onCheckedChange:e=>y("rememberMe",!!e),"data-testid":"remember-me-checkbox"}),s.jsx(x._,{htmlFor:"remember-me",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Remember me"})]}),s.jsx(l(),{href:"/forgot-password",className:"text-sm text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded","data-testid":"forgot-password-link",children:"Forgot your password?"})]}),s.jsx(m.z,{type:"submit",size:"lg",className:"w-full",loading:r,disabled:!b||r,"data-testid":"login-button",children:r?"Signing in...":"Sign in"}),s.jsx("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",s.jsx(l(),{href:"/register",className:"text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded",children:"Sign up here"})]})})]})]})}},80833:(e,r,t)=>{"use strict";t.d(r,{X:()=>D});var s=t(95344),n=t(3729),a=t(31405),i=t(98462),o=t(85222),l=t(33183),d=t(92062),c=t(63085),u=t(43234),m=t(62409),f="Checkbox",[p,x]=(0,i.b)(f),[h,g]=p(f);function b(e){let{__scopeCheckbox:r,checked:t,children:a,defaultChecked:i,disabled:o,form:d,name:c,onCheckedChange:u,required:m,value:p="on",internal_do_not_use_render:x}=e,[g,b]=(0,l.T)({prop:t,defaultProp:i??!1,onChange:u,caller:f}),[v,y]=n.useState(null),[N,j]=n.useState(null),w=n.useRef(!1),O=!v||!!d||!!v.closest("form"),k={checked:g,disabled:o,setChecked:b,control:v,setControl:y,name:c,form:d,value:p,hasConsumerStoppedPropagationRef:w,required:m,defaultChecked:!E(i)&&i,isFormControl:O,bubbleInput:N,setBubbleInput:j};return(0,s.jsx)(h,{scope:r,...k,children:"function"==typeof x?x(k):a})}var v="CheckboxTrigger",y=n.forwardRef(({__scopeCheckbox:e,onKeyDown:r,onClick:t,...i},l)=>{let{control:d,value:c,disabled:u,checked:f,required:p,setControl:x,setChecked:h,hasConsumerStoppedPropagationRef:b,isFormControl:y,bubbleInput:N}=g(v,e),j=(0,a.e)(l,x),w=n.useRef(f);return n.useEffect(()=>{let e=d?.form;if(e){let r=()=>h(w.current);return e.addEventListener("reset",r),()=>e.removeEventListener("reset",r)}},[d,h]),(0,s.jsx)(m.WV.button,{type:"button",role:"checkbox","aria-checked":E(f)?"mixed":f,"aria-required":p,"data-state":_(f),"data-disabled":u?"":void 0,disabled:u,value:c,...i,ref:j,onKeyDown:(0,o.M)(r,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.M)(t,e=>{h(e=>!!E(e)||!e),N&&y&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});y.displayName=v;var N=n.forwardRef((e,r)=>{let{__scopeCheckbox:t,name:n,checked:a,defaultChecked:i,required:o,disabled:l,value:d,onCheckedChange:c,form:u,...m}=e;return(0,s.jsx)(b,{__scopeCheckbox:t,checked:a,defaultChecked:i,disabled:l,required:o,onCheckedChange:c,name:n,form:u,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{...m,ref:r,__scopeCheckbox:t}),e&&(0,s.jsx)(k,{__scopeCheckbox:t})]})})});N.displayName=f;var j="CheckboxIndicator",w=n.forwardRef((e,r)=>{let{__scopeCheckbox:t,forceMount:n,...a}=e,i=g(j,t);return(0,s.jsx)(u.z,{present:n||E(i.checked)||!0===i.checked,children:(0,s.jsx)(m.WV.span,{"data-state":_(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:r,style:{pointerEvents:"none",...e.style}})})});w.displayName=j;var O="CheckboxBubbleInput",k=n.forwardRef(({__scopeCheckbox:e,...r},t)=>{let{control:i,hasConsumerStoppedPropagationRef:o,checked:l,defaultChecked:u,required:f,disabled:p,name:x,value:h,form:b,bubbleInput:v,setBubbleInput:y}=g(O,e),N=(0,a.e)(t,y),j=(0,d.D)(l),w=(0,c.t)(i);n.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,r=!o.current;if(j!==l&&e){let t=new Event("click",{bubbles:r});v.indeterminate=E(l),e.call(v,!E(l)&&l),v.dispatchEvent(t)}},[v,j,l,o]);let k=n.useRef(!E(l)&&l);return(0,s.jsx)(m.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??k.current,required:f,disabled:p,name:x,value:h,form:b,...r,tabIndex:-1,ref:N,style:{...r.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function E(e){return"indeterminate"===e}function _(e){return E(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=O;var C=t(62312);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let D=n.forwardRef(({className:e,...r},t)=>s.jsx(N,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...r,children:s.jsx(w,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center justify-center text-current"),children:s.jsx(C.Z,{className:"h-4 w-4"})})}));D.displayName=N.displayName},46540:(e,r,t)=>{"use strict";t.d(r,{I:()=>l});var s=t(95344),n=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var a=t(66138),i=t(1222),o=t(53148);let l=n.forwardRef(({className:e,type:r,label:t,error:l,helperText:d,leftIcon:c,rightIcon:u,variant:m="default",id:f,required:p,...x},h)=>{let[g,b]=n.useState(!1),[v]=n.useState(()=>f||`input-${Math.random().toString(36).substr(2,9)}`),y="password"===r,N=y&&g?"text":r,j=!!l||"error"===m;return(0,s.jsxs)("div",{className:"space-y-2",children:[t&&(0,s.jsxs)("label",{htmlFor:v,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[t,p&&s.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[c&&s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:c})}),s.jsx("input",{type:N,id:v,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",j&&"border-destructive focus-visible:ring-destructive","success"===m&&"border-green-500 focus-visible:ring-green-500",c&&"pl-10",(u||y||j)&&"pr-10",e),ref:h,"aria-invalid":j,"aria-describedby":l?`${v}-error`:d?`${v}-helper`:void 0,...x}),j&&s.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:s.jsx(a.Z,{className:"h-4 w-4 text-destructive"})}),y&&s.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{b(!g)},"aria-label":g?"Hide password":"Show password",children:g?s.jsx(i.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):s.jsx(o.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),u&&!j&&!y&&s.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:s.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:u})})]}),l&&s.jsx("p",{id:`${v}-error`,className:"text-sm text-destructive",role:"alert",children:l}),d&&!l&&s.jsx("p",{id:`${v}-helper`,className:"text-sm text-muted-foreground",children:d})]})});l.displayName="Input"},33668:(e,r,t)=>{"use strict";t.d(r,{_:()=>d});var s=t(95344),n=t(3729),a=t(62409),i=n.forwardRef((e,r)=>(0,s.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef(({className:e,...r},t)=>s.jsx(i,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l(),e),...r}));d.displayName=i.displayName},59508:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var s=t(40002),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,r)=>{let t=(0,s.forwardRef)(({color:t="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:d="",children:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...n,width:i,height:i,stroke:t,strokeWidth:l?24*Number(o)/Number(i):o,className:["lucide",`lucide-${a(e)}`,d].join(" "),...u},[...r.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(c)?c:[c]]));return t.displayName=`${e}`,t}},13285:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var s=t(40002),n=t(66689),a=t(399),i=(0,s.cache)(async function(e){let r,t;"string"==typeof e?r=e:e&&(t=e.locale,r=e.namespace);let s=await (0,a.Z)(t);return(0,n.eX)({...s,namespace:r,messages:s.messages})})},48026:(e,r,t)=>{let{createProxy:s}=t(86843);e.exports=s("/mnt/persist/workspace/frontend/node_modules/next/dist/client/link.js")},40646:(e,r,t)=>{e.exports=t(48026)},37134:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x,generateMetadata:()=>p});var s=t(25036),n=t(13285),a=t(40646),i=t.n(a),o=t(86843);let l=(0,o.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/forms/login-form.tsx`),{__esModule:d,$$typeof:c}=l;l.default;let u=(0,o.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/forms/login-form.tsx#LoginForm`);var m=t(38634),f=t(44551);async function p({params:{locale:e}}){let r=await (0,n.Z)({locale:e,namespace:"auth"});return{title:r("signIn"),description:r("signInDescription")}}function x(){return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"8,760 Hours"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Transform your life with systematic planning"})]}),(0,s.jsxs)(f.Zb,{children:[(0,s.jsxs)(f.Ol,{className:"space-y-1",children:[s.jsx(f.ll,{className:"text-2xl text-center",children:"Sign in to your account"}),s.jsx(f.SZ,{className:"text-center",children:"Enter your email and password to access your life planning dashboard"})]}),(0,s.jsxs)(f.aY,{children:[s.jsx(u,{}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-gray-300"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:"px-2 bg-white text-gray-500",children:"New to 8,760 Hours?"})})]}),s.jsx("div",{className:"mt-6",children:s.jsx(i(),{href:"/register",children:s.jsx(m.z,{variant:"outline",className:"w-full",children:"Create your account"})})})]}),s.jsx("div",{className:"mt-6 text-center",children:s.jsx(i(),{href:"/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]})]}),s.jsx("div",{className:"text-center text-xs text-gray-500",children:(0,s.jsxs)("p",{children:["By signing in, you agree to our"," ",s.jsx(i(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",s.jsx(i(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})})]})})}},38634:(e,r,t)=>{"use strict";t.d(r,{z:()=>m});var s=t(25036),n=t(40002);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var i=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){let e,i;let o=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,l=function(e,r){let t={...r};for(let s in r){let n=e[s],a=r[s];/^on[A-Z]/.test(s)?n&&a?t[s]=(...e)=>{let r=a(...e);return n(...e),r}:n&&(t[s]=n):"style"===s?t[s]={...n,...a}:"className"===s&&(t[s]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(l.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}(r,o):o),n.cloneElement(t,l)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:a,...i}=e,o=n.Children.toArray(a),d=o.find(l);if(d){let e=d.props.children,a=o.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(r,{...i,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var d=t(47146);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,t(59508).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let u=(0,d.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,loading:a=!1,leftIcon:o,rightIcon:l,children:d,disabled:m,...f},p)=>{let x=n?i:"button",h=m||a;return(0,s.jsxs)(x,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(u({variant:r,size:t,className:e})),ref:p,disabled:h,"aria-disabled":h,...f,children:[a&&s.jsx(c,{className:"mr-2 h-4 w-4 animate-spin"}),!a&&o&&s.jsx("span",{className:"mr-2",children:o}),d,!a&&l&&s.jsx("span",{className:"ml-2",children:l})]})});m.displayName="Button"},44551:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>i,SZ:()=>l,Zb:()=>a,aY:()=>d,ll:()=>o});var s=t(25036),n=t(40002);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let a=n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));a.displayName="Card";let i=n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let o=n.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let l=n.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let d=n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...r}));d.displayName="CardContent",n.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},92062:(e,r,t)=>{"use strict";t.d(r,{D:()=>n});var s=t(3729);function n(e){let r=s.useRef({value:e,previous:e});return s.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},63085:(e,r,t)=>{"use strict";t.d(r,{t:()=>a});var s=t(3729),n=t(16069);function a(e){let[r,t]=s.useState(void 0);return(0,n.b)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let r=new ResizeObserver(r=>{let s,n;if(!Array.isArray(r)||!r.length)return;let a=r[0];if("borderBoxSize"in a){let e=a.borderBoxSize,r=Array.isArray(e)?e[0]:e;s=r.inlineSize,n=r.blockSize}else s=e.offsetWidth,n=e.offsetHeight;t({width:s,height:n})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}t(void 0)},[e]),r}},47146:(e,r,t)=>{"use strict";t.d(r,{j:()=>a});let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=function(){for(var e,r,t=0,s="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,s,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r){if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(s=e(r[t]))&&(n&&(n+=" "),n+=s)}else for(s in r)r[s]&&(n&&(n+=" "),n+=s)}return n}(e))&&(s&&(s+=" "),s+=r);return s},a=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:o}=r,l=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],n=null==o?void 0:o[e];if(null===r)return null;let a=s(r)||s(n);return i[e][a]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return n(e,l,null==r?void 0:null===(a=r.compoundVariants)||void 0===a?void 0:a.reduce((e,r)=>{let{class:t,className:s,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...d}[r]):({...o,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,8356,3828,1476,1498,28,9669],()=>t(72703));module.exports=s})();