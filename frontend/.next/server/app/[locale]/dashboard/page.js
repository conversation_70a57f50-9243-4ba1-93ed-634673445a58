(()=>{var e={};e.id=5631,e.ids=[5631],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6205:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var t=r(50482),a=r(69108),n=r(62563),i=r.n(n),l=r(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["[locale]",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3023)),"/mnt/persist/workspace/frontend/src/app/[locale]/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/mnt/persist/workspace/frontend/src/app/[locale]/dashboard/page.tsx"],x="/[locale]/dashboard/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/dashboard/page",pathname:"/[locale]/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76350:(e,s,r)=>{Promise.resolve().then(r.bind(r,60548)),Promise.resolve().then(r.bind(r,65741)),Promise.resolve().then(r.bind(r,77483)),Promise.resolve().then(r.bind(r,90683)),Promise.resolve().then(r.bind(r,11165)),Promise.resolve().then(r.bind(r,86062))},65187:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},50340:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},63211:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},55794:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},25545:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},2273:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},48120:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},98200:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},21096:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},13746:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17910:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},77402:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},46064:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},18822:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20783:(e,s,r)=>{e.exports=r(61476)},22254:(e,s,r)=>{e.exports=r(14767)},60548:(e,s,r)=>{"use strict";r.r(s),r.d(s,{DashboardOverview:()=>m});var t=r(95344),a=r(23673),n=r(47210),i=r(19591),l=r(50340),o=r(46064),c=(r(77402),r(17910)),d=r(65187),x=r(55794);function m(){let e={overallProgress:67,goalsCompleted:3,totalGoals:8,averageRating:5.2,improvementAreas:2,strongAreas:4,daysRemaining:183,lastAssessment:"2 weeks ago"};return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[t.jsx(a.Zb,{children:(0,t.jsxs)(a.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Overall Progress"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.overallProgress,"%"]})]}),t.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:t.jsx(l.Z,{className:"h-6 w-6 text-blue-600"})})]}),(0,t.jsxs)("div",{className:"mt-4",children:[t.jsx(n.E,{value:e.overallProgress,className:"h-2"}),(0,t.jsxs)("div",{className:"flex items-center mt-2 text-sm",children:[t.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-1"}),(0,t.jsxs)("span",{className:"text-green-600",children:["+",15,"% from last month"]})]})]})]})}),t.jsx(a.Zb,{children:(0,t.jsxs)(a.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Goals Completed"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.goalsCompleted,"/",e.totalGoals]})]}),t.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:t.jsx(c.Z,{className:"h-6 w-6 text-green-600"})})]}),(0,t.jsxs)("div",{className:"mt-4",children:[t.jsx(n.E,{value:e.goalsCompleted/e.totalGoals*100,className:"h-2"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:[e.totalGoals-e.goalsCompleted," goals remaining"]})]})]})}),t.jsx(a.Zb,{children:(0,t.jsxs)(a.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Average Rating"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.averageRating,"/7"]})]}),t.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:t.jsx(d.Z,{className:"h-6 w-6 text-purple-600"})})]}),(0,t.jsxs)("div",{className:"mt-4 flex items-center space-x-2",children:[(0,t.jsxs)(i.C,{variant:"secondary",className:"text-xs",children:[e.strongAreas," Strong Areas"]}),(0,t.jsxs)(i.C,{variant:"outline",className:"text-xs",children:[e.improvementAreas," Need Focus"]})]})]})}),t.jsx(a.Zb,{children:(0,t.jsxs)(a.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Days Remaining"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.daysRemaining})]}),t.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:t.jsx(x.Z,{className:"h-6 w-6 text-orange-600"})})]}),(0,t.jsxs)("div",{className:"mt-4",children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Until year-end review"}),(0,t.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Last assessment: ",e.lastAssessment]})]})]})})]})}},65741:(e,s,r)=>{"use strict";r.r(s),r.d(s,{ProgressSummary:()=>c});var t=r(95344),a=r(23673),n=r(47210),i=r(19591),l=r(46064),o=r(77402);function c(){return(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[t.jsx(a.ll,{children:"Life Areas Progress"}),t.jsx(a.SZ,{children:"Track your improvement across key life areas"})]}),t.jsx(a.aY,{children:t.jsx("div",{className:"space-y-4",children:[{name:"Health & Fitness",current:6,target:7,progress:85,trend:"up"},{name:"Career & Work",current:5,target:6,progress:75,trend:"up"},{name:"Relationships",current:7,target:7,progress:100,trend:"stable"},{name:"Finances",current:4,target:6,progress:50,trend:"up"},{name:"Values & Purpose",current:6,target:7,progress:80,trend:"up"},{name:"Education & Learning",current:5,target:6,progress:70,trend:"up"}].map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"font-medium text-gray-900",children:e.name}),"up"===e.trend&&t.jsx(l.Z,{className:"h-4 w-4 text-green-500"}),"down"===e.trend&&t.jsx(o.Z,{className:"h-4 w-4 text-red-500"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.current,"/",e.target]}),(0,t.jsxs)(i.C,{variant:e.progress>=80?"default":e.progress>=60?"secondary":"outline",className:"text-xs",children:[e.progress,"%"]})]})]}),t.jsx(n.E,{value:e.progress,className:"h-2"})]},s))})})]})}},77483:(e,s,r)=>{"use strict";r.r(s),r.d(s,{QuickActions:()=>d});var t=r(95344),a=r(20783),n=r.n(a),i=r(5094),l=r(50340),o=r(17910),c=r(46064);function d(){return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(n(),{href:"/assessment",children:(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[t.jsx(l.Z,{className:"h-4 w-4 mr-2"}),"New Assessment"]})}),t.jsx(n(),{href:"/planning",children:(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[t.jsx(o.Z,{className:"h-4 w-4 mr-2"}),"Set Goals"]})}),t.jsx(n(),{href:"/progress",children:(0,t.jsxs)(i.z,{size:"sm",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Update Progress"]})})]})}},90683:(e,s,r)=>{"use strict";r.r(s),r.d(s,{RecentActivity:()=>d});var t=r(95344),a=r(23673),n=r(19591),i=r(7060),l=r(17910),o=r(46064),c=r(55794);function d(){let e=[{id:1,type:"goal_completed",title:"Completed fitness goal",description:"Reached target of exercising 4 times per week",area:"Health & Fitness",timestamp:"2 hours ago",icon:i.Z,iconColor:"text-green-600",bgColor:"bg-green-100"},{id:2,type:"milestone_reached",title:"Milestone achieved",description:"Saved $2,500 towards emergency fund",area:"Finances",timestamp:"1 day ago",icon:l.Z,iconColor:"text-blue-600",bgColor:"bg-blue-100"},{id:3,type:"progress_update",title:"Progress updated",description:"Improved career satisfaction rating from 4 to 5",area:"Career & Work",timestamp:"3 days ago",icon:o.Z,iconColor:"text-purple-600",bgColor:"bg-purple-100"},{id:4,type:"planning_session",title:"Planning session completed",description:"Set 3 new goals for Q2 2024",area:"Planning",timestamp:"1 week ago",icon:c.Z,iconColor:"text-orange-600",bgColor:"bg-orange-100"}];return(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[t.jsx(a.ll,{children:"Recent Activity"}),t.jsx(a.SZ,{children:"Your latest achievements and updates"})]}),t.jsx(a.aY,{children:t.jsx("div",{className:"space-y-4",children:e.map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[t.jsx("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:t.jsx(e.icon,{className:`h-4 w-4 ${e.iconColor}`})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[t.jsx("p",{className:"font-medium text-gray-900",children:e.title}),t.jsx("p",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[t.jsx(n.C,{variant:"secondary",className:"text-xs",children:e.area}),t.jsx("span",{className:"text-xs text-gray-400",children:e.timestamp})]})]})]},e.id))})})]})}},11165:(e,s,r)=>{"use strict";r.r(s),r.d(s,{UpcomingMilestones:()=>d});var t=r(95344),a=r(23673),n=r(19591),i=r(47210),l=r(5094),o=r(25545),c=r(17910);function d(){let e=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[t.jsx(a.ll,{children:"Upcoming Milestones"}),t.jsx(a.SZ,{children:"Stay on track with your goals"})]}),(0,t.jsxs)(a.aY,{children:[t.jsx("div",{className:"space-y-4",children:[{id:1,title:"Complete quarterly financial review",area:"Finances",dueDate:"Due in 3 days",progress:75,priority:"high"},{id:2,title:"Finish reading 2 books this month",area:"Education & Learning",dueDate:"Due in 1 week",progress:50,priority:"medium"},{id:3,title:"Plan family vacation",area:"Relationships",dueDate:"Due in 2 weeks",progress:25,priority:"medium"},{id:4,title:"Update career development plan",area:"Career & Work",dueDate:"Due in 3 weeks",progress:10,priority:"low"}].map(s=>(0,t.jsxs)("div",{className:"p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[t.jsx("h4",{className:"font-medium text-gray-900 text-sm",children:s.title}),t.jsx(n.C,{className:`text-xs ${e(s.priority)}`,children:s.priority})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[t.jsx(n.C,{variant:"outline",className:"text-xs",children:s.area}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[t.jsx(o.Z,{className:"h-3 w-3 mr-1"}),s.dueDate]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[t.jsx(i.E,{value:s.progress,className:"flex-1 h-1"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[s.progress,"%"]})]}),t.jsx(l.z,{variant:"outline",size:"sm",className:"w-full text-xs",children:"Update Progress"})]},s.id))}),t.jsx("div",{className:"mt-6 pt-4 border-t",children:(0,t.jsxs)(l.z,{variant:"outline",className:"w-full",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"View All Milestones"]})})]})]})}},86062:(e,s,r)=>{"use strict";r.r(s),r.d(s,{DashboardLayout:()=>N});var t=r(95344),a=r(3729),n=r(20783),i=r.n(n),l=r(22254);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var o=r(5094),c=r(2273),d=r(50340),x=r(17910),m=r(46064),p=r(21096),u=r(63211),h=r(18822),g=r(13746),f=r(14513),y=r(48120),v=r(98200);let j=[{name:"Dashboard",href:"/dashboard",icon:c.Z},{name:"Assessment",href:"/assessment",icon:d.Z},{name:"Planning",href:"/planning",icon:x.Z},{name:"Progress",href:"/progress",icon:m.Z},{name:"Analytics",href:"/analytics",icon:p.Z},{name:"Reviews",href:"/reviews",icon:u.Z}],b=[{name:"Profile",href:"/profile",icon:h.Z},{name:"Settings",href:"/settings",icon:g.Z}];function N({children:e}){let[s,r]=(0,a.useState)(!1),n=(0,l.usePathname)();return(0,t.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("fixed inset-0 z-50 lg:hidden",s?"block":"hidden"),children:[t.jsx("div",{className:"fixed inset-0 bg-black/50",onClick:()=>r(!1)}),(0,t.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-card",children:[(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[t.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"}),t.jsx(o.z,{variant:"ghost",size:"sm",onClick:()=>r(!1),children:t.jsx(f.Z,{className:"h-5 w-5"})})]}),t.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:j.map(e=>{let s=n.startsWith(e.href);return(0,t.jsxs)(i(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),onClick:()=>r(!1),children:[t.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",s?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})]}),t.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,t.jsxs)("div",{className:"flex flex-col flex-grow bg-card border-r border-border",children:[t.jsx("div",{className:"flex h-16 items-center px-4",children:t.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"})}),t.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:j.map(e=>{let s=n.startsWith(e.href);return(0,t.jsxs)(i(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),children:[t.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",s?"text-primary-foreground":"text-muted-foreground group-hover:text-foreground")}),e.name]},e.name)})}),(0,t.jsxs)("div",{className:"border-t border-border p-2",children:[b.map(e=>(0,t.jsxs)(i(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[t.jsx(e.icon,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),e.name]},e.name)),(0,t.jsxs)("button",{className:"group flex w-full items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[t.jsx(y.Z,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),"Sign out"]})]})]})}),(0,t.jsxs)("div",{className:"lg:pl-64",children:[(0,t.jsxs)("div",{className:"sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-border bg-background px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8",children:[t.jsx(o.z,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>r(!0),children:t.jsx(v.Z,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:[t.jsx("div",{className:"flex flex-1"}),t.jsx("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:t.jsx("div",{className:"relative","data-testid":"user-menu-trigger",children:(0,t.jsxs)(o.z,{variant:"ghost",size:"sm",className:"flex items-center space-x-2",children:[t.jsx("div",{className:"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center",children:t.jsx("span",{className:"text-sm font-medium text-white",children:"JD"})}),t.jsx("span",{className:"hidden lg:block text-sm font-medium text-foreground",children:"John Doe"})]})})})]})]}),t.jsx("main",{className:"py-8 px-4 sm:px-6 lg:px-8",children:e})]})]})}},47210:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(95344),a=r(3729),n=r(47370);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let i=a.forwardRef(({className:e,value:s,...r},a)=>t.jsx(n.fC,{ref:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:t.jsx(n.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));i.displayName=n.fC.displayName},13285:(e,s,r)=>{"use strict";r.d(s,{Z:()=>i});var t=r(40002),a=r(66689),n=r(399),i=(0,t.cache)(async function(e){let s,r;"string"==typeof e?s=e:e&&(r=e.locale,s=e.namespace);let t=await (0,n.Z)(r);return(0,a.eX)({...t,namespace:s,messages:t.messages})})},3023:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>_,generateMetadata:()=>C});var t=r(25036),a=r(13285),n=r(97385),i=r(86843);let l=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/dashboard-overview.tsx`),{__esModule:o,$$typeof:c}=l;l.default;let d=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/dashboard-overview.tsx#DashboardOverview`),x=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/quick-actions.tsx`),{__esModule:m,$$typeof:p}=x;x.default;let u=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/quick-actions.tsx#QuickActions`),h=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/recent-activity.tsx`),{__esModule:g,$$typeof:f}=h;h.default;let y=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/recent-activity.tsx#RecentActivity`),v=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/progress-summary.tsx`),{__esModule:j,$$typeof:b}=v;v.default;let N=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/progress-summary.tsx#ProgressSummary`),w=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/upcoming-milestones.tsx`),{__esModule:k,$$typeof:Z}=w;w.default;let P=(0,i.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/dashboard/upcoming-milestones.tsx#UpcomingMilestones`);async function C({params:{locale:e}}){let s=await (0,a.Z)({locale:e,namespace:"dashboard"});return{title:s("title"),description:s("description")}}function _(){return t.jsx(n.c,{children:(0,t.jsxs)("div",{className:"space-y-8","data-testid":"dashboard-content",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),t.jsx("p",{className:"mt-2 text-gray-600",children:"Welcome back! Here's an overview of your life planning progress."})]}),t.jsx("div",{className:"flex items-center space-x-4",children:t.jsx(u,{})})]}),t.jsx(d,{}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[t.jsx(N,{}),t.jsx(y,{})]}),t.jsx("div",{className:"space-y-8",children:t.jsx(P,{})})]}),t.jsx("div",{className:"bg-blue-50 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Ready to plan your next chapter?"}),t.jsx("p",{className:"mt-1 text-blue-700",children:"Take a new assessment or update your goals to keep moving forward."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("button",{className:"bg-white text-blue-600 px-4 py-2 rounded-md border border-blue-200 hover:bg-blue-50 transition-colors",children:"Update Goals"}),t.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Start Assessment"})]})]})})]})})}},97385:(e,s,r)=>{"use strict";r.d(s,{c:()=>l});var t=r(86843);let a=(0,t.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let l=(0,t.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx#DashboardLayout`)},47370:(e,s,r)=>{"use strict";r.d(s,{fC:()=>j,z$:()=>b});var t=r(3729),a=r(98462),n=r(62409),i=r(95344),l="Progress",[o,c]=(0,a.b)(l),[d,x]=o(l),m=t.forwardRef((e,s)=>{let{__scopeProgress:r,value:t=null,max:a,getValueLabel:l=h,...o}=e;(a||0===a)&&y(a);let c=y(a)?a:100;null!==t&&v(t,c);let x=v(t,c)?t:null,m=f(x)?l(x,c):void 0;return(0,i.jsx)(d,{scope:r,value:x,max:c,children:(0,i.jsx)(n.WV.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":f(x)?x:void 0,"aria-valuetext":m,role:"progressbar","data-state":g(x,c),"data-value":x??void 0,"data-max":c,...o,ref:s})})});m.displayName=l;var p="ProgressIndicator",u=t.forwardRef((e,s)=>{let{__scopeProgress:r,...t}=e,a=x(p,r);return(0,i.jsx)(n.WV.div,{"data-state":g(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...t,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function g(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function f(e){return"number"==typeof e}function y(e){return f(e)&&!isNaN(e)&&e>0}function v(e,s){return f(e)&&!isNaN(e)&&e<=s&&e>=0}u.displayName=p;var j=m,b=u}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[1638,8356,3828,1476,1498,9669],()=>r(6205));module.exports=t})();