(()=>{var e={};e.id=9829,e.ids=[9829],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},46459:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(50482),s=r(69108),n=r(62563),l=r.n(n),i=r(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d=["",{children:["[locale]",{children:["progress",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,50313)),"/mnt/persist/workspace/frontend/src/app/[locale]/progress/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,21839)),"/mnt/persist/workspace/frontend/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/mnt/persist/workspace/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/mnt/persist/workspace/frontend/src/app/[locale]/progress/page.tsx"],u="/[locale]/progress/page",x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/progress/page",pathname:"/[locale]/progress",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},56588:(e,t,r)=>{Promise.resolve().then(r.bind(r,86062)),Promise.resolve().then(r.bind(r,76209))},65187:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},50340:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},63211:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25545:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},2273:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},48120:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},98200:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},75695:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},21096:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},51838:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},13746:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17910:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},77402:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},46064:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},18822:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20783:(e,t,r)=>{e.exports=r(61476)},76209:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ProgressDashboard:()=>eb});var a=r(95344),s=r(3729),n=r(68962),l=r(23673),i=r(5094),o=r(47210),d=r(19591),c=r(51467),u=r(85222),x=r(31405),m=r(98462),p=r(99048),f=r(33183),g=r(44155),h=r(27386),v=r(31179),j=r(43234),y=r(62409),b=r(1106),N=r(37792),w=r(45904),k=r(32751),D="Dialog",[O,M]=(0,m.b)(D),[C,_]=O(D),R=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:l,onOpenChange:i,modal:o=!0}=e,d=s.useRef(null),c=s.useRef(null),[u,x]=(0,f.T)({prop:n,defaultProp:l??!1,onChange:i,caller:D});return(0,a.jsx)(C,{scope:t,triggerRef:d,contentRef:c,contentId:(0,p.M)(),titleId:(0,p.M)(),descriptionId:(0,p.M)(),open:u,onOpenChange:x,onOpenToggle:s.useCallback(()=>x(e=>!e),[x]),modal:o,children:r})};R.displayName=D;var E="DialogTrigger";s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=_(E,r),l=(0,x.e)(t,n.triggerRef);return(0,a.jsx)(y.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Q(n.open),...s,ref:l,onClick:(0,u.M)(e.onClick,n.onOpenToggle)})}).displayName=E;var Z="DialogPortal",[P,U]=O(Z,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:l}=e,i=_(Z,t);return(0,a.jsx)(P,{scope:t,forceMount:r,children:s.Children.map(n,e=>(0,a.jsx)(j.z,{present:r||i.open,children:(0,a.jsx)(v.h,{asChild:!0,container:l,children:e})}))})};F.displayName=Z;var T="DialogOverlay",A=s.forwardRef((e,t)=>{let r=U(T,e.__scopeDialog),{forceMount:s=r.forceMount,...n}=e,l=_(T,e.__scopeDialog);return l.modal?(0,a.jsx)(j.z,{present:s||l.open,children:(0,a.jsx)(z,{...n,ref:t})}):null});A.displayName=T;var I=(0,k.Z8)("DialogOverlay.RemoveScroll"),z=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=_(T,r);return(0,a.jsx)(N.Z,{as:I,allowPinchZoom:!0,shards:[n.contentRef],children:(0,a.jsx)(y.WV.div,{"data-state":Q(n.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),L="DialogContent",V=s.forwardRef((e,t)=>{let r=U(L,e.__scopeDialog),{forceMount:s=r.forceMount,...n}=e,l=_(L,e.__scopeDialog);return(0,a.jsx)(j.z,{present:s||l.open,children:l.modal?(0,a.jsx)(S,{...n,ref:t}):(0,a.jsx)(q,{...n,ref:t})})});V.displayName=L;var S=s.forwardRef((e,t)=>{let r=_(L,e.__scopeDialog),n=s.useRef(null),l=(0,x.e)(t,r.contentRef,n);return s.useEffect(()=>{let e=n.current;if(e)return(0,w.Ry)(e)},[]),(0,a.jsx)(W,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,u.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,u.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,u.M)(e.onFocusOutside,e=>e.preventDefault())})}),q=s.forwardRef((e,t)=>{let r=_(L,e.__scopeDialog),n=s.useRef(!1),l=s.useRef(!1);return(0,a.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,l.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let a=t.target;r.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),W=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:i,...o}=e,d=_(L,r),c=s.useRef(null),u=(0,x.e)(t,c);return(0,b.EW)(),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.M,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,a.jsx)(g.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":Q(d.open),...o,ref:u,onDismiss:()=>d.onOpenChange(!1)})}),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(et,{titleId:d.titleId}),(0,a.jsx)(er,{contentRef:c,descriptionId:d.descriptionId})]})]})}),$="DialogTitle",G=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=_($,r);return(0,a.jsx)(y.WV.h2,{id:n.titleId,...s,ref:t})});G.displayName=$;var Y="DialogDescription",B=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=_(Y,r);return(0,a.jsx)(y.WV.p,{id:n.descriptionId,...s,ref:t})});B.displayName=Y;var H="DialogClose",K=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=_(H,r);return(0,a.jsx)(y.WV.button,{type:"button",...s,ref:t,onClick:(0,u.M)(e.onClick,()=>n.onOpenChange(!1))})});function Q(e){return e?"open":"closed"}K.displayName=H;var X="DialogTitleWarning",[J,ee]=(0,m.k)(X,{contentName:L,titleName:$,docsSlug:"dialog"}),et=({titleId:e})=>{let t=ee(X),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&document.getElementById(e)},[r,e]),null},er=({contentRef:e,descriptionId:t})=>{let r=ee("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return s.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&document.getElementById(t)},[a,e,t]),null},ea=r(14513);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let es=s.forwardRef(({className:e,...t},r)=>a.jsx(A,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));es.displayName=A.displayName;let en=s.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsxs)(F,{children:[a.jsx(es,{}),(0,a.jsxs)(V,{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,a.jsxs)(K,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(ea.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));en.displayName=V.displayName;let el=({className:e,...t})=>a.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});el.displayName="DialogHeader";let ei=s.forwardRef(({className:e,...t},r)=>a.jsx(G,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-lg font-semibold leading-none tracking-tight",e),...t}));ei.displayName=G.displayName;let eo=s.forwardRef(({className:e,...t},r)=>a.jsx(B,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));eo.displayName=B.displayName;var ed=r(2690),ec=r(33668),eu=r(10410),ex=r(96885),em=r(51838),ep=r(17910),ef=r(65187),eg=r(7060),eh=r(25545),ev=r(46064),ej=r(77402),ey=r(75695);function eb(){let{common:e,lifeAreas:t}=(0,n.Q)(),[r,u]=(0,s.useState)("overview"),[x,m]=(0,s.useState)(!1),[p,f]=(0,s.useState)(null),[g,h]=(0,s.useState)(0),[v,j]=(0,s.useState)(""),y=[{id:"1",title:"Improve Physical Fitness",lifeAreaId:"health",targetRating:7,currentRating:6,targetDate:"2024-12-31",status:"active",progress:85},{id:"2",title:"Advance Career Development",lifeAreaId:"career",targetRating:6,currentRating:5,targetDate:"2024-12-31",status:"active",progress:75},{id:"3",title:"Build Emergency Fund",lifeAreaId:"finances",targetRating:6,currentRating:4,targetDate:"2024-12-31",status:"active",progress:50}],b=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>({area:e,currentRating:Math.floor(3*Math.random())+4,previousRating:Math.floor(3*Math.random())+3,targetRating:Math.floor(2*Math.random())+6,trend:Math.random()>.3?"up":Math.random()>.5?"stable":"down",lastUpdated:"2024-01-15"})),N=b.reduce((e,t)=>e+t.currentRating,0)/b.length;y.filter(e=>"completed"===e.status).length;let w=y.filter(e=>"active"===e.status).length,k=y.reduce((e,t)=>e+t.progress,0)/y.length,D=e=>{f(e),m(!0),h(0),j("")};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Progress Tracking"}),a.jsx("p",{className:"mt-2 text-gray-600",children:"Monitor your journey across all life areas and celebrate your achievements"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(i.z,{variant:"outline",children:[a.jsx(ex.Z,{className:"mr-2 h-4 w-4"}),"Export Report"]}),(0,a.jsxs)(i.z,{onClick:()=>m(!0),children:[a.jsx(em.Z,{className:"mr-2 h-4 w-4"}),"Update Progress"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:a.jsx(ep.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Overall Average"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[N.toFixed(1),"/7"]})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:a.jsx(ef.Z,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Goals Progress"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[Math.round(k),"%"]})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:a.jsx(eg.Z,{className:"h-6 w-6 text-purple-600 dark:text-purple-400"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Active Goals"}),a.jsx("p",{className:"text-2xl font-bold text-foreground",children:w})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg",children:a.jsx(eh.Z,{className:"h-6 w-6 text-orange-600 dark:text-orange-400"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Days Remaining"}),a.jsx("p",{className:"text-2xl font-bold text-foreground",children:"183"})]})]})})})]}),(0,a.jsxs)(c.mQ,{value:r,onValueChange:u,className:"space-y-6",children:[(0,a.jsxs)(c.dr,{className:"grid w-full grid-cols-3",children:[a.jsx(c.SP,{value:"overview",children:"Life Areas"}),a.jsx(c.SP,{value:"goals",children:"Goals"}),a.jsx(c.SP,{value:"achievements",children:"Achievements"})]}),a.jsx(c.nU,{value:"overview",className:"space-y-6",children:(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[a.jsx(l.ll,{children:"Life Areas Progress"}),a.jsx(l.SZ,{children:"Track your improvement across all 12 life areas"})]}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-6",children:b.map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 w-64",children:[a.jsx(e.area.icon,{className:"h-5 w-5 text-muted-foreground"}),a.jsx("span",{className:"text-sm font-medium text-foreground",children:t(`${e.area.key}.name`)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.currentRating,"/",e.targetRating]}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[Math.round(e.currentRating/e.targetRating*100),"%"]})]}),a.jsx(o.E,{value:e.currentRating/e.targetRating*100,className:"h-2"})]}),(0,a.jsxs)("div",{className:"w-16 flex justify-center",children:["up"===e.trend&&a.jsx(ev.Z,{className:"h-4 w-4 text-green-500"}),"down"===e.trend&&a.jsx(ej.Z,{className:"h-4 w-4 text-red-500"}),"stable"===e.trend&&a.jsx("div",{className:"h-4 w-4 bg-muted rounded-full"})]}),a.jsx(i.z,{variant:"outline",size:"sm",onClick:()=>D(e.area.id),children:a.jsx(ey.Z,{className:"h-3 w-3"})})]},r))})})]})}),a.jsx(c.nU,{value:"goals",className:"space-y-6",children:(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(l.ll,{children:"Active Goals"}),a.jsx(l.SZ,{children:"Track progress on your major life goals"})]}),(0,a.jsxs)(i.z,{children:[a.jsx(em.Z,{className:"mr-2 h-4 w-4"}),"Add Goal"]})]})}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-4",children:y.map(e=>{let t=Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).find(t=>t.id===e.lifeAreaId);return(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[t&&a.jsx(t.icon,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium text-foreground",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Target: ",e.currentRating," → ",e.targetRating," by ",new Date(e.targetDate).toLocaleDateString()]})]})]}),a.jsx(d.C,{variant:"active"===e.status?"default":"secondary",children:e.status})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[a.jsx("span",{className:"text-sm text-muted-foreground",children:"Progress"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.progress,"%"]})]}),a.jsx(o.E,{value:e.progress,className:"h-2"})]}),a.jsx(i.z,{variant:"outline",size:"sm",children:a.jsx(ey.Z,{className:"h-3 w-3"})})]})]},e.id)})})})]})}),a.jsx(c.nU,{value:"achievements",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[a.jsx(l.ll,{children:"Recent Achievements"}),a.jsx(l.SZ,{children:"Celebrate your wins and milestones"})]}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-4",children:[{title:"Completed 30-day fitness challenge",area:"Health & Fitness",date:"2 days ago",type:"milestone"},{title:"Reached savings goal of $5,000",area:"Finances",date:"1 week ago",type:"goal"},{title:"Finished online course on leadership",area:"Education & Learning",date:"2 weeks ago",type:"milestone"}].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg",children:[a.jsx("div",{className:"p-1 bg-green-100 dark:bg-green-900/30 rounded-full",children:a.jsx(ef.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium text-foreground",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx(d.C,{variant:"secondary",className:"text-xs",children:e.area}),a.jsx("span",{className:"text-xs text-muted-foreground",children:e.date})]})]})]},t))})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[a.jsx(l.ll,{children:"Upcoming Milestones"}),a.jsx(l.SZ,{children:"Stay focused on what's coming next"})]}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-4",children:[{title:"Complete quarterly financial review",area:"Finances",dueDate:"Due in 3 days",progress:75},{title:"Finish reading 2 books this month",area:"Education & Learning",dueDate:"Due in 1 week",progress:50}].map((e,t)=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[a.jsx("p",{className:"font-medium text-foreground",children:e.title}),a.jsx("span",{className:"text-xs text-muted-foreground",children:e.dueDate})]}),a.jsx("div",{className:"flex items-center space-x-2 mb-2",children:a.jsx(d.C,{variant:"outline",className:"text-xs",children:e.area})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(o.E,{value:e.progress,className:"flex-1 h-1"}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.progress,"%"]})]})]},t))})})]})]})})]}),a.jsx(R,{open:x,onOpenChange:m,children:(0,a.jsxs)(en,{className:"sm:max-w-md",children:[(0,a.jsxs)(el,{children:[a.jsx(ei,{children:"Update Progress"}),a.jsx(eo,{children:"Rate your current satisfaction in this life area"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ec._,{children:"Life Area"}),(0,a.jsxs)("select",{className:"w-full p-2 border rounded-md",children:[a.jsx("option",{value:"",children:"Select a life area..."}),Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>a.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ec._,{children:"Current Rating"}),a.jsx(eu.tl,{value:g,onChange:h})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ec._,{htmlFor:"notes",children:"Notes (Optional)"}),a.jsx(ed.g,{id:"notes",placeholder:"What's changed? Any insights or reflections...",value:v,onChange:e=>j(e.target.value),rows:3})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[a.jsx(i.z,{variant:"outline",onClick:()=>m(!1),children:"Cancel"}),a.jsx(i.z,{onClick:()=>{m(!1),f(null)},disabled:0===g,children:"Save Update"})]})]})]})})]})}!function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()},33668:(e,t,r)=>{"use strict";r.d(t,{_:()=>d});var a=r(95344),s=r(3729),n=r(62409),l=s.forwardRef((e,t)=>(0,a.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},r)=>a.jsx(l,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o(),e),...t}));d.displayName=l.displayName},10410:(e,t,r)=>{"use strict";r.d(t,{tl:()=>n});var a=r(95344);r(3729),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let s={1:{label:"1",description:"Crisis",color:"bg-red-100 text-red-800 border-red-200"},2:{label:"2",description:"Poor",color:"bg-orange-100 text-orange-800 border-orange-200"},3:{label:"3",description:"Below Average",color:"bg-yellow-100 text-yellow-800 border-yellow-200"},4:{label:"4",description:"Average",color:"bg-blue-100 text-blue-800 border-blue-200"},5:{label:"5",description:"Good",color:"bg-indigo-100 text-indigo-800 border-indigo-200"},6:{label:"6",description:"Excellent",color:"bg-green-100 text-green-800 border-green-200"},7:{label:"7",description:"Outstanding",color:"bg-emerald-100 text-emerald-800 border-emerald-200"}};function n({value:e,onChange:t,disabled:r=!1,className:n}){let l=Array.from({length:7},(e,t)=>t+1);return(0,a.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center space-x-1",n),children:[l.map(s=>{let n=e===s;return a.jsx("button",{type:"button",onClick:()=>!r&&t(s),disabled:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-8 h-8 rounded-full border-2 transition-all duration-200 text-sm font-medium","hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1",n?"bg-primary text-primary-foreground border-primary shadow-md scale-110":"bg-white text-gray-600 border-gray-300 hover:border-gray-400",r&&"opacity-50 cursor-not-allowed hover:scale-100"),"aria-label":`Rate ${s}`,children:s},s)}),e>0&&a.jsx("div",{className:"ml-3 text-sm text-gray-600",children:s[e]?.description})]})}},2690:(e,t,r)=>{"use strict";r.d(t,{g:()=>n});var a=r(95344),s=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=s.forwardRef(({className:e,...t},r)=>a.jsx("textarea",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));n.displayName="Textarea"},50313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x,generateMetadata:()=>u});var a=r(25036),s=r(13285),n=r(97385),l=r(86843);let i=(0,l.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/progress/progress-dashboard.tsx`),{__esModule:o,$$typeof:d}=i;i.default;let c=(0,l.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/progress/progress-dashboard.tsx#ProgressDashboard`);async function u({params:{locale:e}}){let t=await (0,s.Z)({locale:e,namespace:"progress"});return{title:t("title"),description:t("description")}}function x(){return a.jsx(n.c,{children:a.jsx("div",{className:"max-w-6xl mx-auto",children:a.jsx(c,{})})})}},47370:(e,t,r)=>{"use strict";r.d(t,{fC:()=>y,z$:()=>b});var a=r(3729),s=r(98462),n=r(62409),l=r(95344),i="Progress",[o,d]=(0,s.b)(i),[c,u]=o(i),x=a.forwardRef((e,t)=>{let{__scopeProgress:r,value:a=null,max:s,getValueLabel:i=f,...o}=e;(s||0===s)&&v(s);let d=v(s)?s:100;null!==a&&j(a,d);let u=j(a,d)?a:null,x=h(u)?i(u,d):void 0;return(0,l.jsx)(c,{scope:r,value:u,max:d,children:(0,l.jsx)(n.WV.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":h(u)?u:void 0,"aria-valuetext":x,role:"progressbar","data-state":g(u,d),"data-value":u??void 0,"data-max":d,...o,ref:t})})});x.displayName=i;var m="ProgressIndicator",p=a.forwardRef((e,t)=>{let{__scopeProgress:r,...a}=e,s=u(m,r);return(0,l.jsx)(n.WV.div,{"data-state":g(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...a,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function v(e){return h(e)&&!isNaN(e)&&e>0}function j(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=m;var y=x,b=p},89128:(e,t,r)=>{"use strict";r.d(t,{VY:()=>E,aV:()=>_,fC:()=>C,xz:()=>R});var a=r(3729),s=r(85222),n=r(98462),l=r(34504),i=r(43234),o=r(62409),d=r(3975),c=r(33183),u=r(99048),x=r(95344),m="Tabs",[p,f]=(0,n.b)(m,[l.Pc]),g=(0,l.Pc)(),[h,v]=p(m),j=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:s,defaultValue:n,orientation:l="horizontal",dir:i,activationMode:p="automatic",...f}=e,g=(0,d.gm)(i),[v,j]=(0,c.T)({prop:a,onChange:s,defaultProp:n??"",caller:m});return(0,x.jsx)(h,{scope:r,baseId:(0,u.M)(),value:v,onValueChange:j,orientation:l,dir:g,activationMode:p,children:(0,x.jsx)(o.WV.div,{dir:g,"data-orientation":l,...f,ref:t})})});j.displayName=m;var y="TabsList",b=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...s}=e,n=v(y,r),i=g(r);return(0,x.jsx)(l.fC,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:a,children:(0,x.jsx)(o.WV.div,{role:"tablist","aria-orientation":n.orientation,...s,ref:t})})});b.displayName=y;var N="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...i}=e,d=v(N,r),c=g(r),u=O(d.baseId,a),m=M(d.baseId,a),p=a===d.value;return(0,x.jsx)(l.ck,{asChild:!0,...c,focusable:!n,active:p,children:(0,x.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...i,ref:t,onMouseDown:(0,s.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,s.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,s.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||n||!e||d.onValueChange(a)})})})});w.displayName=N;var k="TabsContent",D=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,forceMount:n,children:l,...d}=e,c=v(k,r),u=O(c.baseId,s),m=M(c.baseId,s),p=s===c.value,f=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(i.z,{present:n||p,children:({present:r})=>(0,x.jsx)(o.WV.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:r&&l})})});function O(e,t){return`${e}-trigger-${t}`}function M(e,t){return`${e}-content-${t}`}D.displayName=k;var C=j,_=b,R=w,E=D}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,8356,3828,1476,1498,1110,6583,9669,79],()=>r(46459));module.exports=a})();