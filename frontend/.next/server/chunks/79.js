"use strict";exports.id=79,exports.ids=[79],exports.modules={86062:(e,r,t)=>{t.r(r),t.d(r,{DashboardLayout:()=>j});var a=t(95344),s=t(3729),n=t(20783),o=t.n(n),i=t(22254);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var l=t(5094),d=t(2273),c=t(50340),u=t(17910),m=t(46064),f=t(21096),x=t(63211),h=t(18822),g=t(13746),b=t(14513),p=t(48120),D=t(98200);let v=[{name:"Dashboard",href:"/dashboard",icon:d.Z},{name:"Assessment",href:"/assessment",icon:c.Z},{name:"Planning",href:"/planning",icon:u.Z},{name:"Progress",href:"/progress",icon:m.Z},{name:"Analytics",href:"/analytics",icon:f.Z},{name:"Reviews",href:"/reviews",icon:x.Z}],N=[{name:"Profile",href:"/profile",icon:h.Z},{name:"Settings",href:"/settings",icon:g.Z}];function j({children:e}){let[r,t]=(0,s.useState)(!1),n=(0,i.usePathname)();return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("fixed inset-0 z-50 lg:hidden",r?"block":"hidden"),children:[a.jsx("div",{className:"fixed inset-0 bg-black/50",onClick:()=>t(!1)}),(0,a.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-card",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[a.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"}),a.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>t(!1),children:a.jsx(b.Z,{className:"h-5 w-5"})})]}),a.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:v.map(e=>{let r=n.startsWith(e.href);return(0,a.jsxs)(o(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",r?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),onClick:()=>t(!1),children:[a.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",r?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})]}),a.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,a.jsxs)("div",{className:"flex flex-col flex-grow bg-card border-r border-border",children:[a.jsx("div",{className:"flex h-16 items-center px-4",children:a.jsx("h1",{className:"text-xl font-bold text-foreground",children:"8,760 Hours"})}),a.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:v.map(e=>{let r=n.startsWith(e.href);return(0,a.jsxs)(o(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("group flex items-center px-2 py-2 text-sm font-medium rounded-md",r?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),children:[a.jsx(e.icon,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mr-3 h-5 w-5",r?"text-primary-foreground":"text-muted-foreground group-hover:text-foreground")}),e.name]},e.name)})}),(0,a.jsxs)("div",{className:"border-t border-border p-2",children:[N.map(e=>(0,a.jsxs)(o(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[a.jsx(e.icon,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),e.name]},e.name)),(0,a.jsxs)("button",{className:"group flex w-full items-center px-2 py-2 text-sm font-medium text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground",children:[a.jsx(p.Z,{className:"mr-3 h-5 w-5 text-muted-foreground group-hover:text-foreground"}),"Sign out"]})]})]})}),(0,a.jsxs)("div",{className:"lg:pl-64",children:[(0,a.jsxs)("div",{className:"sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-border bg-background px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8",children:[a.jsx(l.z,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>t(!0),children:a.jsx(D.Z,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:[a.jsx("div",{className:"flex flex-1"}),a.jsx("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:a.jsx("div",{className:"relative","data-testid":"user-menu-trigger",children:(0,a.jsxs)(l.z,{variant:"ghost",size:"sm",className:"flex items-center space-x-2",children:[a.jsx("div",{className:"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center",children:a.jsx("span",{className:"text-sm font-medium text-white",children:"JD"})}),a.jsx("span",{className:"hidden lg:block text-sm font-medium text-foreground",children:"John Doe"})]})})})]})]}),a.jsx("main",{className:"py-8 px-4 sm:px-6 lg:px-8",children:e})]})]})}},47210:(e,r,t)=>{t.d(r,{E:()=>o});var a=t(95344),s=t(3729),n=t(47370);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=s.forwardRef(({className:e,value:r,...t},s)=>a.jsx(n.fC,{ref:s,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:a.jsx(n.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));o.displayName=n.fC.displayName},51467:(e,r,t)=>{t.d(r,{SP:()=>l,dr:()=>i,mQ:()=>o,nU:()=>d});var a=t(95344),s=t(3729),n=t(89128);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=n.fC,i=s.forwardRef(({className:e,...r},t)=>a.jsx(n.aV,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...r}));i.displayName=n.aV.displayName;let l=s.forwardRef(({className:e,...r},t)=>a.jsx(n.xz,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...r}));l.displayName=n.xz.displayName;let d=s.forwardRef(({className:e,...r},t)=>a.jsx(n.VY,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...r}));d.displayName=n.VY.displayName},68962:(e,r,t)=>{t.d(r,{Q:()=>d});var a=t(83877),s=t(22254),n=t(41026);let o=["en","es","fr","de","ja","zh","af","zu"],i={en:"English",es:"Espa\xf1ol",fr:"Fran\xe7ais",de:"Deutsch",ja:"日本語",zh:"中文",af:"Afrikaans",zu:"isiZulu"},l={en:"\uD83C\uDDFA\uD83C\uDDF8",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",ja:"\uD83C\uDDEF\uD83C\uDDF5",zh:"\uD83C\uDDE8\uD83C\uDDF3",af:"\uD83C\uDDFF\uD83C\uDDE6",zu:"\uD83C\uDDFF\uD83C\uDDE6"};function d(){let e=(0,a.useTranslations)(),r=(0,a.useLocale)(),t=(0,s.useRouter)(),n=(0,s.usePathname)(),d=(r,t)=>{try{return e(r,t)}catch(e){return r}};return{t:d,locale:r,changeLocale:e=>{let a=n.replace(`/${r}`,"")||"/";t.push(`/${e}${a}`)},getAvailableLocales:()=>o.map(e=>({code:e,name:i[e],flag:l[e],isActive:e===r})),formatDate:(e,t)=>new Intl.DateTimeFormat(r,t).format(e),formatNumber:(e,t)=>new Intl.NumberFormat(r,t).format(e),formatCurrency:(e,t="USD")=>new Intl.NumberFormat(r,{style:"currency",currency:t}).format(e),formatRelativeTime:e=>{let t=new Intl.RelativeTimeFormat(r,{numeric:"auto"}),a=new Date,s=Math.floor((e.getTime()-a.getTime())/1e3);if(60>Math.abs(s))return t.format(s,"second");let n=Math.floor(s/60);if(60>Math.abs(n))return t.format(n,"minute");let o=Math.floor(n/60);if(24>Math.abs(o))return t.format(o,"hour");let i=Math.floor(o/24);if(30>Math.abs(i))return t.format(i,"day");let l=Math.floor(i/30);return 12>Math.abs(l)?t.format(l,"month"):t.format(Math.floor(l/12),"year")},isRTL:()=>["ar","he","fa"].includes(r),common:(e,r)=>d(`common.${e}`,r),navigation:(e,r)=>d(`navigation.${e}`,r),auth:(e,r)=>d(`auth.${e}`,r),validation:(e,r)=>d(`validation.${e}`,r),lifeAreas:(e,r)=>d(`lifeAreas.${e}`,r),assessment:(e,r)=>d(`assessment.${e}`,r)}}(0,n.cF)(async({locale:e})=>(o.includes(e)||(0,s.notFound)(),{locale:e,messages:(await t(98491)(`./${e}.json`)).default}))},97385:(e,r,t)=>{t.d(r,{c:()=>i});var a=t(86843);let s=(0,a.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx`),{__esModule:n,$$typeof:o}=s;s.default;let i=(0,a.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/layouts/dashboard-layout.tsx#DashboardLayout`)}};