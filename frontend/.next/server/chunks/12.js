"use strict";exports.id=12,exports.ids=[12],exports.modules={33037:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},55794:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},51765:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},1222:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},70009:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},75695:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},31498:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},13746:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},23485:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},17910:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},18822:(e,r,t)=>{t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},46540:(e,r,t)=>{t.d(r,{I:()=>s});var a=t(95344),o=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var i=t(66138),n=t(1222),l=t(53148);let s=o.forwardRef(({className:e,type:r,label:t,error:s,helperText:d,leftIcon:c,rightIcon:u,variant:f="default",id:h,required:p,...b},y)=>{let[v,m]=o.useState(!1),[g]=o.useState(()=>h||`input-${Math.random().toString(36).substr(2,9)}`),x="password"===r,O=x&&v?"text":r,k=!!s||"error"===f;return(0,a.jsxs)("div",{className:"space-y-2",children:[t&&(0,a.jsxs)("label",{htmlFor:g,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[t,p&&a.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[c&&a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:c})}),a.jsx("input",{type:O,id:g,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",k&&"border-destructive focus-visible:ring-destructive","success"===f&&"border-green-500 focus-visible:ring-green-500",c&&"pl-10",(u||x||k)&&"pr-10",e),ref:y,"aria-invalid":k,"aria-describedby":s?`${g}-error`:d?`${g}-helper`:void 0,...b}),k&&a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx(i.Z,{className:"h-4 w-4 text-destructive"})}),x&&a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{m(!v)},"aria-label":v?"Hide password":"Show password",children:v?a.jsx(n.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):a.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),u&&!k&&!x&&a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:u})})]}),s&&a.jsx("p",{id:`${g}-error`,className:"text-sm text-destructive",role:"alert",children:s}),d&&!s&&a.jsx("p",{id:`${g}-helper`,className:"text-sm text-muted-foreground",children:d})]})});s.displayName="Input"},33668:(e,r,t)=>{t.d(r,{_:()=>d});var a=t(95344),o=t(3729),i=t(62409),n=o.forwardRef((e,r)=>(0,a.jsx)(i.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var l=t(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let s=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=o.forwardRef(({className:e,...r},t)=>a.jsx(n,{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(s(),e),...r}));d.displayName=n.displayName},50909:(e,r,t)=>{t.d(r,{Z:()=>d});var a=t(95344),o=t(3729),i=t(62409),n="horizontal",l=["horizontal","vertical"],s=o.forwardRef((e,r)=>{let{decorative:t,orientation:o=n,...s}=e,d=l.includes(o)?o:n;return(0,a.jsx)(i.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...s,ref:r})});s.displayName="Separator",function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let d=o.forwardRef(({className:e,orientation:r="horizontal",decorative:t=!0,...o},i)=>a.jsx(s,{ref:i,decorative:t,orientation:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...o}));d.displayName=s.displayName},12772:(e,r,t)=>{t.d(r,{r:()=>k});var a=t(95344),o=t(3729),i=t(85222),n=t(31405),l=t(98462),s=t(33183),d=t(92062),c=t(63085),u=t(62409),f="Switch",[h,p]=(0,l.b)(f),[b,y]=h(f),v=o.forwardRef((e,r)=>{let{__scopeSwitch:t,name:l,checked:d,defaultChecked:c,required:h,disabled:p,value:y="on",onCheckedChange:v,form:m,...g}=e,[k,w]=o.useState(null),N=(0,n.e)(r,e=>w(e)),E=o.useRef(!1),D=!k||m||!!k.closest("form"),[j,U]=(0,s.T)({prop:d,defaultProp:c??!1,onChange:v,caller:f});return(0,a.jsxs)(b,{scope:t,checked:j,disabled:p,children:[(0,a.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":j,"aria-required":h,"data-state":O(j),"data-disabled":p?"":void 0,disabled:p,value:y,...g,ref:N,onClick:(0,i.M)(e.onClick,e=>{U(e=>!e),D&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),D&&(0,a.jsx)(x,{control:k,bubbles:!E.current,name:l,value:y,checked:j,required:h,disabled:p,form:m,style:{transform:"translateX(-100%)"}})]})});v.displayName=f;var m="SwitchThumb",g=o.forwardRef((e,r)=>{let{__scopeSwitch:t,...o}=e,i=y(m,t);return(0,a.jsx)(u.WV.span,{"data-state":O(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:r})});g.displayName=m;var x=o.forwardRef(({__scopeSwitch:e,control:r,checked:t,bubbles:i=!0,...l},s)=>{let u=o.useRef(null),f=(0,n.e)(u,s),h=(0,d.D)(t),p=(0,c.t)(r);return o.useEffect(()=>{let e=u.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==t&&r){let a=new Event("click",{bubbles:i});r.call(e,t),e.dispatchEvent(a)}},[h,t,i]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:f,style:{...l.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function O(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput",function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let k=o.forwardRef(({className:e,...r},t)=>a.jsx(v,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...r,ref:t,children:a.jsx(g,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));k.displayName=v.displayName},2690:(e,r,t)=>{t.d(r,{g:()=>i});var a=t(95344),o=t(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let i=o.forwardRef(({className:e,...r},t)=>a.jsx("textarea",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...r}));i.displayName="Textarea"},33468:(e,r,t)=>{t.d(r,{t:()=>i});var a=t(43158),o=t(67023);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let i=(0,a.Ue)()((0,o.tJ)((e,r)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async r=>{e({isLoading:!0,error:null});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.login(r);e({user:t.user,token:t.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:r instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?r.message:"Login failed. Please try again."}),r}},register:async r=>{e({isLoading:!0,error:null});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.register(r);e({user:t.user,token:t.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:r instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?r.message:"Registration failed. Please try again."}),r}},logout:async()=>{e({isLoading:!0});try{await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.logout()}catch(e){}finally{e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}},refreshToken:async()=>{let{token:t}=r();if(!t)throw Error("No token available for refresh");try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.refreshToken();e({user:r.user,token:r.accessToken,isAuthenticated:!0,error:null})}catch(r){throw e({user:null,token:null,isAuthenticated:!1,error:"Session expired. Please login again."}),r}},updateProfile:async r=>{e({isLoading:!0,error:null});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.updateProfile(r);e({user:t,isLoading:!1,error:null})}catch(r){throw e({isLoading:!1,error:r instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?r.message:"Profile update failed. Please try again."}),r}},clearError:()=>{e({error:null})},setLoading:r=>{e({isLoading:r})},initialize:async()=>{let{token:t}=r();if(t){e({isLoading:!0});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.me();e({user:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}}}}),{name:"auth-storage",partialize:e=>({token:e.token,user:e.user}),onRehydrateStorage:()=>e=>{e?.token&&Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e.token)}})),n=null,l=()=>{let{token:e,refreshToken:r,logout:t}=i.getState();if(n&&clearTimeout(n),e)try{let a=JSON.parse(atob(e.split(".")[1])),o=1e3*a.exp,i=Date.now();n=setTimeout(async()=>{try{await r(),l()}catch(e){await t()}},Math.max(o-i-3e5,0))}catch(e){t()}};i.subscribe(e=>e.token,e=>{e?(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e),l()):(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(null),n&&(clearTimeout(n),n=null))})},92062:(e,r,t)=>{t.d(r,{D:()=>o});var a=t(3729);function o(e){let r=a.useRef({value:e,previous:e});return a.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},63085:(e,r,t)=>{t.d(r,{t:()=>i});var a=t(3729),o=t(16069);function i(e){let[r,t]=a.useState(void 0);return(0,o.b)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let r=new ResizeObserver(r=>{let a,o;if(!Array.isArray(r)||!r.length)return;let i=r[0];if("borderBoxSize"in i){let e=i.borderBoxSize,r=Array.isArray(e)?e[0]:e;a=r.inlineSize,o=r.blockSize}else a=e.offsetWidth,o=e.offsetHeight;t({width:a,height:o})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}t(void 0)},[e]),r}}};