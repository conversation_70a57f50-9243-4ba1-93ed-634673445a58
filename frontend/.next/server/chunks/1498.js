exports.id=1498,exports.ids=[1498],exports.modules={29155:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},66138:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},45961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},79480:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},7060:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1960:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},96885:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},91991:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},33733:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},38271:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},73229:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},14513:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},53283:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.r(t),r.d(t,{default:()=>a});var i=r(3729),s=r.n(i),o=r(13411);function a(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return s().createElement(o.IntlProvider,n({locale:t},r))}},70336:(e,t,r)=>{"use strict";let n=r(3729).createContext(void 0);t.IntlContext=n},13411:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(3729),i=r(66722),s=r(70336);r(16897);var o=function(e){return e&&e.__esModule?e:{default:e}}(n);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:r,formats:a,getMessageFallback:l,locale:u,messages:h,now:c,onError:d,timeZone:f}=e,p=n.useMemo(()=>i.createCache(),[u]),m=n.useMemo(()=>i.createIntlFormatters(p),[p]),y=n.useMemo(()=>({...i.initializeConfig({locale:u,defaultTranslationValues:r,formats:a,getMessageFallback:l,messages:h,now:c,onError:d,timeZone:f}),formatters:m,cache:p}),[p,r,a,m,l,u,h,c,d,f]);return o.default.createElement(s.IntlContext.Provider,{value:y},t)}},66722:(e,t,r)=>{"use strict";var n=r(16897);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function s(e){return i(e.namespace,e.key)}function o(e){}function a(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function l(e,t){return a(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:l(Intl.DateTimeFormat,e.dateTime),getNumberFormat:l(Intl.NumberFormat,e.number),getPluralRules:l(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:l(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:l(Intl.ListFormat,e.list),getDisplayNames:l(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=s,t.defaultOnError=o,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...i}=e;return{...i,messages:r,onError:n||o,getMessageFallback:t||s}},t.joinPath=i,t.memoFn=a},16897:(e,t,r)=>{"use strict";function n(e,t){var r=t&&t.cache?t.cache:l,n=t&&t.serializer?t.serializer:o;return(t&&t.strategy?t.strategy:function(e,t){var r,n,o=1===e.length?i:s;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function i(e,t,r,n){var i=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),s=t.get(i);return void 0===s&&(s=e.call(this,n),t.set(i,s)),s}function s(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),s=t.get(i);return void 0===s&&(s=e.apply(this,n),t.set(i,s)),s}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>u});var o=function(){return JSON.stringify(arguments)},a=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),l={create:function(){return new a}},u={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,s.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)}}},9482:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{Z:()=>y});var i=r(40002),s=r.n(i);let o=(0,r(86843).createProxy)(String.raw`/mnt/persist/workspace/frontend/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js`),{__esModule:a,$$typeof:l}=o,u=o.default;var h=r(399);let c=(0,i.cache)(async function(){return Promise.resolve((await (0,h.Z)()).locale)}),d=(0,i.cache)(async function(e){return(await (0,h.Z)(e)).now});async function f(e){return d(null==e?void 0:e.locale)}let p=(0,i.cache)(async function(e){return(await (0,h.Z)(e)).timeZone});async function m(e){return p(null==e?void 0:e.locale)}async function y(e){let{locale:t,now:r,timeZone:i,...o}=e;return s().createElement(u,n({locale:null!=t?t:await c(),now:null!=r?r:await f(),timeZone:null!=i?i:await m()},o))}},399:(e,t,r)=>{"use strict";r.d(t,{Z:()=>w});var n=r(88726),i=r(40002),s=r(66689);function o(e){return"function"==typeof e.then}var a=r(32455);let l="X-NEXT-INTL-LOCALE",u=(0,i.cache)(function(){return{locale:void 0}});function h(){return u().locale}let c=(0,i.cache)(async function(){let e=(0,a.headers)();return o(e)?await e:e}),d=(0,i.cache)(async function(){let e;try{e=(await c()).get(l)||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function f(){return h()||await d()}let p=(0,i.cache)(function(){let e;try{e=(0,a.headers)().get(l)}catch(e){throw e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest?Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e}):e}return e||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)()),e});var m=r(44759);let y=!1,g=!1,v=(0,i.cache)(function(){return new Date}),b=(0,i.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),E=(0,i.cache)(async function(e,t){if("function"!=typeof e)throw Error("Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n");let r={get locale(){return g||(console.warn("\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),g=!0),t||h()||p()},get requestLocale(){return t?Promise.resolve(t):f()}},i=e(r);o(i)&&(i=await i);let s=i.locale;return s||(y||(console.error("\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\n\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),y=!0),(s=await r.requestLocale)||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)())),{...i,locale:s,now:i.now||v(),timeZone:i.timeZone||b()}}),P=(0,i.cache)(s.PW),T=(0,i.cache)(s.PI),w=(0,i.cache)(async function(e){let t=await E(m.ZP,e);return{...(0,s.tC)(t),_formatters:P(T())}})},95399:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(40002),i=r(399);let s=(0,n.cache)(async function(e){return function(e){if(!e.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return e.messages}(await (0,i.Z)(e))});async function o(e){return s(null==e?void 0:e.locale)}},17231:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{Z:()=>n})},34778:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return s}});let n=r(4910),i=r(45869);function s(){let e=i.staticGenerationAsyncStorage.getStore();(null==e||!e.forceStatic)&&(null==e?void 0:e.isStaticGeneration)&&(0,n.throwWithNoSSR)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84552:(e,t,r)=>{"use strict";function n(e){}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clientHookInServerComponentError",{enumerable:!0,get:function(){return n}}),r(28100),r(40002),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58284:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return i}});let n=r(72973);class i{get isEnabled(){return this._provider.isEnabled}enable(){if(!(0,n.staticGenerationBailout)("draftMode().enable()"))return this._provider.enable()}disable(){if(!(0,n.staticGenerationBailout)("draftMode().disable()"))return this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28290:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{headers:function(){return h},cookies:function(){return c},draftMode:function(){return d}});let n=r(97366),i=r(3022),s=r(63608),o=r(54580),a=r(72934),l=r(72973),u=r(58284);function h(){if((0,l.staticGenerationBailout)("headers",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return i.HeadersAdapter.seal(new Headers({}));let e=o.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: headers() expects to have requestAsyncStorage, none available.");return e.headers}function c(){if((0,l.staticGenerationBailout)("cookies",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return n.RequestCookiesAdapter.seal(new s.RequestCookies(new Headers({})));let e=o.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: cookies() expects to have requestAsyncStorage, none available.");let t=a.actionAsyncStorage.getStore();return t&&(t.isAction||t.isAppRoute)?e.mutableCookies:e.cookies}function d(){let e=o.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: draftMode() expects to have requestAsyncStorage, none available.");return new u.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19738:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return f},useSearchParams:function(){return p},usePathname:function(){return m},ServerInsertedHTMLContext:function(){return l.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return l.useServerInsertedHTML},useRouter:function(){return y},useParams:function(){return g},useSelectedLayoutSegments:function(){return v},useSelectedLayoutSegment:function(){return b},redirect:function(){return u.redirect},permanentRedirect:function(){return u.permanentRedirect},RedirectType:function(){return u.RedirectType},notFound:function(){return h.notFound}});let n=r(40002),i=r(78726),s=r(57210),o=r(84552),a=r(83092),l=r(80545),u=r(8010),h=r(1988),c=Symbol("internal for urlsearchparams readonly");function d(){return Error("ReadonlyURLSearchParams cannot be modified")}class f{[Symbol.iterator](){return this[c][Symbol.iterator]()}append(){throw d()}delete(){throw d()}set(){throw d()}sort(){throw d()}constructor(e){this[c]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}}function p(){(0,o.clientHookInServerComponentError)("useSearchParams");let e=(0,n.useContext)(s.SearchParamsContext),t=(0,n.useMemo)(()=>e?new f(e):null,[e]);{let{bailoutToClientRendering:e}=r(34778);e()}return t}function m(){return(0,o.clientHookInServerComponentError)("usePathname"),(0,n.useContext)(s.PathnameContext)}function y(){(0,o.clientHookInServerComponentError)("useRouter");let e=(0,n.useContext)(i.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function g(){(0,o.clientHookInServerComponentError)("useParams");let e=(0,n.useContext)(i.GlobalLayoutRouterContext),t=(0,n.useContext)(s.PathParamsContext);return(0,n.useMemo)(()=>(null==e?void 0:e.tree)?function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),s=i?t[1]:t;!s||s.startsWith("__PAGE__")||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r}(e.tree):t,[null==e?void 0:e.tree,t])}function v(e){void 0===e&&(e="children"),(0,o.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:t}=(0,n.useContext)(i.LayoutRouterContext);return function e(t,r,n,i){let s;if(void 0===n&&(n=!0),void 0===i&&(i=[]),n)s=t[1][r];else{var o;let e=t[1];s=null!=(o=e.children)?o:Object.values(e)[0]}if(!s)return i;let l=s[0],u=(0,a.getSegmentValue)(l);return!u||u.startsWith("__PAGE__")?i:(i.push(u),e(s,r,!1,i))}(t,e)}function b(e){void 0===e&&(e="children"),(0,o.clientHookInServerComponentError)("useSelectedLayoutSegment");let t=v(e);return 0===t.length?null:t[0]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1988:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{notFound:function(){return n},isNotFoundError:function(){return i}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function i(e){return(null==e?void 0:e.digest)===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45858:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8010:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},redirect:function(){return u},permanentRedirect:function(){return h},isRedirectError:function(){return c},getURLFromRedirectError:function(){return d},getRedirectTypeFromError:function(){return f},getRedirectStatusCodeFromError:function(){return p}});let i=r(54580),s=r(72934),o=r(45858),a="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let n=Error(a);n.digest=a+";"+t+";"+e+";"+r+";";let s=i.requestAsyncStorage.getStore();return s&&(n.mutableCookies=s.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function h(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function c(e){if("string"!=typeof(null==e?void 0:e.digest))return!1;let[t,r,n,i]=e.digest.split(";",4),s=Number(i);return t===a&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(s)&&s in o.RedirectStatusCode}function d(e){return c(e)?e.digest.split(";",3)[2]:null}function f(e){if(!c(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!c(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83092:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97347:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,i],...s]=a(e),{domain:o,expires:l,httponly:c,maxage:d,path:f,samesite:p,secure:m,priority:y}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(i),domain:o,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:f,...p&&{sameSite:u.includes(t=(t=p).toLowerCase())?t:void 0},...m&&{secure:!0},...y&&{priority:h.includes(r=(r=y).toLowerCase())?r:void 0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>c,ResponseCookies:()=>d,parseCookie:()=>a,parseSetCookie:()=>l,stringifyCookie:()=>o}),e.exports=((e,s,o,a)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let o of n(s))i.call(e,o)||void 0===o||t(e,o,{get:()=>s[o],enumerable:!(a=r(s,o))||a.enumerable});return e})(t({},"__esModule",{value:!0}),s);var u=["strict","lax","none"],h=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,s,o=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;l();)if(","===(r=e.charAt(a))){for(n=a,a+=1,l(),i=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=i,o.push(e.substring(t,n)),t=a):a=n+1}else a+=1;(!s||a>=e.length)&&o.push(e.substring(t,e.length))}return o}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},78726:(e,t,r)=>{"use strict";e.exports=r(50482).vendored.contexts.AppRouterContext},57210:(e,t,r)=>{"use strict";e.exports=r(50482).vendored.contexts.HooksClientContext},80545:(e,t,r)=>{"use strict";e.exports=r(50482).vendored.contexts.ServerInsertedHtml},3022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyHeadersError:function(){return i},HeadersAdapter:function(){return s}});let n=r(6250);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,i);let s=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==o)return n.ReflectAdapter.get(t,o,i)},set(t,r,i,s){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,i,s);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,a??r,i,s)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&n.ReflectAdapter.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||n.ReflectAdapter.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},6250:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},97366:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return o},getModifiedCookieValues:function(){return l},appendMutableCookies:function(){return u},MutableRequestCookiesAdapter:function(){return h}});let n=r(63608),i=r(6250);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new s}}class o{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let a=Symbol.for("next.mutated.cookies");function l(e){let t=e[a];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=l(t);if(0===r.length)return!1;let i=new n.ResponseCookies(e),s=i.getAll();for(let e of r)i.set(e);for(let e of s)i.set(e);return!0}class h{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let s=[],o=new Set,l=()=>{var e;let i=null==fetch.__nextGetStaticStore?void 0:null==(e=fetch.__nextGetStaticStore.call(fetch))?void 0:e.getStore();if(i&&(i.pathWasRevalidated=!0),s=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of s){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case a:return s;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{l()}};default:return i.ReflectAdapter.get(e,t,r)}}})}}},63608:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies}});let n=r(97347)},4910:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_DYNAMIC_NO_SSR_CODE:function(){return r},throwWithNoSSR:function(){return n}});let r="NEXT_DYNAMIC_NO_SSR_CODE";function n(){let e=Error(r);throw e.digest=r,e}},32455:(e,t,r)=>{e.exports=r(28290)},88726:(e,t,r)=>{e.exports=r(19738)},66689:(e,t,r)=>{"use strict";var n=r(290),i=r(40149);r(15123),r(40002),r(81105),n.IntlError,n.IntlErrorCode,n.createFormatter,t.PI=i.createCache,t.PW=i.createIntlFormatters,t.tC=i.initializeConfig,t.eX=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),getMessageFallback:s=i.defaultGetMessageFallback,messages:o,namespace:a,onError:l=i.defaultOnError,...u}=e;return function(e,t){let{messages:r,namespace:i,...s}=e;return r=r[t],i=n.resolveNamespace(i,t),n.createBaseTranslator({...s,messages:r,namespace:i})}({...u,onError:l,cache:t,formatters:r,getMessageFallback:s,messages:{"!":o},namespace:a?"!.".concat(a):"!"},"!")}},290:(e,t,r)=>{"use strict";var n=r(15123),i=r(40002),s=r(40149),o=function(e){return e&&e.__esModule?e:{default:e}}(n);function a(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let l=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class u extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),a(this,"code",void 0),a(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function h(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function c(e,t,r,n){let i=s.joinPath(n,r);if(!t)throw Error(i);let o=t;return r.split(".").forEach(t=>{let r=o[t];if(null==t||null==r)throw Error(i+" (".concat(e,")"));o=r}),o}let d=365/12*86400,f={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=u,t.IntlErrorCode=l,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:s.defaultOnError;try{if(!t)throw Error(void 0);let n=r?c(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new u(l.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:a,getMessageFallback:d=s.defaultGetMessageFallback,locale:f,messagesOrError:p,namespace:m,onError:y,timeZone:g}=e,v=p instanceof u;function b(e,t,r){let n=new u(t,r);return y(n),d({error:n,key:e,namespace:m})}function E(e,u,y){let E,P;if(v)return d({error:p,key:e,namespace:m});try{E=c(f,p,e,m)}catch(t){return b(e,l.MISSING_MESSAGE,t.message)}if("object"==typeof E){let t;return b(e,Array.isArray(E)?l.INVALID_MESSAGE:l.INSUFFICIENT_PATH,t)}let T=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(E,u);if(T)return T;a.getMessageFormat||(a.getMessageFormat=s.memoFn(function(){return new o.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:a,...arguments.length<=3?void 0:arguments[3]})},t.message));try{P=a.getMessageFormat(E,f,function(e,t){let r=t?{...e,dateTime:h(e.dateTime,t)}:e,n=o.default.formats.date,i=t?h(n,t):n,s=o.default.formats.time,a=t?h(s,t):s;return{...r,date:{...i,...r.dateTime},time:{...a,...r.dateTime}}}({...n,...y},g),{formatters:{...a,getDateTimeFormat:(e,t)=>a.getDateTimeFormat(e,{timeZone:g,...t})}})}catch(t){return b(e,l.INVALID_MESSAGE,t.message)}try{let e=P.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,s=0,o=e[r];n="function"==typeof o?e=>{let t=o(e);return i.isValidElement(t)?i.cloneElement(t,{key:r+s++}):t}:o,t[r]=n}),t}({...r,...u}));if(null==e)throw Error(void 0);return i.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return b(e,l.FORMATTING_ERROR,t.message)}}function P(e,t,r){let n=E(e,t,r);return"string"!=typeof n?b(e,l.INVALID_MESSAGE,void 0):n}return P.rich=E,P.markup=(e,t,r)=>{let n=E(e,t,r);if("string"!=typeof n){let t=new u(l.FORMATTING_ERROR,void 0);return y(t),d({error:t,key:e,namespace:m})}return n},P.raw=e=>{if(v)return d({error:p,key:e,namespace:m});try{return c(f,p,e,m)}catch(t){return b(e,l.MISSING_MESSAGE,t.message)}},P.has=e=>{if(v)return!1;try{return c(f,p,e,m),!0}catch(e){return!1}},P}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=s.createCache(),_formatters:r=s.createIntlFormatters(t),formats:n,locale:i,now:o,onError:a=s.defaultOnError,timeZone:h}=e;function c(e){var t;return null!==(t=e)&&void 0!==t&&t.timeZone||(h?e={...e,timeZone:h}:a(new u(l.ENVIRONMENT_FALLBACK,void 0))),e}function p(e,t,r,n){let i;try{i=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new u(l.MISSING_FORMAT,void 0);throw a(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(i)}catch(e){return a(new u(l.FORMATTING_ERROR,e.message)),n()}}function m(e,t){return p(t,null==n?void 0:n.dateTime,t=>(t=c(t),r.getDateTimeFormat(i,t).format(e)),()=>String(e))}function y(){return o||(a(new u(l.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:m,number:function(e,t){return p(t,null==n?void 0:n.number,t=>r.getNumberFormat(i,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let s,o;let a={};t instanceof Date||"number"==typeof t?s=new Date(t):t&&(s=null!=t.now?new Date(t.now):y(),o=t.unit,a.style=t.style,a.numberingSystem=t.numberingSystem),s||(s=y());let l=(new Date(e).getTime()-s.getTime())/1e3;o||(o=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<d?"week":t<31536e3?"month":"year"}(l)),a.numeric="second"===o?"auto":"always";let u=(n=o,Math.round(l/f[n]));return r.getRelativeTimeFormat(i,a).format(u,o)}catch(t){return a(new u(l.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let s=[],o=new Map,a=0;for(let t of e){let e;"object"==typeof t?(e=String(a),o.set(e,t)):e=String(t),s.push(e),a++}return p(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(i,e).formatToParts(s).map(e=>"literal"===e.type?e.value:o.get(e.value)||e.value);return o.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,s){return p(s,null==n?void 0:n.dateTime,n=>(n=c(n),r.getDateTimeFormat(i,n).formatRange(e,t)),()=>[m(e),m(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},40149:(e,t,r)=>{"use strict";var n=r(81105);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function s(e){return i(e.namespace,e.key)}function o(e){}function a(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function l(e,t){return a(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:l(Intl.DateTimeFormat,e.dateTime),getNumberFormat:l(Intl.NumberFormat,e.number),getPluralRules:l(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:l(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:l(Intl.ListFormat,e.list),getDisplayNames:l(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=s,t.defaultOnError=o,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...i}=e;return{...i,messages:r,onError:n||o,getMessageFallback:t||s}},t.joinPath=i,t.memoFn=a},81105:(e,t,r)=>{"use strict";function n(e,t){var r=t&&t.cache?t.cache:l,n=t&&t.serializer?t.serializer:o;return(t&&t.strategy?t.strategy:function(e,t){var r,n,o=1===e.length?i:s;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function i(e,t,r,n){var i=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),s=t.get(i);return void 0===s&&(s=e.call(this,n),t.set(i,s)),s}function s(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),s=t.get(i);return void 0===s&&(s=e.apply(this,n),t.set(i,s)),s}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>u});var o=function(){return JSON.stringify(arguments)},a=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),l={create:function(){return new a}},u={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,s.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)}}},15123:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorCode:()=>l,FormatError:()=>es,IntlMessageFormat:()=>ed,InvalidValueError:()=>eo,InvalidValueTypeError:()=>ea,MissingValueError:()=>el,PART_TYPE:()=>u,default:()=>ef,formatToParts:()=>eh,isFormatXMLElementFn:()=>eu});var n,i,s,o,a,l,u,h=function(e,t){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function c(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}h(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var d=function(){return(d=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function f(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create;var p=("function"==typeof SuppressedError&&SuppressedError,r(81105));function m(e){return e.type===i.literal}function y(e){return e.type===i.number}function g(e){return e.type===i.date}function v(e){return e.type===i.time}function b(e){return e.type===i.select}function E(e){return e.type===i.plural}function P(e){return e.type===i.tag}function T(e){return!!(e&&"object"==typeof e&&e.type===s.number)}function w(e){return!!(e&&"object"==typeof e&&e.type===s.dateTime)}(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(s||(s={}));var S=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,A=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,x=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,C=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,M=/^(@+)?(\+|#+)?[rs]?$/g,R=/(\*)(0+)|(#+)(0+)|(0+)/g,O=/^(0+)$/;function L(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(M,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function _(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function k(e){return _(e)||{}}var I={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},D=new RegExp("^".concat(S.source,"*")),N=new RegExp("".concat(S.source,"*$"));function B(e,t){return{start:e,end:t}}var H=!!String.prototype.startsWith&&"_a".startsWith("a",1),F=!!String.fromCodePoint,j=!!Object.fromEntries,V=!!String.prototype.codePointAt,U=!!String.prototype.trimStart,G=!!String.prototype.trimEnd,q=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&9007199254740991>=Math.abs(e)},W=!0;try{var Z=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");W=(null===(o=Z.exec("a"))||void 0===o?void 0:o[0])==="a"}catch(e){W=!1}var z=H?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},K=F?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,s=0;i>s;){if((e=t[s++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},$=j?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],i=n[0],s=n[1];t[i]=s}return t},X=V?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:(i-55296<<10)+(r-56320)+65536}},Y=U?function(e){return e.trimStart()}:function(e){return e.replace(D,"")},Q=G?function(e){return e.trimEnd()}:function(e){return e.replace(N,"")};function J(e,t){return new RegExp(e,t)}if(W){var ee=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");a=function(e,t){var r;return ee.lastIndex=t,null!==(r=ee.exec(e)[1])&&void 0!==r?r:""}}else a=function(e,t){for(var r=[];;){var n,i=X(e,t);if(void 0===i||en(i)||(n=i)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(i),t+=i>=65536?2:1}return K.apply(void 0,r)};var et=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var s=[];!this.isEOF();){var o=this.char();if(123===o){var a=this.parseArgument(e,r);if(a.err)return a;s.push(a.val)}else if(125===o&&e>0)break;else if(35===o&&("plural"===t||"selectordinal"===t)){var l=this.clonePosition();this.bump(),s.push({type:i.pound,location:B(l,this.clonePosition())})}else if(60!==o||this.ignoreTag||47!==this.peek()){if(60===o&&!this.ignoreTag&&er(this.peek()||0)){var a=this.parseTag(e,t);if(a.err)return a;s.push(a.val)}else{var a=this.parseLiteral(e,t);if(a.err)return a;s.push(a.val)}}else{if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,B(this.clonePosition(),this.clonePosition()));break}}return{val:s,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var s=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(s,"/>"),location:B(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,B(r,this.clonePosition()));var o=this.parseMessage(e+1,t,!0);if(o.err)return o;var a=o.val,l=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,B(r,this.clonePosition()));if(this.isEOF()||!er(this.char()))return this.error(n.INVALID_TAG,B(l,this.clonePosition()));var u=this.clonePosition();return s!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,B(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:i.tag,value:s,children:a,location:B(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,B(l,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var s=this.tryParseQuote(t);if(s){n+=s;continue}var o=this.tryParseUnquoted(e,t);if(o){n+=o;continue}var a=this.tryParseLeftAngleBracket();if(a){n+=a;continue}break}var l=B(r,this.clonePosition());return{val:{type:i.literal,value:n,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(er(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r){if(39===this.peek())t.push(39),this.bump();else{this.bump();break}}else t.push(r);this.bump()}return K.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),K(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,B(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,B(r,this.clonePosition()));var s=this.parseIdentifierIfPossible().value;if(!s)return this.error(n.MALFORMED_ARGUMENT,B(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,B(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:s,location:B(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,B(r,this.clonePosition()));return this.parseArgumentOptions(e,t,s,r);default:return this.error(n.MALFORMED_ARGUMENT,B(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=a(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:B(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,o){var a,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,h=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,B(l,h));case"number":case"date":case"time":this.bumpSpace();var c=null;if(this.bumpIf(",")){this.bumpSpace();var f=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=Q(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,B(this.clonePosition(),this.clonePosition()));c={style:m,styleLocation:B(f,this.clonePosition())}}var y=this.tryParseArgumentClose(o);if(y.err)return y;var g=B(o,this.clonePosition());if(c&&z(null==c?void 0:c.style,"::",0)){var v,b=Y(c.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(b,c.styleLocation);if(p.err)return p;return{val:{type:i.number,value:r,location:g,style:p.val},err:null}}if(0===b.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,g);var E=b;this.locale&&(E=function(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if("j"===i){for(var s=0;n+1<e.length&&e.charAt(n+1)===i;)s++,n++;var o=1+(1&s),a=s<2?1:3+(s>>1),l=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(I[t||""]||I[n||""]||I["".concat(n,"-001")]||I["001"])[0]}(t);for(("H"==l||"k"==l)&&(a=0);a-- >0;)r+="a";for(;o-- >0;)r=l+r}else"J"===i?r+="H":r+=i}return r}(b,this.locale));var m={type:s.dateTime,pattern:E,location:c.styleLocation,parsedOptions:this.shouldParseSkeletons?(v={},E.replace(A,function(e){var t=e.length;switch(e[0]){case"G":v.era=4===t?"long":5===t?"narrow":"short";break;case"y":v.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":v.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":v.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":v.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");v.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");v.weekday=["short","long","narrow","short"][t-4];break;case"a":v.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":v.hourCycle="h12",v.hour=["numeric","2-digit"][t-1];break;case"H":v.hourCycle="h23",v.hour=["numeric","2-digit"][t-1];break;case"K":v.hourCycle="h11",v.hour=["numeric","2-digit"][t-1];break;case"k":v.hourCycle="h24",v.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":v.minute=["numeric","2-digit"][t-1];break;case"s":v.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":v.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),v):{}};return{val:{type:"date"===u?i.date:i.time,value:r,location:g,style:m},err:null}}return{val:{type:"number"===u?i.number:"date"===u?i.date:i.time,value:r,location:g,style:null!==(a=null==c?void 0:c.style)&&void 0!==a?a:null},err:null};case"plural":case"selectordinal":case"select":var P=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,B(P,d({},P)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),w=0;if("select"!==u&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,B(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),T=this.parseIdentifierIfPossible(),w=p.val}var S=this.tryParsePluralOrSelectOptions(e,u,t,T);if(S.err)return S;var y=this.tryParseArgumentClose(o);if(y.err)return y;var x=B(o,this.clonePosition());if("select"===u)return{val:{type:i.select,value:r,options:$(S.val),location:x},err:null};return{val:{type:i.plural,value:r,options:$(S.val),offset:w,pluralType:"plural"===u?"cardinal":"ordinal",location:x},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,B(l,h))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,B(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,B(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(x).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var i=t[n].split("/");if(0===i.length)throw Error("Invalid number skeleton");for(var s=i[0],o=i.slice(1),a=0;a<o.length;a++)if(0===o[a].length)throw Error("Invalid number skeleton");r.push({stem:s,options:o})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:s.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=d(d(d({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return d(d({},e),_(t)||{})},{}));continue;case"engineering":t=d(d(d({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return d(d({},e),_(t)||{})},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(R,function(e,r,n,i,s,o){if(r)t.minimumIntegerDigits=n.length;else if(i&&s)throw Error("We currently do not support maximum integer digits");else if(o)throw Error("We currently do not support exact integer digits");return""});continue}if(O.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(C.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(C,function(e,r,n,i,s,o){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:s&&o?(t.minimumFractionDigits=s.length,t.maximumFractionDigits=s.length+o.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var i=n.options[0];"w"===i?t=d(d({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=d(d({},t),L(i)));continue}if(M.test(n.stem)){t=d(d({},t),L(n.stem));continue}var s=_(n.stem);s&&(t=d(d({},t),s));var o=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!O.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);o&&(t=d(d({},t),o))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,i){for(var s,o=!1,a=[],l=new Set,u=i.value,h=i.location;;){if(0===u.length){var c=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;h=B(c,this.clonePosition()),u=this.message.slice(c.offset,this.offset())}else break}if(l.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,h);"other"===u&&(o=!0),this.bumpSpace();var f=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,B(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(f);if(m.err)return m;a.push([u,{value:p.val,location:B(f,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(s=this.parseIdentifierIfPossible()).value,h=s.location}return 0===a.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,B(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!o?this.error(n.MISSING_OTHER_CLAUSE,B(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,s=0;!this.isEOF();){var o=this.char();if(o>=48&&o<=57)i=!0,s=10*s+(o-48),this.bump();else break}var a=B(n,this.clonePosition());return i?q(s*=r)?{val:s,err:null}:this.error(t,a):this.error(e,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=X(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(z(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&en(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function er(e){return e>=97&&e<=122||e>=65&&e<=90}function en(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ei(e,t){void 0===t&&(t={});var r=new et(e,t=d({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var i=SyntaxError(n[r.err.kind]);throw i.location=r.err.location,i.originalMessage=r.err.message,i}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,b(t)||E(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else y(t)&&T(t.style)?delete t.style.location:(g(t)||v(t))&&w(t.style)?delete t.style.location:P(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(l||(l={}));var es=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return c(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),eo=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),l.INVALID_VALUE,i)||this}return c(t,e),t}(es),ea=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),l.INVALID_VALUE,n)||this}return c(t,e),t}(es),el=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),l.MISSING_VALUE,r)||this}return c(t,e),t}(es);function eu(e){return"function"==typeof e}function eh(e,t,r,n,s,o,a){if(1===e.length&&m(e[0]))return[{type:u.literal,value:e[0].value}];for(var h=[],c=0;c<e.length;c++){var d=e[c];if(m(d)){h.push({type:u.literal,value:d.value});continue}if(d.type===i.pound){"number"==typeof o&&h.push({type:u.literal,value:r.getNumberFormat(t).format(o)});continue}var f=d.value;if(!(s&&f in s))throw new el(f,a);var p=s[f];if(d.type===i.argument){p&&"string"!=typeof p&&"number"!=typeof p||(p="string"==typeof p||"number"==typeof p?String(p):""),h.push({type:"string"==typeof p?u.literal:u.object,value:p});continue}if(g(d)){var S="string"==typeof d.style?n.date[d.style]:w(d.style)?d.style.parsedOptions:void 0;h.push({type:u.literal,value:r.getDateTimeFormat(t,S).format(p)});continue}if(v(d)){var S="string"==typeof d.style?n.time[d.style]:w(d.style)?d.style.parsedOptions:n.time.medium;h.push({type:u.literal,value:r.getDateTimeFormat(t,S).format(p)});continue}if(y(d)){var S="string"==typeof d.style?n.number[d.style]:T(d.style)?d.style.parsedOptions:void 0;S&&S.scale&&(p*=S.scale||1),h.push({type:u.literal,value:r.getNumberFormat(t,S).format(p)});continue}if(P(d)){var A=d.children,x=d.value,C=s[x];if(!eu(C))throw new ea(x,"function",a);var M=C(eh(A,t,r,n,s,o).map(function(e){return e.value}));Array.isArray(M)||(M=[M]),h.push.apply(h,M.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(b(d)){var R=d.options[p]||d.options.other;if(!R)throw new eo(d.value,p,Object.keys(d.options),a);h.push.apply(h,eh(R.value,t,r,n,s));continue}if(E(d)){var R=d.options["=".concat(p)];if(!R){if(!Intl.PluralRules)throw new es('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',l.MISSING_INTL_API,a);var O=r.getPluralRules(t,{type:d.pluralType}).select(p-(d.offset||0));R=d.options[O]||d.options.other}if(!R)throw new eo(d.value,p,Object.keys(d.options),a);h.push.apply(h,eh(R.value,t,r,n,s,p-(d.offset||0)));continue}}return h.length<2?h:h.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}function ec(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var ed=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var s,o,a=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=a.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return eh(a.ast,a.locales,a.formatters,a.formats,e,void 0,a.message)},this.resolvedOptions=function(){var e;return{locale:(null===(e=a.resolvedLocale)||void 0===e?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(a.locales)[0]}},this.getAst=function(){return a.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var l=i||{},h=(l.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}(l,["formatters"]));this.ast=e.__parse(t,d(d({},h),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(s=e.formats,n?Object.keys(s).reduce(function(e,t){var r,i;return e[t]=(r=s[t],(i=n[t])?d(d(d({},r||{}),i||{}),Object.keys(r).reduce(function(e,t){return e[t]=d(d({},r[t]),i[t]||{}),e},{})):r),e},d({},s)):s),this.formatters=i&&i.formatters||(void 0===(o=this.formatterCache)&&(o={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,f([void 0],t,!1)))},{cache:ec(o.number),strategy:p.strategies.variadic}),getDateTimeFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,f([void 0],t,!1)))},{cache:ec(o.dateTime),strategy:p.strategies.variadic}),getPluralRules:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,f([void 0],t,!1)))},{cache:ec(o.pluralRules),strategy:p.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=ei,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ef=ed},85222:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:()=>n})},77411:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var n=r(3729),i=r(98462),s=r(31405),o=r(32751),a=r(95344);function l(e){let t=e+"CollectionProvider",[r,l]=(0,i.b)(t),[u,h]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,i=n.useRef(null),s=n.useRef(new Map).current;return(0,a.jsx)(u,{scope:t,itemMap:s,collectionRef:i,children:r})};c.displayName=t;let d=e+"CollectionSlot",f=(0,o.Z8)(d),p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=h(d,r),o=(0,s.e)(t,i.collectionRef);return(0,a.jsx)(f,{ref:o,children:n})});p.displayName=d;let m=e+"CollectionItemSlot",y="data-radix-collection-item",g=(0,o.Z8)(m),v=n.forwardRef((e,t)=>{let{scope:r,children:i,...o}=e,l=n.useRef(null),u=(0,s.e)(t,l),c=h(m,r);return n.useEffect(()=>(c.itemMap.set(l,{ref:l,...o}),()=>void c.itemMap.delete(l))),(0,a.jsx)(g,{[y]:"",ref:u,children:i})});return v.displayName=m,[{Provider:c,Slot:p,ItemSlot:v},function(t){let r=h(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${y}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},98462:(e,t,r)=>{"use strict";r.d(t,{b:()=>o,k:()=>s});var n=r(3729),i=r(95344);function s(e,t){let r=n.createContext(t),s=e=>{let{children:t,...s}=e,o=n.useMemo(()=>s,Object.values(s));return(0,i.jsx)(r.Provider,{value:o,children:t})};return s.displayName=e+"Provider",[s,function(i){let s=n.useContext(r);if(s)return s;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],s=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return s.scopeName=e,[function(t,s){let o=n.createContext(s),a=r.length;r=[...r,s];let l=t=>{let{scope:r,children:s,...l}=t,u=r?.[e]?.[a]||o,h=n.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:h,children:s})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[a]||o,u=n.useContext(l);if(u)return u;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(s,...t)]}},44155:(e,t,r)=>{"use strict";r.d(t,{I0:()=>g,XB:()=>d,fC:()=>y});var n,i=r(3729),s=r(85222),o=r(62409),a=r(31405),l=r(2256),u=r(95344),h="dismissableLayer.update",c=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:y,onInteractOutside:g,onDismiss:v,...b}=e,E=i.useContext(c),[P,T]=i.useState(null),w=P?.ownerDocument??globalThis?.document,[,S]=i.useState({}),A=(0,a.e)(t,e=>T(e)),x=Array.from(E.layers),[C]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),M=x.indexOf(C),R=P?x.indexOf(P):-1,O=E.layersWithOutsidePointerEventsDisabled.size>0,L=R>=M,_=function(e,t=globalThis?.document){let r=(0,l.W)(e),n=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",s.current),s.current=n,t.addEventListener("click",s.current,{once:!0})):n()}else t.removeEventListener("click",s.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",s.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));!L||r||(f?.(e),g?.(e),e.defaultPrevented||v?.())},w),k=function(e,t=globalThis?.document){let r=(0,l.W)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(y?.(e),g?.(e),e.defaultPrevented||v?.())},w);return function(e,t=globalThis?.document){let r=(0,l.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{R!==E.layers.size-1||(d?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},w),i.useEffect(()=>{if(P)return r&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(P)),E.layers.add(P),p(),()=>{r&&1===E.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=n)}},[P,w,r,E]),i.useEffect(()=>()=>{P&&(E.layers.delete(P),E.layersWithOutsidePointerEventsDisabled.delete(P),p())},[P,E]),i.useEffect(()=>{let e=()=>S({});return document.addEventListener(h,e),()=>document.removeEventListener(h,e)},[]),(0,u.jsx)(o.WV.div,{...b,ref:A,style:{pointerEvents:O?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.M)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,s.M)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,s.M)(e.onPointerDownCapture,_.onPointerDownCapture)})});d.displayName="DismissableLayer";var f=i.forwardRef((e,t)=>{let r=i.useContext(c),n=i.useRef(null),s=(0,a.e)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(o.WV.div,{...e,ref:s})});function p(){let e=new CustomEvent(h);document.dispatchEvent(e)}function m(e,t,r,{discrete:n}){let i=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,o.jH)(i,s):i.dispatchEvent(s)}f.displayName="DismissableLayerBranch";var y=d,g=f},31179:(e,t,r)=>{"use strict";r.d(t,{h:()=>l});var n=r(3729),i=r(81202),s=r(62409),o=r(16069),a=r(95344),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,h]=n.useState(!1);(0,o.b)(()=>h(!0),[]);let c=r||u&&globalThis?.document?.body;return c?i.createPortal((0,a.jsx)(s.WV.div,{...l,ref:t}),c):null});l.displayName="Portal"},43234:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var n=r(3729),i=r(31405),s=r(16069),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[i,o]=n.useState(),l=n.useRef(null),u=n.useRef(e),h=n.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=a(l.current);h.current="mounted"===c?e:"none"},[c]),(0,s.b)(()=>{let t=l.current,r=u.current;if(r!==e){let n=h.current,i=a(t);e?d("MOUNT"):"none"===i||t?.display==="none"?d("UNMOUNT"):r&&n!==i?d("ANIMATION_OUT"):d("UNMOUNT"),u.current=e}},[e,d]),(0,s.b)(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,r=r=>{let n=a(l.current).includes(r.animationName);if(r.target===i&&n&&(d("ANIMATION_END"),!u.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(h.current=a(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}d("ANIMATION_END")},[i,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),u=(0,i.e)(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:u}):null};function a(e){return e?.animationName||"none"}o.displayName="Presence"},62409:(e,t,r)=>{"use strict";r.d(t,{WV:()=>a,jH:()=>l});var n=r(3729),i=r(81202),s=r(32751),o=r(95344),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.Z8)(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...s}=e,a=i?r:t;return(0,o.jsx)(a,{...s,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},33390:(e,t,r)=>{"use strict";r.d(t,{Dx:()=>Q,aU:()=>ee,dk:()=>J,fC:()=>Y,l_:()=>X,x8:()=>et,zt:()=>$});var n=r(3729),i=r(81202),s=r(85222),o=r(31405),a=r(77411),l=r(98462),u=r(44155),h=r(31179),c=r(43234),d=r(62409),f=r(2256),p=r(33183),m=r(16069),y=r(87298),g=r(95344),v="ToastProvider",[b,E,P]=(0,a.B)("Toast"),[T,w]=(0,l.b)("Toast",[P]),[S,A]=T(v),x=e=>{let{__scopeToast:t,label:r="Notification",duration:i=5e3,swipeDirection:s="right",swipeThreshold:o=50,children:a}=e,[l,u]=n.useState(null),[h,c]=n.useState(0),d=n.useRef(!1),f=n.useRef(!1);return r.trim(),(0,g.jsx)(b.Provider,{scope:t,children:(0,g.jsx)(S,{scope:t,label:r,duration:i,swipeDirection:s,swipeThreshold:o,toastCount:h,viewport:l,onViewportChange:u,onToastAdd:n.useCallback(()=>c(e=>e+1),[]),onToastRemove:n.useCallback(()=>c(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f,children:a})})};x.displayName=v;var C="ToastViewport",M=["F8"],R="toast.viewportPause",O="toast.viewportResume",L=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:i=M,label:s="Notifications ({hotkey})",...a}=e,l=A(C,r),h=E(r),c=n.useRef(null),f=n.useRef(null),p=n.useRef(null),m=n.useRef(null),y=(0,o.e)(t,m,l.onViewportChange),v=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),P=l.toastCount>0;n.useEffect(()=>{let e=e=>{0!==i.length&&i.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),n.useEffect(()=>{let e=c.current,t=m.current;if(P&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(R);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(O);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||n()},s=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[P,l.isClosePausedRef]);let T=n.useCallback(({tabbingDirection:e})=>{let t=h().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[h]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n){f.current?.focus();return}let i=T({tabbingDirection:n?"backwards":"forwards"}),s=i.findIndex(e=>e===r);K(i.slice(s+1))?t.preventDefault():n?f.current?.focus():p.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[h,T]),(0,g.jsxs)(u.I0,{ref:c,role:"region","aria-label":s.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:P?void 0:"none"},children:[P&&(0,g.jsx)(k,{ref:f,onFocusFromOutsideViewport:()=>{K(T({tabbingDirection:"forwards"}))}}),(0,g.jsx)(b.Slot,{scope:r,children:(0,g.jsx)(d.WV.ol,{tabIndex:-1,...a,ref:y})}),P&&(0,g.jsx)(k,{ref:p,onFocusFromOutsideViewport:()=>{K(T({tabbingDirection:"backwards"}))}})]})});L.displayName=C;var _="ToastFocusProxy",k=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...i}=e,s=A(_,r);return(0,g.jsx)(y.TX,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;s.viewport?.contains(t)||n()}})});k.displayName=_;var I="Toast",D=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:i,onOpenChange:o,...a}=e,[l,u]=(0,p.T)({prop:n,defaultProp:i??!0,onChange:o,caller:I});return(0,g.jsx)(c.z,{present:r||l,children:(0,g.jsx)(H,{open:l,...a,ref:t,onClose:()=>u(!1),onPause:(0,f.W)(e.onPause),onResume:(0,f.W)(e.onResume),onSwipeStart:(0,s.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,s.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,s.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,s.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),u(!1)})})})});D.displayName=I;var[N,B]=T(I,{onClose(){}}),H=n.forwardRef((e,t)=>{let{__scopeToast:r,type:a="foreground",duration:l,open:h,onClose:c,onEscapeKeyDown:p,onPause:m,onResume:y,onSwipeStart:v,onSwipeMove:E,onSwipeCancel:P,onSwipeEnd:T,...w}=e,S=A(I,r),[x,C]=n.useState(null),M=(0,o.e)(t,e=>C(e)),L=n.useRef(null),_=n.useRef(null),k=l||S.duration,D=n.useRef(0),B=n.useRef(k),H=n.useRef(0),{onToastAdd:j,onToastRemove:V}=S,U=(0,f.W)(()=>{x?.contains(document.activeElement)&&S.viewport?.focus(),c()}),G=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(H.current),D.current=new Date().getTime(),H.current=window.setTimeout(U,e))},[U]);n.useEffect(()=>{let e=S.viewport;if(e){let t=()=>{G(B.current),y?.()},r=()=>{let e=new Date().getTime()-D.current;B.current=B.current-e,window.clearTimeout(H.current),m?.()};return e.addEventListener(R,r),e.addEventListener(O,t),()=>{e.removeEventListener(R,r),e.removeEventListener(O,t)}}},[S.viewport,k,m,y,G]),n.useEffect(()=>{h&&!S.isClosePausedRef.current&&G(k)},[h,k,S.isClosePausedRef,G]),n.useEffect(()=>(j(),()=>V()),[j,V]);let q=n.useMemo(()=>x?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!n){if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(x):null,[x]);return S.viewport?(0,g.jsxs)(g.Fragment,{children:[q&&(0,g.jsx)(F,{__scopeToast:r,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:q}),(0,g.jsx)(N,{scope:r,onClose:U,children:i.createPortal((0,g.jsx)(b.ItemSlot,{scope:r,children:(0,g.jsx)(u.fC,{asChild:!0,onEscapeKeyDown:(0,s.M)(p,()=>{S.isFocusedToastEscapeKeyDownRef.current||U(),S.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,g.jsx)(d.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":h?"open":"closed","data-swipe-direction":S.swipeDirection,...w,ref:M,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,s.M)(e.onKeyDown,e=>{"Escape"!==e.key||(p?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(S.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:(0,s.M)(e.onPointerDown,e=>{0===e.button&&(L.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,s.M)(e.onPointerMove,e=>{if(!L.current)return;let t=e.clientX-L.current.x,r=e.clientY-L.current.y,n=!!_.current,i=["left","right"].includes(S.swipeDirection),s=["left","up"].includes(S.swipeDirection)?Math.min:Math.max,o=i?s(0,t):0,a=i?0:s(0,r),l="touch"===e.pointerType?10:2,u={x:o,y:a},h={originalEvent:e,delta:u};n?(_.current=u,Z("toast.swipeMove",E,h,{discrete:!1})):z(u,S.swipeDirection,l)?(_.current=u,Z("toast.swipeStart",v,h,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(L.current=null)}),onPointerUp:(0,s.M)(e.onPointerUp,e=>{let t=_.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),_.current=null,L.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};z(t,S.swipeDirection,S.swipeThreshold)?Z("toast.swipeEnd",T,n,{discrete:!0}):Z("toast.swipeCancel",P,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),S.viewport)})]}):null}),F=e=>{let{__scopeToast:t,children:r,...i}=e,s=A(I,t),[o,a]=n.useState(!1),[l,u]=n.useState(!1);return function(e=()=>{}){let t=(0,f.W)(e);(0,m.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,g.jsx)(h.h,{asChild:!0,children:(0,g.jsx)(y.TX,{...i,children:o&&(0,g.jsxs)(g.Fragment,{children:[s.label," ",r]})})})},j=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(d.WV.div,{...n,ref:t})});j.displayName="ToastTitle";var V=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(d.WV.div,{...n,ref:t})});V.displayName="ToastDescription";var U=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,g.jsx)(W,{altText:r,asChild:!0,children:(0,g.jsx)(q,{...n,ref:t})}):null});U.displayName="ToastAction";var G="ToastClose",q=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,i=B(G,r);return(0,g.jsx)(W,{asChild:!0,children:(0,g.jsx)(d.WV.button,{type:"button",...n,ref:t,onClick:(0,s.M)(e.onClick,i.onClose)})})});q.displayName=G;var W=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...i}=e;return(0,g.jsx)(d.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...i,ref:t})});function Z(e,t,r,{discrete:n}){let i=r.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,d.jH)(i,s):i.dispatchEvent(s)}var z=(e,t,r=0)=>{let n=Math.abs(e.x),i=Math.abs(e.y),s=n>i;return"left"===t||"right"===t?s&&n>r:!s&&i>r};function K(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var $=x,X=L,Y=D,Q=j,J=V,ee=U,et=q},2256:(e,t,r)=>{"use strict";r.d(t,{W:()=>i});var n=r(3729);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},33183:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});var n,i=r(3729),s=r(16069),o=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||s.b;function a({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[s,a,l]=function({defaultProp:e,onChange:t}){let[r,n]=i.useState(e),s=i.useRef(r),a=i.useRef(t);return o(()=>{a.current=t},[t]),i.useEffect(()=>{s.current!==r&&(a.current?.(r),s.current=r)},[r,s]),[r,n,a]}({defaultProp:t,onChange:r}),u=void 0!==e,h=u?e:s;{let t=i.useRef(void 0!==e);i.useEffect(()=>{t.current,t.current=u},[u,n])}return[h,i.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else a(t)},[u,e,a,l])]}Symbol("RADIX:SYNC_STATE")},16069:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var n=r(3729),i=globalThis?.document?n.useLayoutEffect:()=>{}},87298:(e,t,r)=>{"use strict";r.d(t,{C2:()=>o,TX:()=>a});var n=r(3729),i=r(62409),s=r(95344),o=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,s.jsx)(i.WV.span,{...e,ref:t,style:{...o,...e.style}}));a.displayName="VisuallyHidden"},19115:(e,t,r)=>{"use strict";function n(){}function i(e,t){return"function"==typeof e?e(t):e}function s(e,t){let{type:r="all",exact:n,fetchStatus:i,predicate:s,queryKey:o,stale:l}=e;if(o){if(n){if(t.queryHash!==a(o,t.options))return!1}else if(!u(t.queryKey,o))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof l||t.isStale()===l)&&(!i||i===t.state.fetchStatus)&&(!s||!!s(t))}function o(e,t){let{exact:r,status:n,predicate:i,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(r){if(l(t.options.mutationKey)!==l(s))return!1}else if(!u(t.options.mutationKey,s))return!1}return(!n||t.state.status===n)&&(!i||!!i(t))}function a(e,t){return(t?.queryKeyHashFn||l)(e)}function l(e){return JSON.stringify(e,(e,t)=>c(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function u(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>u(e[r],t[r]))}function h(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function c(e){if(!d(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!(d(r)&&r.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function d(e){return"[object Object]"===Object.prototype.toString.call(e)}function f(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}function p(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}r.d(t,{S:()=>D});var m=Symbol();function y(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==m?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var g=e=>setTimeout(e,0),v=function(){let e=[],t=0,r=e=>{e()},n=e=>{e()},i=g,s=n=>{t?e.push(n):i(()=>{r(n)})},o=()=>{let t=e;e=[],t.length&&i(()=>{n(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||o()}return r},batchCalls:e=>(...t)=>{s(()=>{e(...t)})},schedule:s,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{i=e}}}(),b=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},E=new class extends b{#e;#t;#r;constructor(){super(),this.#r=e=>{}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},P=new class extends b{#n;#t;#r;constructor(){super(),this.#n=!0,this.#r=e=>{}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#n!==e&&(this.#n=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#n}};function T(e){return Math.min(1e3*2**e,3e4)}function w(e){return(e??"online")!=="online"||P.isOnline()}var S=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function A(e){return e instanceof S}function x(e){let t,r=!1,n=0,i=!1,s=function(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}(),o=()=>E.isFocused()&&("always"===e.networkMode||P.isOnline())&&e.canRun(),a=()=>w(e.networkMode)&&e.canRun(),l=r=>{i||(i=!0,e.onSuccess?.(r),t?.(),s.resolve(r))},u=r=>{i||(i=!0,e.onError?.(r),t?.(),s.reject(r))},h=()=>new Promise(r=>{t=e=>{(i||o())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,i||e.onContinue?.()}),c=()=>{let t;if(i)return;let s=0===n?e.initialPromise:void 0;try{t=s??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(l).catch(t=>{if(i)return;let s=e.retry??0,a=e.retryDelay??T,l="function"==typeof a?a(n,t):a,d=!0===s||"number"==typeof s&&n<s||"function"==typeof s&&s(n,t);if(r||!d){u(t);return}n++,e.onFail?.(n,t),new Promise(e=>{setTimeout(e,l)}).then(()=>o()?void 0:h()).then(()=>{r?u(t):c()})})};return{promise:s,cancel:t=>{i||(u(new S(t)),e.abort?.())},continue:()=>(t?.(),s),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:a,start:()=>(a()?c():h().then(c),s)}}var C=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??1/0)}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},M=class extends C{#s;#o;#a;#l;#u;#h;#c;constructor(e){super(),this.#c=!1,this.#h=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#a=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#s=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#s,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(e){this.options={...this.#h,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#a.remove(this)}setData(e,t){var r,n;let i=(r=this.state.data,"function"==typeof(n=this.options).structuralSharing?n.structuralSharing(r,e):!1!==n.structuralSharing?function e(t,r){if(t===r)return t;let n=h(t)&&h(r);if(n||c(t)&&c(r)){let i=n?t:Object.keys(t),s=i.length,o=n?r:Object.keys(r),a=o.length,l=n?[]:{},u=new Set(i),h=0;for(let i=0;i<a;i++){let s=n?i:o[i];(!n&&u.has(s)||n)&&void 0===t[s]&&void 0===r[s]?(l[s]=void 0,h++):(l[s]=e(t[s],r[s]),l[s]===t[s]&&void 0!==t[s]&&h++)}return s===a&&h===s?t:l}return r}(r,e):e);return this.#d({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#u?.promise;return this.#u?.cancel(e),t?t.then(n).catch(n):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#s)}isActive(){return this.observers.some(e=>{var t;return!1!==("function"==typeof(t=e.options.enabled)?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===i(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#a.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#u&&(this.#c?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#a.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#c=!0,r.signal)})},i=()=>{let e=y(this.options,t),r=(()=>{let e={client:this.#l,queryKey:this.queryKey,meta:this.meta};return n(e),e})();return(this.#c=!1,this.options.persister)?this.options.persister(e,r,this):e(r)},s=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:i};return n(e),e})();this.options.behavior?.onFetch(s,this),this.#o=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==s.fetchOptions?.meta)&&this.#d({type:"fetch",meta:s.fetchOptions?.meta});let o=e=>{A(e)&&e.silent||this.#d({type:"error",error:e}),A(e)||(this.#a.config.onError?.(e,this),this.#a.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#u=x({initialPromise:t?.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){o(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){o(e);return}this.#a.config.onSuccess?.(e,this),this.#a.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0}),this.#u.start()}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var r;return{...t,...(r=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:w(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let n=e.error;if(A(n)&&n.revert&&this.#o)return{...this.#o,fetchStatus:"idle"};return{...t,error:n,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),v.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#a.notify({query:this,type:"updated",action:e})})}},R=class extends b{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,r){let n=t.queryKey,i=t.queryHash??a(n,t),s=this.get(i);return s||(s=new M({client:e,queryKey:n,queryHash:i,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(s)),s}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){v.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>s(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>s(e,t)):t}notify(e){v.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){v.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){v.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},O=class extends C{#p;#m;#u;constructor(e){super(),this.mutationId=e.mutationId,this.#m=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#d({type:"continue"})};this.#u=x({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#m.canRun(this)});let r="pending"===this.state.status,n=!this.#u.canStart();try{if(r)t();else{this.#d({type:"pending",variables:e,isPaused:n}),await this.#m.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#d({type:"pending",context:t,variables:e,isPaused:n})}let i=await this.#u.start();return await this.#m.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#m.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#d({type:"success",data:i}),i}catch(t){try{throw await this.#m.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#m.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#d({type:"error",error:t})}}finally{this.#m.runNext(this)}}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),v.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#m.notify({mutation:this,type:"updated",action:e})})}},L=class extends b{constructor(e={}){super(),this.config=e,this.#y=new Set,this.#g=new Map,this.#v=0}#y;#g;#v;build(e,t,r){let n=new O({mutationCache:this,mutationId:++this.#v,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#y.add(e);let t=_(e);if("string"==typeof t){let r=this.#g.get(t);r?r.push(e):this.#g.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#y.delete(e)){let t=_(e);if("string"==typeof t){let r=this.#g.get(t);if(r){if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#g.delete(t)}}}this.notify({type:"removed",mutation:e})}canRun(e){let t=_(e);if("string"!=typeof t)return!0;{let r=this.#g.get(t),n=r?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=_(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#g.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){v.batch(()=>{this.#y.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#y.clear(),this.#g.clear()})}getAll(){return Array.from(this.#y)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>o(t,e))}findAll(e={}){return this.getAll().filter(t=>o(e,t))}notify(e){v.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return v.batch(()=>Promise.all(e.map(e=>e.continue().catch(n))))}};function _(e){return e.options.scope?.id}function k(e){return{onFetch:(t,r)=>{let n=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,s=t.state.data?.pages||[],o=t.state.data?.pageParams||[],a={pages:[],pageParams:[]},l=0,u=async()=>{let r=!1,u=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},h=y(t.options,t.fetchOptions),c=async(e,n,i)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let s=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:n,direction:i?"backward":"forward",meta:t.options.meta};return u(e),e})(),o=await h(s),{maxPages:a}=t.options,l=i?p:f;return{pages:l(e.pages,o,a),pageParams:l(e.pageParams,n,a)}};if(i&&s.length){let e="backward"===i,t={pages:s,pageParams:o},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:I)(n,t);a=await c(t,r,e)}else{let t=e??s.length;do{let e=0===l?o[0]??n.initialPageParam:I(n,a);if(l>0&&null==e)break;a=await c(a,e),l++}while(l<t)}return a};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=u}}}function I(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}var D=class{#b;#m;#h;#E;#P;#T;#w;#S;constructor(e={}){this.#b=e.queryCache||new R,this.#m=e.mutationCache||new L,this.#h=e.defaultOptions||{},this.#E=new Map,this.#P=new Map,this.#T=0}mount(){this.#T++,1===this.#T&&(this.#w=E.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onFocus())}),this.#S=P.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onOnline())}))}unmount(){this.#T--,0===this.#T&&(this.#w?.(),this.#w=void 0,this.#S?.(),this.#S=void 0)}isFetching(e){return this.#b.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#m.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#b.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(i(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#b.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let n=this.defaultQueryOptions({queryKey:e}),i=this.#b.get(n.queryHash),s=i?.state.data,o="function"==typeof t?t(s):t;if(void 0!==o)return this.#b.build(this,n).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return v.batch(()=>this.#b.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state}removeQueries(e){let t=this.#b;v.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#b;return v.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(v.batch(()=>this.#b.findAll(e).map(e=>e.cancel(r)))).then(n).catch(n)}invalidateQueries(e,t={}){return v.batch(()=>(this.#b.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(v.batch(()=>this.#b.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#b.build(this,t);return r.isStaleByTime(i(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n).catch(n)}fetchInfiniteQuery(e){return e.behavior=k(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n).catch(n)}ensureInfiniteQueryData(e){return e.behavior=k(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return P.isOnline()?this.#m.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#b}getMutationCache(){return this.#m}getDefaultOptions(){return this.#h}setDefaultOptions(e){this.#h=e}setQueryDefaults(e,t){this.#E.set(l(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#E.values()],r={};return t.forEach(t=>{u(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#P.set(l(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#P.values()],r={};return t.forEach(t=>{u(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#h.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=a(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===m&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#h.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#b.clear(),this.#m.clear()}}},55849:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var n=function(){return null}},26274:(e,t,r)=>{"use strict";r.d(t,{aH:()=>o});var n=r(3729),i=r(95344),s=n.createContext(void 0),o=({client:e,children:t})=>(n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(s.Provider,{value:e,children:t}))},73644:(e,t,r)=>{"use strict";r.d(t,{M:()=>y});var n=r(3729),i=r(19038);function s(){let e=(0,n.useRef)(!1);return(0,i.L)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var o=r(80228),a=r(35986),l=r(40207);class u extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t}){let r=(0,n.useId)(),i=(0,n.useRef)(null),s=(0,n.useRef)({width:0,height:0,top:0,left:0});return(0,n.useInsertionEffect)(()=>{let{width:e,height:n,top:o,left:a}=s.current;if(t||!i.current||!e||!n)return;i.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            top: ${o}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),n.createElement(u,{isPresent:t,childRef:i,sizeRef:s},n.cloneElement(e,{ref:i}))}let c=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:s,presenceAffectsLayout:o,mode:u})=>{let c=(0,l.h)(d),f=(0,n.useId)(),p=(0,n.useMemo)(()=>({id:f,initial:t,isPresent:r,custom:s,onExitComplete:e=>{for(let t of(c.set(e,!0),c.values()))if(!t)return;i&&i()},register:e=>(c.set(e,!1),()=>c.delete(e))}),o?void 0:[r]);return(0,n.useMemo)(()=>{c.forEach((e,t)=>c.set(t,!1))},[r]),n.useEffect(()=>{r||c.size||!i||i()},[r]),"popLayout"===u&&(e=n.createElement(h,{isPresent:r},e)),n.createElement(a.O.Provider,{value:p},e)};function d(){return new Map}var f=r(66828),p=r(87222);let m=e=>e.key||"",y=({children:e,custom:t,initial:r=!0,onExitComplete:a,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:h="sync"})=>{var d;(0,p.k)(!l,"Replace exitBeforeEnter with mode='wait'");let y=(0,n.useContext)(f.p).forceRender||function(){let e=s(),[t,r]=(0,n.useState)(0),i=(0,n.useCallback)(()=>{e.current&&r(t+1)},[t]);return[(0,n.useCallback)(()=>o.Wi.postRender(i),[i]),t]}()[0],g=s(),v=function(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}(e),b=v,E=(0,n.useRef)(new Map).current,P=(0,n.useRef)(b),T=(0,n.useRef)(new Map).current,w=(0,n.useRef)(!0);if((0,i.L)(()=>{w.current=!1,function(e,t){e.forEach(e=>{let r=m(e);t.set(r,e)})}(v,T),P.current=b}),d=()=>{w.current=!0,T.clear(),E.clear()},(0,n.useEffect)(()=>()=>d(),[]),w.current)return n.createElement(n.Fragment,null,b.map(e=>n.createElement(c,{key:m(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:u,mode:h},e)));b=[...b];let S=P.current.map(m),A=v.map(m),x=S.length;for(let e=0;e<x;e++){let t=S[e];-1!==A.indexOf(t)||E.has(t)||E.set(t,void 0)}return"wait"===h&&E.size&&(b=[]),E.forEach((e,r)=>{if(-1!==A.indexOf(r))return;let i=T.get(r);if(!i)return;let s=S.indexOf(r),o=e;o||(o=n.createElement(c,{key:m(i),isPresent:!1,onExitComplete:()=>{E.delete(r);let e=Array.from(T.keys()).filter(e=>!A.includes(e));if(e.forEach(e=>T.delete(e)),P.current=v.filter(t=>{let n=m(t);return n===r||e.includes(n)}),!E.size){if(!1===g.current)return;y(),a&&a()}},custom:t,presenceAffectsLayout:u,mode:h},i),E.set(r,o)),b.splice(s,0,o)}),b=b.map(e=>{let t=e.key;return E.has(t)?e:n.createElement(c,{key:m(e),isPresent:!0,presenceAffectsLayout:u,mode:h},e)}),n.createElement(n.Fragment,null,E.size?b:b.map(e=>(0,n.cloneElement)(e)))}},66828:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});let n=(0,r(3729).createContext)({})},35986:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});let n=(0,r(3729).createContext)(null)},80228:(e,t,r)=>{"use strict";r.d(t,{Pn:()=>a,Wi:()=>o,frameData:()=>l,S6:()=>u});var n=r(30254);class i{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let s=["prepare","read","update","preRender","render","postRender"],{schedule:o,cancel:a,state:l,steps:u}=function(e,t){let r=!1,n=!0,o={delta:0,timestamp:0,isProcessing:!1},a=s.reduce((e,t)=>(e[t]=function(e){let t=new i,r=new i,n=0,s=!1,o=!1,a=new WeakSet,l={schedule:(e,i=!1,o=!1)=>{let l=o&&s,u=l?t:r;return i&&a.add(e),u.add(e)&&l&&s&&(n=t.order.length),e},cancel:e=>{r.remove(e),a.delete(e)},process:i=>{if(s){o=!0;return}if(s=!0,[t,r]=[r,t],r.clear(),n=t.order.length)for(let r=0;r<n;r++){let n=t.order[r];n(i),a.has(n)&&(l.schedule(n),e())}s=!1,o&&(o=!1,l.process(i))}};return l}(()=>r=!0),e),{}),l=e=>a[e].process(o),u=()=>{let i=performance.now();r=!1,o.delta=n?1e3/60:Math.max(Math.min(i-o.timestamp,40),1),o.timestamp=i,o.isProcessing=!0,s.forEach(l),o.isProcessing=!1,r&&t&&(n=!1,e(u))},h=()=>{r=!0,n=!0,o.isProcessing||e(u)};return{schedule:s.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,i=!1)=>(r||h(),n.schedule(e,t,i)),e},{}),cancel:e=>s.forEach(t=>a[t].cancel(e)),state:o,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},57916:(e,t,r)=>{"use strict";r.d(t,{E:()=>iU});var n=r(3729);let i=(0,n.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),s=(0,n.createContext)({});var o=r(35986),a=r(19038);let l=(0,n.createContext)({strict:!1}),u=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),h="data-"+u("framerAppearId");function c(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function d(e){return"string"==typeof e||Array.isArray(e)}function f(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let p=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],m=["initial",...p];function y(e){return f(e.animate)||m.some(t=>d(e[t]))}function g(e){return!!(y(e)||e.variants)}function v(e){return Array.isArray(e)?e.join(" "):e}let b={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},E={};for(let e in b)E[e]={isEnabled:t=>b[e].some(e=>!!t[e])};var P=r(79398),T=r(66828);let w=(0,n.createContext)({}),S=Symbol.for("motionComponentSymbol"),A=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function x(e){if("string"!=typeof e||e.includes("-"));else if(A.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let C={},M=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],R=new Set(M);function O(e,{layout:t,layoutId:r}){return R.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!C[e]||"opacity"===e)}let L=e=>!!(e&&e.getVelocity),_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},k=M.length,I=e=>t=>"string"==typeof t&&t.startsWith(e),D=I("--"),N=I("var(--"),B=(e,t)=>t&&"number"==typeof e?t.transform(e):e,H=(e,t,r)=>Math.min(Math.max(r,e),t),F={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},j={...F,transform:e=>H(0,1,e)},V={...F,default:1},U=e=>Math.round(1e5*e)/1e5,G=/(-)?([\d]*\.?[\d])+/g,q=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,W=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Z(e){return"string"==typeof e}let z=e=>({test:t=>Z(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),K=z("deg"),$=z("%"),X=z("px"),Y=z("vh"),Q=z("vw"),J={...$,parse:e=>$.parse(e)/100,transform:e=>$.transform(100*e)},ee={...F,transform:Math.round},et={borderWidth:X,borderTopWidth:X,borderRightWidth:X,borderBottomWidth:X,borderLeftWidth:X,borderRadius:X,radius:X,borderTopLeftRadius:X,borderTopRightRadius:X,borderBottomRightRadius:X,borderBottomLeftRadius:X,width:X,maxWidth:X,height:X,maxHeight:X,size:X,top:X,right:X,bottom:X,left:X,padding:X,paddingTop:X,paddingRight:X,paddingBottom:X,paddingLeft:X,margin:X,marginTop:X,marginRight:X,marginBottom:X,marginLeft:X,rotate:K,rotateX:K,rotateY:K,rotateZ:K,scale:V,scaleX:V,scaleY:V,scaleZ:V,skew:K,skewX:K,skewY:K,distance:X,translateX:X,translateY:X,translateZ:X,x:X,y:X,z:X,perspective:X,transformPerspective:X,opacity:j,originX:J,originY:J,originZ:X,zIndex:ee,fillOpacity:j,strokeOpacity:j,numOctaves:ee};function er(e,t,r,n){let{style:i,vars:s,transform:o,transformOrigin:a}=e,l=!1,u=!1,h=!0;for(let e in t){let r=t[e];if(D(e)){s[e]=r;continue}let n=et[e],c=B(r,n);if(R.has(e)){if(l=!0,o[e]=c,!h)continue;r!==(n.default||0)&&(h=!1)}else e.startsWith("origin")?(u=!0,a[e]=c):i[e]=c}if(!t.transform&&(l||n?i.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},n,i){let s="";for(let t=0;t<k;t++){let r=M[t];if(void 0!==e[r]){let t=_[r]||r;s+=`${t}(${e[r]}) `}}return t&&!e.z&&(s+="translateZ(0)"),s=s.trim(),i?s=i(e,n?"":s):r&&n&&(s="none"),s}(e.transform,r,h,n):i.transform&&(i.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:r=0}=a;i.transformOrigin=`${e} ${t} ${r}`}}let en=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ei(e,t,r){for(let n in t)L(t[n])||O(n,r)||(e[n]=t[n])}let es=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eo(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||es.has(e)}let ea=e=>!eo(e);try{!function(e){e&&(ea=t=>t.startsWith("on")?!eo(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}function el(e,t,r){return"string"==typeof e?e:X.transform(t+r*e)}let eu={offset:"stroke-dashoffset",array:"stroke-dasharray"},eh={offset:"strokeDashoffset",array:"strokeDasharray"};function ec(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(er(e,u,h,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:f,style:p,dimensions:m}=e;f.transform&&(m&&(p.transform=f.transform),delete f.transform),m&&(void 0!==i||void 0!==s||p.transform)&&(p.transformOrigin=function(e,t,r){let n=el(t,e.x,e.width),i=el(r,e.y,e.height);return`${n} ${i}`}(m,void 0!==i?i:.5,void 0!==s?s:.5)),void 0!==t&&(f.x=t),void 0!==r&&(f.y=r),void 0!==n&&(f.scale=n),void 0!==o&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let s=i?eu:eh;e[s.offset]=X.transform(-n);let o=X.transform(t),a=X.transform(r);e[s.array]=`${o} ${a}`}(f,o,a,l,!1)}let ed=()=>({...en(),attrs:{}}),ef=e=>"string"==typeof e&&"svg"===e.toLowerCase();function ep(e,{style:t,vars:r},n,i){for(let s in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(s,r[s])}let em=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ey(e,t,r,n){for(let r in ep(e,t,void 0,n),t.attrs)e.setAttribute(em.has(r)?r:u(r),t.attrs[r])}function eg(e,t){let{style:r}=e,n={};for(let i in r)(L(r[i])||t.style&&L(t.style[i])||O(i,e))&&(n[i]=r[i]);return n}function ev(e,t){let r=eg(e,t);for(let n in e)(L(e[n])||L(t[n]))&&(r[-1!==M.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}function eb(e,t,r,n={},i={}){return"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),t}var eE=r(40207);let eP=e=>Array.isArray(e),eT=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),ew=e=>eP(e)?e[e.length-1]||0:e;function eS(e){let t=L(e)?e.get():e;return eT(t)?t.toValue():t}let eA=e=>(t,r)=>{let i=(0,n.useContext)(s),a=(0,n.useContext)(o.O),l=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,i,s){let o={latestValues:function(e,t,r,n){let i={},s=n(e,{});for(let e in s)i[e]=eS(s[e]);let{initial:o,animate:a}=e,l=y(e),u=g(e);t&&u&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===a&&(a=t.animate));let h=!!r&&!1===r.initial,c=(h=h||!1===o)?a:o;return c&&"boolean"!=typeof c&&!f(c)&&(Array.isArray(c)?c:[c]).forEach(t=>{let r=eb(e,t);if(!r)return;let{transitionEnd:n,transition:s,...o}=r;for(let e in o){let t=o[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let e in n)i[e]=n[e]}),i}(n,i,s,e),renderState:t()};return r&&(o.mount=e=>r(n,e,o)),o})(e,t,i,a);return r?l():(0,eE.h)(l)};var ex=r(80228);let eC={useVisualState:eA({scrapeMotionValuesFromProps:ev,createRenderState:ed,onMount:(e,t,{renderState:r,latestValues:n})=>{ex.Wi.read(()=>{try{r.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),ex.Wi.render(()=>{ec(r,n,{enableHardwareAcceleration:!1},ef(t.tagName),e.transformTemplate),ey(t,r)})}})},eM={useVisualState:eA({scrapeMotionValuesFromProps:eg,createRenderState:en})};function eR(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let eO=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eL(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let e_=e=>t=>eO(t)&&e(t,eL(t));function ek(e,t,r,n){return eR(e,t,e_(r),n)}let eI=(e,t)=>r=>t(e(r)),eD=(...e)=>e.reduce(eI);function eN(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let eB=eN("dragHorizontal"),eH=eN("dragVertical");function eF(e){let t=!1;if("y"===e)t=eH();else if("x"===e)t=eB();else{let e=eB(),r=eH();e&&r?t=()=>{e(),r()}:(e&&e(),r&&r())}return t}function ej(){let e=eF(!0);return!e||(e(),!1)}class eV{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eU(e,t){let r="onHover"+(t?"Start":"End");return ek(e.current,"pointer"+(t?"enter":"leave"),(n,i)=>{if("touch"===n.pointerType||ej())return;let s=e.getProps();e.animationState&&s.whileHover&&e.animationState.setActive("whileHover",t),s[r]&&ex.Wi.update(()=>s[r](n,i))},{passive:!e.getProps()[r]})}class eG extends eV{mount(){this.unmount=eD(eU(this.node,!0),eU(this.node,!1))}unmount(){}}class eq extends eV{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eD(eR(this.node.current,"focus",()=>this.onFocus()),eR(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let eW=(e,t)=>!!t&&(e===t||eW(e,t.parentElement));var eZ=r(30254);function ez(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,eL(r))}class eK extends eV{constructor(){super(...arguments),this.removeStartListeners=eZ.Z,this.removeEndListeners=eZ.Z,this.removeAccessibleListeners=eZ.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),n=ek(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:n,globalTapTarget:i}=this.node.getProps();ex.Wi.update(()=>{i||eW(this.node.current,e.target)?r&&r(e,t):n&&n(e,t)})},{passive:!(r.onTap||r.onPointerUp)}),i=ek(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=eD(n,i),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eR(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eR(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&ez("up",(e,t)=>{let{onTap:r}=this.node.getProps();r&&ex.Wi.update(()=>r(e,t))})}),ez("down",(e,t)=>{this.startPress(e,t)}))}),t=eR(this.node.current,"blur",()=>{this.isPressing&&ez("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=eD(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&ex.Wi.update(()=>r(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!ej()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&ex.Wi.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=ek(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=eR(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=eD(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let e$=new WeakMap,eX=new WeakMap,eY=e=>{let t=e$.get(e.target);t&&t(e)},eQ=e=>{e.forEach(eY)},eJ={some:0,all:1};class e0 extends eV{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,s={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:eJ[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;eX.has(r)||eX.set(r,{});let n=eX.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(eQ,{root:e,...t})),n[i]}(t);return e$.set(e,r),n.observe(e),()=>{e$.delete(e),n.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),s=t?r:n;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}function e1(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function e2(e,t,r){let n=e.getProps();return eb(n,t,void 0!==r?r:n.custom,function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.getVelocity()),t}(e))}var e3=r(87222);let e8=e=>1e3*e,e9=e=>e/1e3,e6={current:!1},e5=e=>Array.isArray(e)&&"number"==typeof e[0],e4=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,e7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e4([0,.65,.55,1]),circOut:e4([.55,0,1,.45]),backIn:e4([.31,.01,.66,-.59]),backOut:e4([.33,1.53,.69,.99])},te=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function tt(e,t,r,n){if(e===t&&r===n)return eZ.Z;let i=t=>(function(e,t,r,n,i){let s,o;let a=0;do(s=te(o=t+(r-t)/2,n,i)-e)>0?r=o:t=o;while(Math.abs(s)>1e-7&&++a<12);return o})(t,0,1,e,r);return e=>0===e||1===e?e:te(i(e),t,n)}let tr=tt(.42,0,1,1),tn=tt(0,0,.58,1),ti=tt(.42,0,.58,1),ts=e=>Array.isArray(e)&&"number"!=typeof e[0],to=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ta=e=>t=>1-e(1-t),tl=e=>1-Math.sin(Math.acos(e)),tu=ta(tl),th=to(tl),tc=tt(.33,1.53,.69,.99),td=ta(tc),tf=to(td),tp={linear:eZ.Z,easeIn:tr,easeInOut:ti,easeOut:tn,circIn:tl,circInOut:th,circOut:tu,backIn:td,backInOut:tf,backOut:tc,anticipate:e=>(e*=2)<1?.5*td(e):.5*(2-Math.pow(2,-10*(e-1)))},tm=e=>{if(Array.isArray(e)){(0,e3.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return tt(t,r,n,i)}return"string"==typeof e?((0,e3.k)(void 0!==tp[e],`Invalid easing type '${e}'`),tp[e]):e},ty=(e,t)=>r=>!!(Z(r)&&W.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),tg=(e,t,r)=>n=>{if(!Z(n))return n;let[i,s,o,a]=n.match(G);return{[e]:parseFloat(i),[t]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tv=e=>H(0,255,e),tb={...F,transform:e=>Math.round(tv(e))},tE={test:ty("rgb","red"),parse:tg("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+tb.transform(e)+", "+tb.transform(t)+", "+tb.transform(r)+", "+U(j.transform(n))+")"},tP={test:ty("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:tE.transform},tT={test:ty("hsl","hue"),parse:tg("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+$.transform(U(t))+", "+$.transform(U(r))+", "+U(j.transform(n))+")"},tw={test:e=>tE.test(e)||tP.test(e)||tT.test(e),parse:e=>tE.test(e)?tE.parse(e):tT.test(e)?tT.parse(e):tP.parse(e),transform:e=>Z(e)?e:e.hasOwnProperty("red")?tE.transform(e):tT.transform(e)},tS=(e,t,r)=>-r*e+r*t+e;function tA(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}let tx=(e,t,r)=>{let n=e*e;return Math.sqrt(Math.max(0,r*(t*t-n)+n))},tC=[tP,tE,tT],tM=e=>tC.find(t=>t.test(e));function tR(e){let t=tM(e);(0,e3.k)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===tT&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,s=0,o=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,a=2*r-n;i=tA(a,n,e+1/3),s=tA(a,n,e),o=tA(a,n,e-1/3)}else i=s=o=r;return{red:Math.round(255*i),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(r)),r}let tO=(e,t)=>{let r=tR(e),n=tR(t),i={...r};return e=>(i.red=tx(r.red,n.red,e),i.green=tx(r.green,n.green,e),i.blue=tx(r.blue,n.blue,e),i.alpha=tS(r.alpha,n.alpha,e),tE.transform(i))},tL={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:eZ.Z},t_={regex:q,countKey:"Colors",token:"${c}",parse:tw.parse},tk={regex:G,countKey:"Numbers",token:"${n}",parse:F.parse};function tI(e,{regex:t,countKey:r,token:n,parse:i}){let s=e.tokenised.match(t);s&&(e["num"+r]=s.length,e.tokenised=e.tokenised.replace(t,n),e.values.push(...s.map(i)))}function tD(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&tI(r,tL),tI(r,t_),tI(r,tk),r}function tN(e){return tD(e).values}function tB(e){let{values:t,numColors:r,numVars:n,tokenised:i}=tD(e),s=t.length;return e=>{let t=i;for(let i=0;i<s;i++)t=i<n?t.replace(tL.token,e[i]):i<n+r?t.replace(t_.token,tw.transform(e[i])):t.replace(tk.token,U(e[i]));return t}}let tH=e=>"number"==typeof e?0:e,tF={test:function(e){var t,r;return isNaN(e)&&Z(e)&&((null===(t=e.match(G))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(q))||void 0===r?void 0:r.length)||0)>0},parse:tN,createTransformer:tB,getAnimatableNone:function(e){let t=tN(e);return tB(e)(t.map(tH))}},tj=(e,t)=>r=>`${r>0?t:e}`;function tV(e,t){return"number"==typeof e?r=>tS(e,t,r):tw.test(e)?tO(e,t):e.startsWith("var(")?tj(e,t):tq(e,t)}let tU=(e,t)=>{let r=[...e],n=r.length,i=e.map((e,r)=>tV(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}},tG=(e,t)=>{let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=tV(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}},tq=(e,t)=>{let r=tF.createTransformer(t),n=tD(e),i=tD(t);return n.numVars===i.numVars&&n.numColors===i.numColors&&n.numNumbers>=i.numNumbers?eD(tU(n.values,i.values),r):((0,e3.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tj(e,t))},tW=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n},tZ=(e,t)=>r=>tS(e,t,r);function tz(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let s=e.length;if((0,e3.k)(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,r){let n=[],i=r||function(e){if("number"==typeof e);else if("string"==typeof e)return tw.test(e)?tO:tq;else if(Array.isArray(e))return tU;else if("object"==typeof e)return tG;return tZ}(e[0]),s=e.length-1;for(let r=0;r<s;r++){let s=i(e[r],e[r+1]);t&&(s=eD(Array.isArray(t)?t[r]||eZ.Z:t,s)),n.push(s)}return n}(t,n,i),a=o.length,l=t=>{let r=0;if(a>1)for(;r<e.length-2&&!(t<e[r+1]);r++);let n=tW(e[r],e[r+1],t);return o[r](n)};return r?t=>l(H(e[0],e[s-1],t)):l}function tK({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=ts(n)?n.map(tm):tm(n),s={done:!1,value:t[0]},o=tz((r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=tW(0,t,n);e.push(tS(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(i)?i:t.map(()=>i||ti).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}function t$(e,t,r){var n,i;let s=Math.max(t-5,0);return n=r-e(s),(i=t-s)?1e3/i*n:0}function tX(e,t){return e*Math.sqrt(1-t*t)}let tY=["duration","bounce"],tQ=["stiffness","damping","mass"];function tJ(e,t){return t.some(t=>void 0!==e[t])}function t0({keyframes:e,restDelta:t,restSpeed:r,...n}){let i;let s=e[0],o=e[e.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:f}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!tJ(e,tQ)&&tJ(e,tY)){let r=function({duration:e=800,bounce:t=.25,velocity:r=0,mass:n=1}){let i,s;(0,e3.K)(e<=e8(10),"Spring duration must be 10 seconds or less");let o=1-t;o=H(.05,1,o),e=H(.01,10,e9(e)),o<1?(i=t=>{let n=t*o,i=n*e;return .001-(n-r)/tX(t,o)*Math.exp(-i)},s=t=>{let n=t*o*e,s=Math.pow(o,2)*Math.pow(t,2)*e,a=tX(Math.pow(t,2),o);return(n*r+r-s)*Math.exp(-n)*(-i(t)+.001>0?-1:1)/a}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),s=t=>e*e*(r-t)*Math.exp(-t*e));let a=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,s,5/e);if(e=e8(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(a,2)*n;return{stiffness:t,damping:2*o*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:1}).isResolvedFromDuration=!0}return t}({...n,velocity:-e9(n.velocity||0)}),p=d||0,m=u/(2*Math.sqrt(l*h)),y=o-s,g=e9(Math.sqrt(l/h)),v=5>Math.abs(y);if(r||(r=v?.01:2),t||(t=v?.005:.5),m<1){let e=tX(g,m);i=t=>o-Math.exp(-m*g*t)*((p+m*g*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===m)i=e=>o-Math.exp(-g*e)*(y+(p+g*y)*e);else{let e=g*Math.sqrt(m*m-1);i=t=>{let r=Math.exp(-m*g*t),n=Math.min(e*t,300);return o-r*((p+m*g*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}return{calculatedDuration:f&&c||null,next:e=>{let n=i(e);if(f)a.done=e>=c;else{let s=p;0!==e&&(s=m<1?t$(i,e,n):0);let l=Math.abs(s)<=r,u=Math.abs(o-n)<=t;a.done=l&&u}return a.value=a.done?o:n,a}}}function t1({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let f=e[0],p={done:!1,value:f},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,y=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,g=r*t,v=f+g,b=void 0===o?v:o(v);b!==v&&(g=b-f);let E=e=>-g*Math.exp(-e/n),P=e=>b+E(e),T=e=>{let t=E(e),r=P(e);p.done=Math.abs(t)<=u,p.value=p.done?b:r},w=e=>{m(p.value)&&(c=e,d=t0({keyframes:[p.value,y(p.value)],velocity:t$(P,e,p.value),damping:i,stiffness:s,restDelta:u,restSpeed:h}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==c||(t=!0,T(e),w(e)),void 0!==c&&e>c)?d.next(e-c):(t||T(e),p)}}}let t2=e=>{let t=({timestamp:t})=>e(t);return{start:()=>ex.Wi.update(t,!0),stop:()=>(0,ex.Pn)(t),now:()=>ex.frameData.isProcessing?ex.frameData.timestamp:performance.now()}};function t3(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}let t8={decay:t1,inertia:t1,tween:tK,keyframes:tK,spring:t0};function t9({autoplay:e=!0,delay:t=0,driver:r=t2,keyframes:n,type:i="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let f,p,m,y,g,v=1,b=!1,E=()=>{p=new Promise(e=>{f=e})};E();let P=t8[i]||tK;P!==tK&&"number"!=typeof n[0]&&(y=tz([0,100],n,{clamp:!1}),n=[0,100]);let T=P({...d,keyframes:n});"mirror"===a&&(g=P({...d,keyframes:[...n].reverse(),velocity:-(d.velocity||0)}));let w="idle",S=null,A=null,x=null;null===T.calculatedDuration&&s&&(T.calculatedDuration=t3(T));let{calculatedDuration:C}=T,M=1/0,R=1/0;null!==C&&(R=(M=C+o)*(s+1)-o);let O=0,L=e=>{if(null===A)return;v>0&&(A=Math.min(A,e)),v<0&&(A=Math.min(e-R/v,A));let r=(O=null!==S?S:Math.round(e-A)*v)-t*(v>=0?1:-1),i=v>=0?r<0:r>R;O=Math.max(r,0),"finished"===w&&null===S&&(O=R);let l=O,u=T;if(s){let e=Math.min(O,R)/M,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,s+1))%2&&("reverse"===a?(r=1-r,o&&(r-=o/M)):"mirror"===a&&(u=g)),l=H(0,1,r)*M}let h=i?{done:!1,value:n[0]}:u.next(l);y&&(h.value=y(h.value));let{done:d}=h;i||null===C||(d=v>=0?O>=R:O<=0);let f=null===S&&("finished"===w||"running"===w&&d);return c&&c(h.value),f&&I(),h},_=()=>{m&&m.stop(),m=void 0},k=()=>{w="idle",_(),f(),E(),A=x=null},I=()=>{w="finished",h&&h(),_(),f()},D=()=>{if(b)return;m||(m=r(L));let e=m.now();l&&l(),null!==S?A=e-S:A&&"finished"!==w||(A=e),"finished"===w&&E(),x=A,S=null,w="running",m.start()};e&&D();let N={then:(e,t)=>p.then(e,t),get time(){return e9(O)},set time(newTime){O=newTime=e8(newTime),null===S&&m&&0!==v?A=m.now()-newTime/v:S=newTime},get duration(){return e9(null===T.calculatedDuration?t3(T):T.calculatedDuration)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!m)return;v=newSpeed,N.time=e9(O)},get state(){return w},play:D,pause:()=>{w="paused",S=O},stop:()=>{b=!0,"idle"!==w&&(w="idle",u&&u(),k())},cancel:()=>{null!==x&&L(x),k()},complete:()=>{w="finished"},sample:e=>(A=0,L(e))};return N}let t6=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),t5=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),t4=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&e7[t]||e5(t)||Array.isArray(t)&&t.every(e))}(t.ease),t7={type:"spring",stiffness:500,damping:25,restSpeed:10},re=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),rt={type:"keyframes",duration:.8},rr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rn=(e,{keyframes:t})=>t.length>2?rt:R.has(e)?e.startsWith("scale")?re(t[1]):t7:rr,ri=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tF.test(t)||"0"===t)&&!t.startsWith("url(")),rs=new Set(["brightness","contrast","saturate","opacity"]);function ro(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(G)||[];if(!n)return e;let i=r.replace(n,""),s=rs.has(t)?1:0;return n!==r&&(s*=100),t+"("+s+i+")"}let ra=/([a-z-]*)\(.*?\)/g,rl={...tF,getAnimatableNone:e=>{let t=e.match(ra);return t?t.map(ro).join(" "):e}},ru={...et,color:tw,backgroundColor:tw,outlineColor:tw,fill:tw,stroke:tw,borderColor:tw,borderTopColor:tw,borderRightColor:tw,borderBottomColor:tw,borderLeftColor:tw,filter:rl,WebkitFilter:rl},rh=e=>ru[e];function rc(e,t){let r=rh(e);return r!==rl&&(r=tF),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let rd=e=>/^0[^.\s]+$/.test(e);function rf(e,t){return e[t]||e.default||e}let rp={skipAnimations:!1},rm=(e,t,r,n={})=>i=>{let s=rf(n,e)||{},o=s.delay||n.delay||0,{elapsed:a=0}=n;a-=e8(o);let l=function(e,t,r,n){let i,s;let o=ri(t,r);i=Array.isArray(r)?[...r]:[null,r];let a=void 0!==n.from?n.from:e.get(),l=[];for(let e=0;e<i.length;e++){var u;null===i[e]&&(i[e]=0===e?a:i[e-1]),("number"==typeof(u=i[e])?0===u:null!==u?"none"===u||"0"===u||rd(u):void 0)&&l.push(e),"string"==typeof i[e]&&"none"!==i[e]&&"0"!==i[e]&&(s=i[e])}if(o&&l.length&&s)for(let e=0;e<l.length;e++)i[l[e]]=rc(t,s);return i}(t,e,r,s),u=l[0],h=l[l.length-1],c=ri(e,u),d=ri(e,h);(0,e3.K)(c===d,`You are trying to animate ${e} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let f={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{i(),s.onComplete&&s.onComplete()}};if(!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(s)&&(f={...f,...rn(e,f)}),f.duration&&(f.duration=e8(f.duration)),f.repeatDelay&&(f.repeatDelay=e8(f.repeatDelay)),!c||!d||e6.current||!1===s.type||rp.skipAnimations)return function({keyframes:e,delay:t,onUpdate:r,onComplete:n}){let i=()=>(r&&r(e[e.length-1]),n&&n(),{time:0,speed:1,duration:0,play:eZ.Z,pause:eZ.Z,stop:eZ.Z,then:e=>(e(),Promise.resolve()),cancel:eZ.Z,complete:eZ.Z});return t?t9({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}(e6.current?{...f,delay:0}:f);if(!n.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let r=function(e,t,{onUpdate:r,onComplete:n,...i}){let s,o;if(!(t6()&&t5.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type))return!1;let a=!1,l=!1,u=()=>{o=new Promise(e=>{s=e})};u();let{keyframes:h,duration:c=300,ease:d,times:f}=i;if(t4(t,i)){let e=t9({...i,repeat:0,delay:0}),t={done:!1,value:h[0]},r=[],n=0;for(;!t.done&&n<2e4;)t=e.sample(n),r.push(t.value),n+=10;f=void 0,h=r,c=n-10,d="linear"}let p=function(e,t,r,{delay:n=0,duration:i,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){let u={[t]:r};l&&(u.offset=l);let h=function e(t){if(t)return e5(t)?e4(t):Array.isArray(t)?t.map(e):e7[t]}(a);return Array.isArray(h)&&(u.easing=h),e.animate(u,{delay:n,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(e.owner.current,t,h,{...i,duration:c,ease:d,times:f}),m=()=>{l=!1,p.cancel()},y=()=>{l=!0,ex.Wi.update(m),s(),u()};return p.onfinish=()=>{l||(e.set(function(e,{repeat:t,repeatType:r="loop"}){let n=t&&"loop"!==r&&t%2==1?0:e.length-1;return e[n]}(h,i)),n&&n(),y())},{then:(e,t)=>o.then(e,t),attachTimeline:e=>(p.timeline=e,p.onfinish=null,eZ.Z),get time(){return e9(p.currentTime||0)},set time(newTime){p.currentTime=e8(newTime)},get speed(){return p.playbackRate},set speed(newSpeed){p.playbackRate=newSpeed},get duration(){return e9(c)},play:()=>{a||(p.play(),(0,ex.Pn)(m))},pause:()=>p.pause(),stop:()=>{if(a=!0,"idle"===p.playState)return;let{currentTime:t}=p;if(t){let r=t9({...i,autoplay:!1});e.setWithVelocity(r.sample(t-10).value,r.sample(t).value,10)}y()},complete:()=>{l||p.finish()},cancel:y}}(t,e,f);if(r)return r}return t9(f)};function ry(e){return!!(L(e)&&e.add)}let rg=e=>/^\-?\d*\.?\d+$/.test(e);function rv(e,t){-1===e.indexOf(t)&&e.push(t)}function rb(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class rE{constructor(){this.subscriptions=[]}add(e){return rv(this.subscriptions,e),()=>rb(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rP=e=>!isNaN(parseFloat(e)),rT={current:void 0};class rw{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:r,timestamp:n}=ex.frameData;this.lastUpdated!==n&&(this.timeDelta=r,this.lastUpdated=n,ex.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>ex.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=rP(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new rE);let r=this.events[e].add(t);return"change"===e?()=>{r(),ex.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rT.current&&rT.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?1e3/t*e:0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rS(e,t){return new rw(e,t)}let rA=e=>t=>t.test(e),rx=[F,X,$,K,Q,Y,{test:e=>"auto"===e,parse:e=>e}],rC=e=>rx.find(rA(e)),rM=[...rx,tw,tF],rR=e=>rM.find(rA(e));function rO(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...a}=e.makeTargetAnimatable(t),l=e.getValue("willChange");n&&(s=n);let u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(let t in a){let n=e.getValue(t),i=a[t];if(!n||void 0===i||c&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(c,t))continue;let o={delay:r,elapsed:0,...rf(s||{},t)};if(window.HandoffAppearAnimations){let r=e.getProps()[h];if(r){let e=window.HandoffAppearAnimations(r,t,n,ex.Wi);null!==e&&(o.elapsed=e,o.isHandoff=!0)}}let d=!o.isHandoff&&!function(e,t){let r=e.get();if(!Array.isArray(t))return r!==t;for(let e=0;e<t.length;e++)if(t[e]!==r)return!0}(n,i);if("spring"===o.type&&(n.getVelocity()||o.velocity)&&(d=!1),n.animation&&(d=!1),d)continue;n.start(rm(t,n,i,e.shouldReduceMotion&&R.has(t)?{type:!1}:o));let f=n.animation;ry(l)&&(l.add(t),f.then(()=>l.remove(t))),u.push(f)}return o&&Promise.all(u).then(()=>{o&&function(e,t){let r=e2(e,t),{transitionEnd:n={},transition:i={},...s}=r?e.makeTargetAnimatable(r,!1):{};for(let t in s={...s,...n}){let r=ew(s[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,rS(r))}}(e,o)}),u}function rL(e,t,r={}){let n=e2(e,t,r.custom),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let s=n?()=>Promise.all(rO(e,n,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=i;return function(e,t,r=0,n=0,i=1,s){let o=[],a=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>a-e*n;return Array.from(e.variantChildren).sort(r_).forEach((e,n)=>{e.notify("AnimationStart",t),o.push(rL(e,t,{...s,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,s+n,o,a,r)}:()=>Promise.resolve(),{when:a}=i;if(!a)return Promise.all([s(),o(r.delay)]);{let[e,t]="beforeChildren"===a?[s,o]:[o,s];return e().then(()=>t())}}function r_(e,t){return e.sortNodePosition(t)}let rk=[...p].reverse(),rI=p.length;function rD(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rN extends eV{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>rL(e,t,r)));else if("string"==typeof t)n=rL(e,t,r);else{let i="function"==typeof t?e2(e,t,r.custom):t;n=Promise.all(rO(e,i,r))}return n.then(()=>e.notify("AnimationComplete",t))})(e,t,r))),r={animate:rD(!0),whileInView:rD(),whileHover:rD(),whileTap:rD(),whileDrag:rD(),whileFocus:rD(),exit:rD()},n=!0,i=(t,r)=>{let n=e2(e,r);if(n){let{transition:e,transitionEnd:r,...i}=n;t={...t,...i,...r}}return t};function s(s,o){let a=e.getProps(),l=e.getVariantContext(!0)||{},u=[],h=new Set,c={},p=1/0;for(let t=0;t<rI;t++){var m;let y=rk[t],g=r[y],v=void 0!==a[y]?a[y]:l[y],b=d(v),E=y===o?g.isActive:null;!1===E&&(p=t);let P=v===l[y]&&v!==a[y]&&b;if(P&&n&&e.manuallyAnimateOnMount&&(P=!1),g.protectedKeys={...c},!g.isActive&&null===E||!v&&!g.prevProp||f(v)||"boolean"==typeof v)continue;let T=(m=g.prevProp,("string"==typeof v?v!==m:!!Array.isArray(v)&&!e1(v,m))||y===o&&g.isActive&&!P&&b||t>p&&b),w=!1,S=Array.isArray(v)?v:[v],A=S.reduce(i,{});!1===E&&(A={});let{prevResolvedValues:x={}}=g,C={...x,...A},M=e=>{T=!0,h.has(e)&&(w=!0,h.delete(e)),g.needsAnimating[e]=!0};for(let e in C){let t=A[e],r=x[e];if(!c.hasOwnProperty(e))(eP(t)&&eP(r)?e1(t,r):t===r)?void 0!==t&&h.has(e)?M(e):g.protectedKeys[e]=!0:void 0!==t?M(e):h.add(e)}g.prevProp=v,g.prevResolvedValues=A,g.isActive&&(c={...c,...A}),n&&e.blockInitialAnimation&&(T=!1),T&&(!P||w)&&u.push(...S.map(e=>({animation:e,options:{type:y,...s}})))}if(h.size){let t={};h.forEach(r=>{let n=e.getBaseTarget(r);void 0!==n&&(t[r]=n)}),u.push({animation:t})}let y=!!u.length;return n&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(y=!1),n=!1,y?t(u):Promise.resolve()}return{animateChanges:s,setActive:function(t,n,i){var o;if(r[t].isActive===n)return Promise.resolve();null===(o=e.variantChildren)||void 0===o||o.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let a=s(i,t);for(let e in r)r[e].protectedKeys={};return a},setAnimateFunction:function(r){t=r(e)},getState:()=>r}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),f(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let rB=0;class rH extends eV{constructor(){super(...arguments),this.id=rB++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let i=this.node.animationState.setActive("exit",!e,{custom:null!=r?r:this.node.getProps().custom});t&&!e&&i.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let rF=(e,t)=>Math.abs(e-t);class rj{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rG(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(rF(e.x,t.x)**2+rF(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=ex.frameData;this.history.push({...n,timestamp:i});let{onStart:s,onMove:o}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rV(t,this.transformPagePoint),ex.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rG("pointercancel"===e.type?this.lastMoveEventInfo:rV(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,s),n&&n(e,s)},!eO(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let s=rV(eL(e),this.transformPagePoint),{point:o}=s,{timestamp:a}=ex.frameData;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,rG(s,this.history)),this.removeListeners=eD(ek(this.contextWindow,"pointermove",this.handlePointerMove),ek(this.contextWindow,"pointerup",this.handlePointerUp),ek(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,ex.Pn)(this.updatePoint)}}function rV(e,t){return t?{point:t(e.point)}:e}function rU(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rG({point:e},t){return{point:e,delta:rU(e,rq(t)),offset:rU(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rq(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>e8(.1)));)r--;if(!n)return{x:0,y:0};let s=e9(i.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(i.x-n.x)/s,y:(i.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,0)}}function rq(e){return e[e.length-1]}function rW(e){return e.max-e.min}function rZ(e,t=0,r=.01){return Math.abs(e-t)<=r}function rz(e,t,r,n=.5){e.origin=n,e.originPoint=tS(t.min,t.max,e.origin),e.scale=rW(r)/rW(t),(rZ(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=tS(r.min,r.max,e.origin)-e.originPoint,(rZ(e.translate)||isNaN(e.translate))&&(e.translate=0)}function rK(e,t,r,n){rz(e.x,t.x,r.x,n?n.originX:void 0),rz(e.y,t.y,r.y,n?n.originY:void 0)}function r$(e,t,r){e.min=r.min+t.min,e.max=e.min+rW(t)}function rX(e,t,r){e.min=t.min-r.min,e.max=e.min+rW(t)}function rY(e,t,r){rX(e.x,t.x,r.x),rX(e.y,t.y,r.y)}function rQ(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rJ(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function r0(e,t,r){return{min:r1(e,t),max:r1(e,r)}}function r1(e,t){return"number"==typeof e?e:e[t]||0}let r2=()=>({translate:0,scale:1,origin:0,originPoint:0}),r3=()=>({x:r2(),y:r2()}),r8=()=>({min:0,max:0}),r9=()=>({x:r8(),y:r8()});function r6(e){return[e("x"),e("y")]}function r5({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function r4(e){return void 0===e||1===e}function r7({scale:e,scaleX:t,scaleY:r}){return!r4(e)||!r4(t)||!r4(r)}function ne(e){return r7(e)||nt(e)||e.z||e.rotate||e.rotateX||e.rotateY}function nt(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function nr(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function nn(e,t=0,r=1,n,i){e.min=nr(e.min,t,r,n,i),e.max=nr(e.max,t,r,n,i)}function ni(e,{x:t,y:r}){nn(e.x,t.translate,t.scale,t.originPoint),nn(e.y,r.translate,r.scale,r.originPoint)}function ns(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function no(e,t){e.min=e.min+t,e.max=e.max+t}function na(e,t,[r,n,i]){let s=void 0!==t[i]?t[i]:.5,o=tS(e.min,e.max,s);nn(e,t[r],t[n],o,t.scale)}let nl=["x","scaleX","originX"],nu=["y","scaleY","originY"];function nh(e,t){na(e.x,t,nl),na(e.y,t,nu)}function nc(e,t){return r5(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let nd=({current:e})=>e?e.ownerDocument.defaultView:null,nf=new WeakMap;class np{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=r9(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new rj(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eL(e,"page").point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=eF(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),r6(e=>{let t=this.getAxisMotionValue(e).get()||0;if($.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];if(n){let e=rW(n);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),i&&ex.Wi.update(()=>i(e,t),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:s}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:o}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(o),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>r6(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:nd(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&ex.Wi.update(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!nm(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),s=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?tS(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?tS(r,e,n.max):Math.min(e,r)),e}(s,this.constraints[e],this.elastic[e])),i.set(s)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&c(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:rQ(e.x,r,i),y:rQ(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r0(e,"left","right"),y:r0(e,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&r6(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!c(t))return!1;let n=t.current;(0,e3.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let s=function(e,t,r){let n=nc(e,r),{scroll:i}=t;return i&&(no(n.x,i.offset.x),no(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),o={x:rJ((e=i.layout.layoutBox).x,s.x),y:rJ(e.y,s.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=r5(e))}return o}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(r6(o=>{if(!nm(o,t,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(rm(e,r,0,t))}stopAnimation(){r6(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){r6(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){r6(t=>{let{drag:r}=this.getProps();if(!nm(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:s}=n.layout.layoutBox[t];i.set(e[t]-tS(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!c(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};r6(e=>{let t=this.getAxisMotionValue(e);if(t){let r=t.get();n[e]=function(e,t){let r=.5,n=rW(e),i=rW(t);return i>n?r=tW(t.min,t.max-n,e.min):n>i&&(r=tW(e.min,e.max-i,t.min)),H(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),r6(t=>{if(!nm(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:s}=this.constraints[t];r.set(tS(i,s,n[t]))})}addListeners(){if(!this.visualElement.current)return;nf.set(this.visualElement,this);let e=ek(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();c(e)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),t();let i=eR(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(r6(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:s=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:s,dragMomentum:o}}}function nm(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class ny extends eV{constructor(e){super(e),this.removeGroupControls=eZ.Z,this.removeListeners=eZ.Z,this.controls=new np(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eZ.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let ng=e=>(t,r)=>{e&&ex.Wi.update(()=>e(t,r))};class nv extends eV{constructor(){super(...arguments),this.removePointerDownListener=eZ.Z}onPointerDown(e){this.session=new rj(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nd(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:ng(e),onStart:ng(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&ex.Wi.update(()=>n(e,t))}}}mount(){this.removePointerDownListener=ek(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let nb={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nE(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nP={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!X.test(e))return e;e=parseFloat(e)}let r=nE(e,t.target.x),n=nE(e,t.target.y);return`${r}% ${n}%`}};class nT extends n.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;Object.assign(C,nS),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nb.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,n||e.layoutDependency!==t||void 0===t?s.willUpdate():this.safeToRemove(),e.isPresent===i||(i?s.promote():s.relegate()||ex.Wi.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nw(e){let[t,r]=function(){let e=(0,n.useContext)(o.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:r,register:i}=e,s=(0,n.useId)();return(0,n.useEffect)(()=>i(s),[]),!t&&r?[!1,()=>r&&r(s)]:[!0]}(),i=(0,n.useContext)(T.p);return n.createElement(nT,{...e,layoutGroup:i,switchLayoutGroup:(0,n.useContext)(w),isPresent:t,safeToRemove:r})}let nS={borderRadius:{...nP,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nP,borderTopRightRadius:nP,borderBottomLeftRadius:nP,borderBottomRightRadius:nP,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=tF.parse(e);if(n.length>5)return e;let i=tF.createTransformer(e),s="number"!=typeof n[0]?1:0,o=r.x.scale*t.x,a=r.y.scale*t.y;n[0+s]/=o,n[1+s]/=a;let l=tS(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),i(n)}}},nA=["TopLeft","TopRight","BottomLeft","BottomRight"],nx=nA.length,nC=e=>"string"==typeof e?parseFloat(e):e,nM=e=>"number"==typeof e||X.test(e);function nR(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nO=n_(0,.5,tu),nL=n_(.5,.95,eZ.Z);function n_(e,t,r){return n=>n<e?0:n>t?1:r(tW(e,t,n))}function nk(e,t){e.min=t.min,e.max=t.max}function nI(e,t){nk(e.x,t.x),nk(e.y,t.y)}function nD(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nN(e,t,[r,n,i],s,o){!function(e,t=0,r=1,n=.5,i,s=e,o=e){if($.test(t)&&(t=parseFloat(t),t=tS(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let a=tS(s.min,s.max,n);e===s&&(a-=t),e.min=nD(e.min,t,r,a,i),e.max=nD(e.max,t,r,a,i)}(e,t[r],t[n],t[i],t.scale,s,o)}let nB=["x","scaleX","originX"],nH=["y","scaleY","originY"];function nF(e,t,r,n){nN(e.x,t,nB,r?r.x:void 0,n?n.x:void 0),nN(e.y,t,nH,r?r.y:void 0,n?n.y:void 0)}function nj(e){return 0===e.translate&&1===e.scale}function nV(e){return nj(e.x)&&nj(e.y)}function nU(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function nG(e){return rW(e.x)/rW(e.y)}class nq{constructor(){this.members=[]}add(e){rv(this.members,e),e.scheduleRender()}remove(e){if(rb(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function nW(e,t,r){let n="",i=e.x.translate/t.x,s=e.y.translate/t.y;if((i||s)&&(n=`translate3d(${i}px, ${s}px, 0) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:e,rotateX:t,rotateY:i}=r;e&&(n+=`rotate(${e}deg) `),t&&(n+=`rotateX(${t}deg) `),i&&(n+=`rotateY(${i}deg) `)}let o=e.x.scale*t.x,a=e.y.scale*t.y;return(1!==o||1!==a)&&(n+=`scale(${o}, ${a})`),n||"none"}let nZ=(e,t)=>e.depth-t.depth;class nz{constructor(){this.children=[],this.isDirty=!1}add(e){rv(this.children,e),this.isDirty=!0}remove(e){rb(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nZ),this.isDirty=!1,this.children.forEach(e)}}let nK=["","X","Y","Z"],n$={visibility:"hidden"},nX=0,nY={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function nQ({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=null==t?void 0:t()){this.id=nX++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nY.totalNodes=nY.resolvedTargetDeltas=nY.recalculatedProjection=0,this.nodes.forEach(n1),this.nodes.forEach(n4),this.nodes.forEach(n7),this.nodes.forEach(n2),window.MotionDebug&&window.MotionDebug.record(nY)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nz)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new rE),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:i,visualElement:s}=this.options;if(s&&!s.current&&s.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=performance.now(),n=({timestamp:i})=>{let s=i-r;s>=t&&((0,ex.Pn)(n),e(s-t))};return ex.Wi.read(n,!0),()=>(0,ex.Pn)(n)}(n,250),nb.hasAnimatedSinceResize&&(nb.hasAnimatedSinceResize=!1,this.nodes.forEach(n5))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||s.getDefaultTransition()||io,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!nU(this.targetLayout,n)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...rf(i,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||n5(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,ex.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ie),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n8);return}this.isUpdating||this.nodes.forEach(n9),this.isUpdating=!1,this.nodes.forEach(n6),this.nodes.forEach(nJ),this.nodes.forEach(n0),this.clearAllSnapshots();let e=performance.now();ex.frameData.delta=H(0,1e3/60,e-ex.frameData.timestamp),ex.frameData.timestamp=e,ex.frameData.isProcessing=!0,ex.S6.update.process(ex.frameData),ex.S6.preRender.process(ex.frameData),ex.S6.render.process(ex.frameData),ex.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(n3),this.sharedNodes.forEach(it)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ex.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ex.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=r9(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:n(this.instance),offset:r(this.instance)})}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!nV(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;e&&(t||ne(this.latestValues)||s)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),iu((t=n).x),iu(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return r9();let t=e.measureViewportBox(),{scroll:r}=this.root;return r&&(no(t.x,r.offset.x),no(t.y,r.offset.y)),t}removeElementScroll(e){let t=r9();nI(t,e);for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:s}=n;if(n!==this.root&&i&&s.layoutScroll){if(i.isRoot){nI(t,e);let{scroll:r}=this.root;r&&(no(t.x,-r.offset.x),no(t.y,-r.offset.y))}no(t.x,i.offset.x),no(t.y,i.offset.y)}}return t}applyTransform(e,t=!1){let r=r9();nI(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&nh(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),ne(n.latestValues)&&nh(r,n.latestValues)}return ne(this.latestValues)&&nh(r,this.latestValues),r}removeTransform(e){let t=r9();nI(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!ne(r.latestValues))continue;r7(r.latestValues)&&r.updateSnapshot();let n=r9();nI(n,r.measurePageBox()),nF(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return ne(this.latestValues)&&nF(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ex.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,i;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(e||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=ex.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r9(),this.relativeTargetOrigin=r9(),rY(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=r9(),this.targetWithTransforms=r9()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,r$(r.x,n.x,i.x),r$(r.y,n.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nI(this.target,this.layout.layoutBox),ni(this.target,this.targetDelta)):nI(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r9(),this.relativeTargetOrigin=r9(),rY(this.relativeTargetOrigin,this.target,e.target),nI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nY.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||r7(this.parent.latestValues)||nt(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===ex.frameData.timestamp&&(n=!1),n)return;let{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||s))return;nI(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(e,t,r,n=!1){let i,s;let o=r.length;if(o){t.x=t.y=1;for(let a=0;a<o;a++){s=(i=r[a]).projectionDelta;let o=i.instance;(!o||!o.style||"contents"!==o.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nh(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,ni(e,s)),n&&ne(i.latestValues)&&nh(e,i.latestValues))}t.x=ns(t.x),t.y=ns(t.y)}})(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=r3(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r3(),this.projectionDeltaWithTransform=r3());let u=this.projectionTransform;rK(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=nW(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nY.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o=r3();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=r9(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(is));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(ir(o.x,e.x,n),ir(o.y,e.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p;rY(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,ii(f.x,p.x,a.x,n),ii(f.y,p.y,a.y,n),r&&(u=this.relativeTarget,d=r,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),r||(r=r9()),nI(r,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,r,n,i,s){i?(e.opacity=tS(0,void 0!==r.opacity?r.opacity:1,nO(n)),e.opacityExit=tS(void 0!==t.opacity?t.opacity:1,0,nL(n))):s&&(e.opacity=tS(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<nx;i++){let s=`border${nA[i]}Radius`,o=nR(t,s),a=nR(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nM(o)===nM(a)?(e[s]=Math.max(tS(nC(o),nC(a),n),0),($.test(a)||$.test(o))&&(e[s]+="%")):e[s]=a)}(t.rotate||r.rotate)&&(e.rotate=tS(t.rotate||0,r.rotate||0,n))}(s,i,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,ex.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ex.Wi.update(()=>{nb.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let n=L(e)?e:rS(e);return n.start(rm("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ih(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||r9();let t=rW(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rW(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nI(t,r),nh(t,i),rK(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nq),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(t=!0),!t)return;let n={};for(let t=0;t<nK.length;t++){let i="rotate"+nK[t];r[i]&&(n[i]=r[i],e.setStaticValue(i,0))}for(let t in e.render(),n)e.setStaticValue(t,n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return n$;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=eS(null==e?void 0:e.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eS(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!ne(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),n.transform=nW(this.projectionDeltaWithTransform,this.treeScale,o),i&&(n.transform=i(o,n.transform));let{x:a,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?n.opacity=s===this?null!==(r=null!==(t=o.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:n.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,C){if(void 0===o[e])continue;let{correct:t,applyTo:r}=C[e],i="none"===n.transform?o[e]:t(o[e],s);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=i}else n[e]=i}return this.options.layoutId&&(n.pointerEvents=s===this?eS(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(n8),this.root.sharedNodes.clear()}}}function nJ(e){e.updateLayout()}function n0(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:i}=e.options,s=r.source!==e.layout.source;"size"===i?r6(e=>{let n=s?r.measuredBox[e]:r.layoutBox[e],i=rW(n);n.min=t[e].min,n.max=n.min+i}):ih(i,r.layoutBox,t)&&r6(n=>{let i=s?r.measuredBox[n]:r.layoutBox[n],o=rW(t[n]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+o)});let o=r3();rK(o,t,r.layoutBox);let a=r3();s?rK(a,e.applyTransform(n,!0),r.measuredBox):rK(a,t,r.layoutBox);let l=!nV(o),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:s}=n;if(i&&s){let o=r9();rY(o,r.layoutBox,i.layoutBox);let a=r9();rY(a,t,s.layoutBox),nU(o,a)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=o,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n1(e){nY.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function n2(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function n3(e){e.clearSnapshot()}function n8(e){e.clearMeasurements()}function n9(e){e.isLayoutDirty=!1}function n6(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function n5(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function n4(e){e.resolveTargetDelta()}function n7(e){e.calcProjection()}function ie(e){e.resetRotation()}function it(e){e.removeLeadSnapshot()}function ir(e,t,r){e.translate=tS(t.translate,0,r),e.scale=tS(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function ii(e,t,r,n){e.min=tS(t.min,r.min,n),e.max=tS(t.max,r.max,n)}function is(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let io={duration:.45,ease:[.4,0,.1,1]},ia=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),il=ia("applewebkit/")&&!ia("chrome/")?Math.round:eZ.Z;function iu(e){e.min=il(e.min),e.max=il(e.max)}function ih(e,t,r){return"position"===e||"preserve-aspect"===e&&!rZ(nG(t),nG(r),.2)}let ic=nQ({attachResizeListener:(e,t)=>eR(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),id={current:void 0},ip=nQ({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!id.current){let e=new ic({});e.mount(window),e.setOptions({layoutScroll:!0}),id.current=e}return id.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),im=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function iy(e,t,r=1){(0,e3.k)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,i]=function(e){let t=im.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]}(e);if(!n)return;let s=window.getComputedStyle(t).getPropertyValue(n);if(s){let e=s.trim();return rg(e)?parseFloat(e):e}return N(i)?iy(i,t,r+1):i}let ig=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),iv=e=>ig.has(e),ib=e=>Object.keys(e).some(iv),iE=e=>e===F||e===X,iP=(e,t)=>parseFloat(e.split(", ")[t]),iT=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/);if(i)return iP(i[1],t);{let t=n.match(/^matrix\((.+)\)$/);return t?iP(t[1],e):0}},iw=new Set(["x","y","z"]),iS=M.filter(e=>!iw.has(e)),iA={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:iT(4,13),y:iT(5,14)};iA.translateX=iA.x,iA.translateY=iA.y;let ix=(e,t,r)=>{let n=t.measureViewportBox(),i=t.current,s=getComputedStyle(i),{display:o}=s,a={};"none"===o&&t.setStaticValue("display",e.display||"block"),r.forEach(e=>{a[e]=iA[e](n,s)}),t.render();let l=t.measureViewportBox();return r.forEach(r=>{let n=t.getValue(r);n&&n.jump(a[r]),e[r]=iA[r](l,s)}),e},iC=(e,t,r={},n={})=>{t={...t},n={...n};let i=Object.keys(t).filter(iv),s=[],o=!1,a=[];if(i.forEach(i=>{let l;let u=e.getValue(i);if(!e.hasValue(i))return;let h=r[i],c=rC(h),d=t[i];if(eP(d)){let e=d.length,t=null===d[0]?1:0;c=rC(h=d[t]);for(let r=t;r<e&&null!==d[r];r++)l?(0,e3.k)(rC(d[r])===l,"All keyframes must be of the same type"):(l=rC(d[r]),(0,e3.k)(l===c||iE(c)&&iE(l),"Keyframes must be of the same dimension as the current value"))}else l=rC(d);if(c!==l){if(iE(c)&&iE(l)){let e=u.get();"string"==typeof e&&u.set(parseFloat(e)),"string"==typeof d?t[i]=parseFloat(d):Array.isArray(d)&&l===X&&(t[i]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):t[i]=c.transform(d):(o||(s=function(e){let t=[];return iS.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),o=!0),a.push(i),n[i]=void 0!==n[i]?n[i]:t[i],u.jump(d))}}),!a.length)return{target:t,transitionEnd:n};{let r=a.indexOf("height")>=0?window.pageYOffset:null,i=ix(t,e,a);return s.length&&s.forEach(([t,r])=>{e.getValue(t).set(r)}),e.render(),P.j&&null!==r&&window.scrollTo({top:r}),{target:i,transitionEnd:n}}},iM=(e,t,r,n)=>{let i=function(e,{...t},r){let n=e.current;if(!(n instanceof Element))return{target:t,transitionEnd:r};for(let i in r&&(r={...r}),e.values.forEach(e=>{let t=e.get();if(!N(t))return;let r=iy(t,n);r&&e.set(r)}),t){let e=t[i];if(!N(e))continue;let s=iy(e,n);s&&(t[i]=s,r||(r={}),void 0===r[i]&&(r[i]=e))}return{target:t,transitionEnd:r}}(e,t,n);return function(e,t,r,n){return ib(t)?iC(e,t,r,n):{target:t,transitionEnd:n}}(e,t=i.target,r,n=i.transitionEnd)},iR={current:null},iO={current:!1},iL=new WeakMap,i_=Object.keys(E),ik=i_.length,iI=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],iD=m.length;class iN{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,visualState:i},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>ex.Wi.render(this.render,!1,!0);let{latestValues:o,renderState:a}=i;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=a,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=s,this.isControllingVariants=y(t),this.isVariantNode=g(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{});for(let e in u){let t=u[e];void 0!==o[e]&&L(t)&&(t.set(o[e],!1),ry(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,iL.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),iO.current||function(){if(iO.current=!0,P.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>iR.current=e.matches;e.addListener(t),t()}else iR.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||iR.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in iL.delete(this.current),this.projection&&this.projection.unmount(),(0,ex.Pn)(this.notifyUpdate),(0,ex.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=R.has(e),n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&ex.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),i()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},r,n,i){let s,o;for(let e=0;e<ik;e++){let r=i_[e],{isEnabled:n,Feature:i,ProjectionNode:a,MeasureLayout:l}=E[r];a&&(s=a),n(t)&&(!this.features[r]&&i&&(this.features[r]=new i(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:r,drag:n,dragConstraints:o,layoutScroll:a,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:r,alwaysMeasureLayout:!!n||o&&c(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:i,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):r9()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<iI.length;t++){let r=iI[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){let{willChange:n}=t;for(let i in t){let s=t[i],o=r[i];if(L(s))e.addValue(i,s),ry(n)&&n.add(i);else if(L(o))e.addValue(i,rS(s,{owner:e})),ry(n)&&n.remove(i);else if(o!==s){if(e.hasValue(i)){let t=e.getValue(i);t.hasAnimated||t.set(s)}else{let t=e.getStaticValue(i);e.addValue(i,rS(void 0!==t?t:s,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<iD;e++){let r=m[e],n=this.props[r];(d(n)||!1===n)&&(t[r]=n)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=rS(t,{owner:this}),this.addValue(e,r)),r}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,n="string"==typeof r||"object"==typeof r?null===(t=eb(this.props,r))||void 0===t?void 0:t[e]:void 0;if(r&&void 0!==n)return n;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||L(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new rE),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class iB extends iN{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},{transformValues:n},i){let s=function(e,t,r){let n={};for(let i in e){let e=function(e,t){if(t)return(t[e]||t.default||t).from}(i,t);if(void 0!==e)n[i]=e;else{let e=r.getValue(i);e&&(n[i]=e.get())}}return n}(r,e||{},this);if(n&&(t&&(t=n(t)),r&&(r=n(r)),s&&(s=n(s))),i){!function(e,t,r){var n,i;let s=Object.keys(t).filter(t=>!e.hasValue(t)),o=s.length;if(o)for(let a=0;a<o;a++){let o=s[a],l=t[o],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(i=null!==(n=r[o])&&void 0!==n?n:e.readValue(o))&&void 0!==i?i:t[o]),null!=u&&("string"==typeof u&&(rg(u)||rd(u))?u=parseFloat(u):!rR(u)&&tF.test(l)&&(u=rc(o,l)),e.addValue(o,rS(u,{owner:e})),void 0===r[o]&&(r[o]=u),null!==u&&e.setBaseTarget(o,u))}}(this,r,s);let e=iM(this,r,s,t);t=e.transitionEnd,r=e.target}return{transition:e,transitionEnd:t,...r}}}class iH extends iB{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(R.has(t)){let e=rh(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=(D(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nc(e,t)}build(e,t,r,n){er(e,t,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,t){return eg(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,r,n){ep(e,t,r,n)}}class iF extends iB{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(R.has(t)){let e=rh(t);return e&&e.default||0}return t=em.has(t)?t:u(t),e.getAttribute(t)}measureInstanceViewportBox(){return r9()}scrapeMotionValuesFromProps(e,t){return ev(e,t)}build(e,t,r,n){ec(e,t,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,r,n){ey(e,t,r,n)}mount(e){this.isSVGTag=ef(e.tagName),super.mount(e)}}let ij=(e,t)=>x(e)?new iF(t,{enableHardwareAcceleration:!1}):new iH(t,{enableHardwareAcceleration:!0}),iV={animation:{Feature:rN},exit:{Feature:rH},inView:{Feature:e0},tap:{Feature:eK},focus:{Feature:eq},hover:{Feature:eG},pan:{Feature:nv},drag:{Feature:ny,ProjectionNode:ip,MeasureLayout:nw},layout:{ProjectionNode:ip,MeasureLayout:nw}},iU=function(e){function t(t,r={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:u,Component:f}){e&&function(e){for(let t in e)E[t]={...E[t],...e[t]}}(e);let p=(0,n.forwardRef)(function(p,m){var g;let b;let E={...(0,n.useContext)(i),...p,layoutId:function({layoutId:e}){let t=(0,n.useContext)(T.p).id;return t&&void 0!==e?t+"-"+e:e}(p)},{isStatic:S}=E,A=function(e){let{initial:t,animate:r}=function(e,t){if(y(e)){let{initial:t,animate:r}=e;return{initial:!1===t||d(t)?t:void 0,animate:d(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,n.useContext)(s));return(0,n.useMemo)(()=>({initial:t,animate:r}),[v(t),v(r)])}(p),x=u(p,S);if(!S&&P.j){A.visualElement=function(e,t,r,u){let{visualElement:c}=(0,n.useContext)(s),d=(0,n.useContext)(l),f=(0,n.useContext)(o.O),p=(0,n.useContext)(i).reducedMotion,m=(0,n.useRef)();u=u||d.renderer,!m.current&&u&&(m.current=u(e,{visualState:t,parent:c,props:r,presenceContext:f,blockInitialAnimation:!!f&&!1===f.initial,reducedMotionConfig:p}));let y=m.current;(0,n.useInsertionEffect)(()=>{y&&y.update(r,f)});let g=(0,n.useRef)(!!(r[h]&&!window.HandoffComplete));return(0,a.L)(()=>{y&&(y.render(),g.current&&y.animationState&&y.animationState.animateChanges())}),(0,n.useEffect)(()=>{y&&(y.updateFeatures(),!g.current&&y.animationState&&y.animationState.animateChanges(),g.current&&(g.current=!1,window.HandoffComplete=!0))}),y}(f,x,E,t);let r=(0,n.useContext)(w),u=(0,n.useContext)(l).strict;A.visualElement&&(b=A.visualElement.loadFeatures(E,u,e,r))}return n.createElement(s.Provider,{value:A},b&&A.visualElement?n.createElement(b,{visualElement:A.visualElement,...E}):null,r(f,p,(g=A.visualElement,(0,n.useCallback)(e=>{e&&x.mount&&x.mount(e),g&&(e?g.mount(e):g.unmount()),m&&("function"==typeof m?m(e):c(m)&&(m.current=e))},[g])),x,S,A.visualElement))});return p[S]=f,p}(e(t,r))}if("undefined"==typeof Proxy)return t;let r=new Map;return new Proxy(t,{get:(e,n)=>(r.has(n)||r.set(n,t(n)),r.get(n))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},r,i){return{...x(e)?eC:eM,preloadedFeatures:r,useRender:function(e=!1){return(t,r,i,{latestValues:s},o)=>{let a=(x(t)?function(e,t,r,i){let s=(0,n.useMemo)(()=>{let r=ed();return ec(r,t,{enableHardwareAcceleration:!1},ef(i),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};ei(t,e.style,e),s.style={...t,...s.style}}return s}:function(e,t,r){let i={},s=function(e,t,r){let i=e.style||{},s={};return ei(s,i,e),Object.assign(s,function({transformTemplate:e},t,r){return(0,n.useMemo)(()=>{let n=en();return er(n,t,{enableHardwareAcceleration:!r},e),Object.assign({},n.vars,n.style)},[t])}(e,t,r)),e.transformValues?e.transformValues(s):s}(e,t,r);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=s,i})(r,s,o,t),l={...function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(ea(i)||!0===r&&eo(i)||!t&&!eo(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),...a,ref:i},{children:u}=r,h=(0,n.useMemo)(()=>L(u)?u.get():u,[u]);return(0,n.createElement)(t,{...l,children:h})}}(t),createVisualElement:i,Component:e}})(e,t,iV,ij))},87222:(e,t,r)=>{"use strict";r.d(t,{K:()=>i,k:()=>s});var n=r(30254);let i=n.Z,s=n.Z},79398:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});let n="undefined"!=typeof document},30254:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=e=>e},40207:(e,t,r)=>{"use strict";r.d(t,{h:()=>i});var n=r(3729);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},19038:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var n=r(3729);let i=r(79398).j?n.useLayoutEffect:n.useEffect},99558:(e,t,r)=>{"use strict";r.d(t,{f:()=>l});var n=r(3729),i=(e,t,r,n,i,s,o,a)=>{let l=document.documentElement,u=["light","dark"];function h(t){(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&s?i.map(e=>s[e]||e):i;r?(l.classList.remove(...n),l.classList.add(s&&s[t]?s[t]:t)):l.setAttribute(e,t)}),a&&u.includes(t)&&(l.style.colorScheme=t)}if(n)h(n);else try{let e=localStorage.getItem(t)||r,n=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;h(n)}catch(e){}},s=["light","dark"],o="(prefers-color-scheme: dark)",a=n.createContext(void 0),l=e=>n.useContext(a)?n.createElement(n.Fragment,null,e.children):n.createElement(h,{...e}),u=["light","dark"],h=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:r=!0,enableColorScheme:i=!0,storageKey:l="theme",themes:h=u,defaultTheme:m=r?"system":"light",attribute:y="data-theme",value:g,children:v,nonce:b,scriptProps:E})=>{let[P,T]=n.useState(()=>d(l,m)),[w,S]=n.useState(()=>"system"===P?p():P),A=g?Object.values(g):h,x=n.useCallback(e=>{let n=e;if(!n)return;"system"===e&&r&&(n=p());let o=g?g[n]:n,a=t?f(b):null,l=document.documentElement,u=e=>{"class"===e?(l.classList.remove(...A),o&&l.classList.add(o)):e.startsWith("data-")&&(o?l.setAttribute(e,o):l.removeAttribute(e))};if(Array.isArray(y)?y.forEach(u):u(y),i){let e=s.includes(m)?m:null,t=s.includes(n)?n:e;l.style.colorScheme=t}null==a||a()},[b]),C=n.useCallback(e=>{let t="function"==typeof e?e(P):e;T(t);try{localStorage.setItem(l,t)}catch(e){}},[P]),M=n.useCallback(t=>{S(p(t)),"system"===P&&r&&!e&&x("system")},[P,e]);n.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),n.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?T(e.newValue):C(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[C]),n.useEffect(()=>{x(null!=e?e:P)},[e,P]);let R=n.useMemo(()=>({theme:P,setTheme:C,forcedTheme:e,resolvedTheme:"system"===P?w:P,themes:r?[...h,"system"]:h,systemTheme:r?w:void 0}),[P,C,e,w,r,h]);return n.createElement(a.Provider,{value:R},n.createElement(c,{forcedTheme:e,storageKey:l,attribute:y,enableSystem:r,enableColorScheme:i,defaultTheme:m,value:g,themes:h,nonce:b,scriptProps:E}),v)},c=n.memo(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:s,enableColorScheme:o,defaultTheme:a,value:l,themes:u,nonce:h,scriptProps:c})=>{let d=JSON.stringify([r,t,a,e,u,l,s,o]).slice(1,-1);return n.createElement("script",{...c,suppressHydrationWarning:!0,nonce:h,dangerouslySetInnerHTML:{__html:`(${i.toString()})(${d})`}})}),d=(e,t)=>{},f=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},p=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")}};