exports.id=5843,exports.ids=[5843],exports.modules={65187:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},50340:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},63211:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},55794:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},25390:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},12704:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},2273:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},48120:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},98200:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},21096:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},13746:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},76755:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},17910:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},18822:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20783:(e,t,r)=>{e.exports=r(61476)},47370:(e,t,r)=>{"use strict";r.d(t,{fC:()=>w,z$:()=>b});var n=r(3729),a=r(98462),l=r(62409),o=r(95344),i="Progress",[s,d]=(0,a.b)(i),[u,c]=s(i),p=n.forwardRef((e,t)=>{let{__scopeProgress:r,value:n=null,max:a,getValueLabel:i=v,...s}=e;(a||0===a)&&m(a);let d=m(a)?a:100;null!==n&&g(n,d);let c=g(n,d)?n:null,p=x(c)?i(c,d):void 0;return(0,o.jsx)(u,{scope:r,value:c,max:d,children:(0,o.jsx)(l.WV.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":x(c)?c:void 0,"aria-valuetext":p,role:"progressbar","data-state":y(c,d),"data-value":c??void 0,"data-max":d,...s,ref:t})})});p.displayName=i;var f="ProgressIndicator",h=n.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,a=c(f,r);return(0,o.jsx)(l.WV.div,{"data-state":y(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...n,ref:t})});function v(e,t){return`${Math.round(e/t*100)}%`}function y(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function x(e){return"number"==typeof e}function m(e){return x(e)&&!isNaN(e)&&e>0}function g(e,t){return x(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=f;var w=p,b=h},32116:(e,t,r)=>{"use strict";r.d(t,{VY:()=>eZ,ZA:()=>eL,JO:()=>eN,ck:()=>eH,wU:()=>eB,eT:()=>e_,__:()=>eA,h_:()=>eE,fC:()=>eI,$G:()=>eF,u_:()=>ez,Z0:()=>eK,xz:()=>eD,B4:()=>eP,l_:()=>eW});var n=r(3729),a=r(81202);function l(e,[t,r]){return Math.min(r,Math.max(t,e))}var o=r(85222),i=r(77411),s=r(31405),d=r(98462),u=r(3975),c=r(44155),p=r(1106),f=r(27386),h=r(99048),v=r(37574),y=r(31179),x=r(62409),m=r(32751),g=r(2256),w=r(33183),b=r(16069),k=r(92062),C=r(87298),S=r(45904),M=r(37792),j=r(95344),T=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],V="Select",[I,D,P]=(0,i.B)(V),[N,E]=(0,d.b)(V,[P,v.D7]),Z=(0,v.D7)(),[W,L]=N(V),[A,H]=N(V),_=e=>{let{__scopeSelect:t,children:r,open:a,defaultOpen:l,onOpenChange:o,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:y,required:x,form:m}=e,g=Z(t),[b,k]=n.useState(null),[C,S]=n.useState(null),[M,T]=n.useState(!1),R=(0,u.gm)(c),[D,P]=(0,w.T)({prop:a,defaultProp:l??!1,onChange:o,caller:V}),[N,E]=(0,w.T)({prop:i,defaultProp:s,onChange:d,caller:V}),L=n.useRef(null),H=!b||m||!!b.closest("form"),[_,B]=n.useState(new Set),z=Array.from(_).map(e=>e.props.value).join(";");return(0,j.jsx)(v.fC,{...g,children:(0,j.jsxs)(W,{required:x,scope:t,trigger:b,onTriggerChange:k,valueNode:C,onValueNodeChange:S,valueNodeHasChildren:M,onValueNodeHasChildrenChange:T,contentId:(0,h.M)(),value:N,onValueChange:E,open:D,onOpenChange:P,dir:R,triggerPointerDownPosRef:L,disabled:y,children:[(0,j.jsx)(I.Provider,{scope:t,children:(0,j.jsx)(A,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{B(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{B(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),H?(0,j.jsxs)(ej,{"aria-hidden":!0,required:x,tabIndex:-1,name:p,autoComplete:f,value:N,onChange:e=>E(e.target.value),disabled:y,form:m,children:[void 0===N?(0,j.jsx)("option",{value:""}):null,Array.from(_)]},z):null]})})};_.displayName=V;var B="SelectTrigger",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:a=!1,...l}=e,i=Z(r),d=L(B,r),u=d.disabled||a,c=(0,s.e)(t,d.onTriggerChange),p=D(r),f=n.useRef("touch"),[h,y,m]=eR(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eV(t,e,r);void 0!==n&&d.onValueChange(n.value)}),g=e=>{u||(d.onOpenChange(!0),m()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,j.jsx)(v.ee,{asChild:!0,...i,children:(0,j.jsx)(x.WV.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eT(d.value)?"":void 0,...l,ref:c,onClick:(0,o.M)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:(0,o.M)(l.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,o.M)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(g(),e.preventDefault())})})})});z.displayName=B;var F="SelectValue",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:a,children:l,placeholder:o="",...i}=e,d=L(F,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==l,p=(0,s.e)(t,d.onValueNodeChange);return(0,b.b)(()=>{u(c)},[u,c]),(0,j.jsx)(x.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:eT(d.value)?(0,j.jsx)(j.Fragment,{children:o}):l})});K.displayName=F;var O=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...a}=e;return(0,j.jsx)(x.WV.span,{"aria-hidden":!0,...a,ref:t,children:n||"▼"})});O.displayName="SelectIcon";var U=e=>(0,j.jsx)(y.h,{asChild:!0,...e});U.displayName="SelectPortal";var q="SelectContent",$=n.forwardRef((e,t)=>{let r=L(q,e.__scopeSelect),[l,o]=n.useState();return((0,b.b)(()=>{o(new DocumentFragment)},[]),r.open)?(0,j.jsx)(J,{...e,ref:t}):l?a.createPortal((0,j.jsx)(Y,{scope:e.__scopeSelect,children:(0,j.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,j.jsx)("div",{children:e.children})})}),l):null});$.displayName=q;var[Y,X]=N(q),G=(0,m.Z8)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:a="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:y,arrowPadding:x,collisionBoundary:m,collisionPadding:g,sticky:w,hideWhenDetached:b,avoidCollisions:k,...C}=e,T=L(q,r),[R,V]=n.useState(null),[I,P]=n.useState(null),N=(0,s.e)(t,e=>V(e)),[E,Z]=n.useState(null),[W,A]=n.useState(null),H=D(r),[_,B]=n.useState(!1),z=n.useRef(!1);n.useEffect(()=>{if(R)return(0,S.Ry)(R)},[R]),(0,p.EW)();let F=n.useCallback(e=>{let[t,...r]=H().map(e=>e.ref.current),[n]=r.slice(-1),a=document.activeElement;for(let r of e)if(r===a||(r?.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),r?.focus(),document.activeElement!==a))return},[H,I]),K=n.useCallback(()=>F([E,R]),[F,E,R]);n.useEffect(()=>{_&&K()},[_,K]);let{onOpenChange:O,triggerPointerDownPosRef:U}=T;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(U.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||O(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,O,U]),n.useEffect(()=>{let e=()=>O(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[O]);let[$,X]=eR(e=>{let t=H().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eV(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!z.current&&!r;(void 0!==T.value&&T.value===t||n)&&(Z(e),n&&(z.current=!0))},[T.value]),et=n.useCallback(()=>R?.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!z.current&&!r;(void 0!==T.value&&T.value===t||n)&&A(e)},[T.value]),en="popper"===a?ee:Q,ea=en===ee?{side:u,sideOffset:h,align:v,alignOffset:y,arrowPadding:x,collisionBoundary:m,collisionPadding:g,sticky:w,hideWhenDetached:b,avoidCollisions:k}:{};return(0,j.jsx)(Y,{scope:r,content:R,viewport:I,onViewportChange:P,itemRefCallback:J,selectedItem:E,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:K,selectedItemText:W,position:a,isPositioned:_,searchRef:$,children:(0,j.jsx)(M.Z,{as:G,allowPinchZoom:!0,children:(0,j.jsx)(f.M,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,o.M)(l,e=>{T.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,j.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,j.jsx)(en,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...C,...ea,onPlaced:()=>B(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,o.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:a,...o}=e,i=L(q,r),d=X(q,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.e)(t,e=>f(e)),v=D(r),y=n.useRef(!1),m=n.useRef(!0),{viewport:g,selectedItem:w,selectedItemText:k,focusSelectedItem:C}=d,S=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&g&&w&&k){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=k.getBoundingClientRect();if("rtl"!==i.dir){let a=n.left-t.left,o=r.left-a,i=e.left-o,s=e.width+i,d=Math.max(s,t.width),c=l(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let a=t.right-n.right,o=window.innerWidth-r.right-a,i=window.innerWidth-e.right-o,s=e.width+i,d=Math.max(s,t.width),c=l(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let o=v(),s=window.innerHeight-20,d=g.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),x=parseInt(c.borderBottomWidth,10),m=f+h+d+parseInt(c.paddingBottom,10)+x,b=Math.min(5*w.offsetHeight,m),C=window.getComputedStyle(g),S=parseInt(C.paddingTop,10),M=parseInt(C.paddingBottom,10),j=e.top+e.height/2-10,T=w.offsetHeight/2,R=f+h+(w.offsetTop+T);if(R<=j){let e=o.length>0&&w===o[o.length-1].ref.current;u.style.bottom="0px";let t=p.clientHeight-g.offsetTop-g.offsetHeight;u.style.height=R+Math.max(s-j,T+(e?M:0)+t+x)+"px"}else{let e=o.length>0&&w===o[0].ref.current;u.style.top="0px";let t=Math.max(j,f+g.offsetTop+(e?S:0)+T);u.style.height=t+(m-R)+"px",g.scrollTop=R-j+g.offsetTop}u.style.margin="10px 0",u.style.minHeight=b+"px",u.style.maxHeight=s+"px",a?.(),requestAnimationFrame(()=>y.current=!0)}},[v,i.trigger,i.valueNode,u,p,g,w,k,i.dir,a]);(0,b.b)(()=>S(),[S]);let[M,T]=n.useState();(0,b.b)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===m.current&&(S(),C?.(),m.current=!1)},[S,C]);return(0,j.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:y,onScrollButtonChange:R,children:(0,j.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:M},children:(0,j.jsx)(x.WV.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:a=10,...l}=e,o=Z(r);return(0,j.jsx)(v.VY,{...o,...l,ref:t,align:n,collisionPadding:a,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(q,{}),en="SelectViewport",ea=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:a,...l}=e,i=X(en,r),d=er(en,r),u=(0,s.e)(t,i.onViewportChange),c=n.useRef(0);return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,j.jsx)(I.Slot,{scope:r,children:(0,j.jsx)(x.WV.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,o.M)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,a=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(a<n){let l=a+e,o=Math.min(n,l),i=l-o;r.style.height=o+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ea.displayName=en;var el="SelectGroup",[eo,ei]=N(el),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,a=(0,h.M)();return(0,j.jsx)(eo,{scope:r,id:a,children:(0,j.jsx)(x.WV.div,{role:"group","aria-labelledby":a,...n,ref:t})})});es.displayName=el;var ed="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,a=ei(ed,r);return(0,j.jsx)(x.WV.div,{id:a.id,...n,ref:t})});eu.displayName=ed;var ec="SelectItem",[ep,ef]=N(ec),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:a,disabled:l=!1,textValue:i,...d}=e,u=L(ec,r),c=X(ec,r),p=u.value===a,[f,v]=n.useState(i??""),[y,m]=n.useState(!1),g=(0,s.e)(t,e=>c.itemRefCallback?.(e,a,l)),w=(0,h.M)(),b=n.useRef("touch"),k=()=>{l||(u.onValueChange(a),u.onOpenChange(!1))};if(""===a)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,j.jsx)(ep,{scope:r,value:a,disabled:l,textId:w,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,j.jsx)(I.ItemSlot,{scope:r,value:a,disabled:l,textValue:f,children:(0,j.jsx)(x.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":y?"":void 0,"aria-selected":p&&y,"data-state":p?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...d,ref:g,onFocus:(0,o.M)(d.onFocus,()=>m(!0)),onBlur:(0,o.M)(d.onBlur,()=>m(!1)),onClick:(0,o.M)(d.onClick,()=>{"mouse"!==b.current&&k()}),onPointerUp:(0,o.M)(d.onPointerUp,()=>{"mouse"===b.current&&k()}),onPointerDown:(0,o.M)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,o.M)(d.onPointerMove,e=>{b.current=e.pointerType,l?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,o.M)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,o.M)(d.onKeyDown,e=>{c.searchRef?.current!==""&&" "===e.key||(R.includes(e.key)&&k()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ec;var ev="SelectItemText",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:o,...i}=e,d=L(ev,r),u=X(ev,r),c=ef(ev,r),p=H(ev,r),[f,h]=n.useState(null),v=(0,s.e)(t,e=>h(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),y=f?.textContent,m=n.useMemo(()=>(0,j.jsx)("option",{value:c.value,disabled:c.disabled,children:y},c.value),[c.disabled,c.value,y]),{onNativeOptionAdd:g,onNativeOptionRemove:w}=p;return(0,b.b)(()=>(g(m),()=>w(m)),[g,w,m]),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(x.WV.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?a.createPortal(i.children,d.valueNode):null]})});ey.displayName=ev;var ex="SelectItemIndicator",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ex,r).isSelected?(0,j.jsx)(x.WV.span,{"aria-hidden":!0,...n,ref:t}):null});em.displayName=ex;var eg="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=X(eg,e.__scopeSelect),a=er(eg,e.__scopeSelect),[l,o]=n.useState(!1),i=(0,s.e)(t,a.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){o(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,j.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eg;var eb="SelectScrollDownButton",ek=n.forwardRef((e,t)=>{let r=X(eb,e.__scopeSelect),a=er(eb,e.__scopeSelect),[l,o]=n.useState(!1),i=(0,s.e)(t,a.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,j.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ek.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:a,...l}=e,i=X("SelectScrollButton",r),s=n.useRef(null),d=D(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.b)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,j.jsx)(x.WV.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,o.M)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(a,50))}),onPointerMove:(0,o.M)(l.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(a,50))}),onPointerLeave:(0,o.M)(l.onPointerLeave,()=>{u()})})}),eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,j.jsx)(x.WV.div,{"aria-hidden":!0,...n,ref:t})});eS.displayName="SelectSeparator";var eM="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,a=Z(r),l=L(eM,r),o=X(eM,r);return l.open&&"popper"===o.position?(0,j.jsx)(v.Eh,{...a,...n,ref:t}):null}).displayName=eM;var ej=n.forwardRef(({__scopeSelect:e,value:t,...r},a)=>{let l=n.useRef(null),o=(0,s.e)(a,l),i=(0,k.D)(t);return n.useEffect(()=>{let e=l.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,j.jsx)(x.WV.select,{...r,style:{...C.C2,...r.style},ref:o,defaultValue:t})});function eT(e){return""===e||void 0===e}function eR(e){let t=(0,g.W)(e),r=n.useRef(""),a=n.useRef(0),l=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(a.current),""!==t&&(a.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),o=n.useCallback(()=>{r.current="",window.clearTimeout(a.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(a.current),[]),[r,l,o]}function eV(e,t,r){var n;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===a.length&&(l=l.filter(e=>e!==r));let o=l.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return o!==r?o:void 0}ej.displayName="SelectBubbleInput";var eI=_,eD=z,eP=K,eN=O,eE=U,eZ=$,eW=ea,eL=es,eA=eu,eH=eh,e_=ey,eB=em,ez=ew,eF=ek,eK=eS},89128:(e,t,r)=>{"use strict";r.d(t,{VY:()=>D,aV:()=>V,fC:()=>R,xz:()=>I});var n=r(3729),a=r(85222),l=r(98462),o=r(34504),i=r(43234),s=r(62409),d=r(3975),u=r(33183),c=r(99048),p=r(95344),f="Tabs",[h,v]=(0,l.b)(f,[o.Pc]),y=(0,o.Pc)(),[x,m]=h(f),g=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:l,orientation:o="horizontal",dir:i,activationMode:h="automatic",...v}=e,y=(0,d.gm)(i),[m,g]=(0,u.T)({prop:n,onChange:a,defaultProp:l??"",caller:f});return(0,p.jsx)(x,{scope:r,baseId:(0,c.M)(),value:m,onValueChange:g,orientation:o,dir:y,activationMode:h,children:(0,p.jsx)(s.WV.div,{dir:y,"data-orientation":o,...v,ref:t})})});g.displayName=f;var w="TabsList",b=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,l=m(w,r),i=y(r);return(0,p.jsx)(o.fC,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:n,children:(0,p.jsx)(s.WV.div,{role:"tablist","aria-orientation":l.orientation,...a,ref:t})})});b.displayName=w;var k="TabsTrigger",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:l=!1,...i}=e,d=m(k,r),u=y(r),c=j(d.baseId,n),f=T(d.baseId,n),h=n===d.value;return(0,p.jsx)(o.ck,{asChild:!0,...u,focusable:!l,active:h,children:(0,p.jsx)(s.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":f,"data-state":h?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:c,...i,ref:t,onMouseDown:(0,a.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,a.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||l||!e||d.onValueChange(n)})})})});C.displayName=k;var S="TabsContent",M=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:l,children:o,...d}=e,u=m(S,r),c=j(u.baseId,a),f=T(u.baseId,a),h=a===u.value,v=n.useRef(h);return n.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.z,{present:l||h,children:({present:r})=>(0,p.jsx)(s.WV.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&o})})});function j(e,t){return`${e}-trigger-${t}`}function T(e,t){return`${e}-content-${t}`}M.displayName=S;var R=g,V=b,I=C,D=M},92062:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var n=r(3729);function a(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};