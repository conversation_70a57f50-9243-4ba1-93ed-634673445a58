exports.id=9669,exports.ids=[9669],exports.modules={98491:(e,t,r)=>{var s={"./af.json":[97425,7425],"./de.json":[75387,5387],"./en.json":[27065,7065],"./es.json":[85175,5175],"./fr.json":[34352,4352],"./ja.json":[25450,5450],"./zh.json":[21616,1616],"./zu.json":[77891,7891]};function o(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],o=t[0];return r.e(t[1]).then(()=>r.t(o,19))}o.keys=()=>Object.keys(s),o.id=98491,e.exports=o},13660:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},20991:(e,t,r)=>{Promise.resolve().then(r.bind(r,53283)),Promise.resolve().then(r.bind(r,89351))},35303:()=>{},81596:(e,t,r)=>{"use strict";r.d(t,{SV:()=>f,ku:()=>m});var s=r(95344),o=r(3729),n=r.n(o),a=r(23673),i=r(5094),l=r(45961),d=r(33733),c=r(1960),u=r(79480);class f extends n().Component{constructor(e){super(e),this.handleReset=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.copyErrorToClipboard=()=>{let{error:e,errorInfo:t}=this.state,r=`
Error: ${e?.message}
Stack: ${e?.stack}
Component Stack: ${t?.componentStack}
    `.trim();navigator.clipboard.writeText(r).then(()=>{alert("Error details copied to clipboard")})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t}),this.props.onError?.(e,t)}render(){if(this.state.hasError){let{error:e,errorInfo:t}=this.state;if(this.props.fallback){let t=this.props.fallback;return s.jsx(t,{error:e,reset:this.handleReset})}return s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsxs)(a.Zb,{className:"max-w-2xl w-full",children:[(0,s.jsxs)(a.Ol,{children:[(0,s.jsxs)(a.ll,{className:"flex items-center space-x-2 text-red-600",children:[s.jsx(l.Z,{className:"h-6 w-6"}),s.jsx("span",{children:"Something went wrong"})]}),s.jsx(a.SZ,{children:"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists."})]}),(0,s.jsxs)(a.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[s.jsx("h3",{className:"font-medium text-red-900 mb-2",children:"Error Details"}),s.jsx("p",{className:"text-sm text-red-800 font-mono",children:e?.message||"Unknown error"})]}),!1,(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(i.z,{onClick:this.handleReset,className:"flex items-center space-x-2",children:[s.jsx(d.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"Try Again"})]}),(0,s.jsxs)(i.z,{variant:"outline",onClick:this.copyErrorToClipboard,className:"flex items-center space-x-2",children:[s.jsx(c.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"Copy Error"})]}),(0,s.jsxs)(i.z,{variant:"outline",onClick:()=>window.location.reload(),className:"flex items-center space-x-2",children:[s.jsx(d.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"Reload Page"})]})]}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[s.jsx(u.Z,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-blue-900",children:"Need Help?"}),s.jsx("p",{className:"text-sm text-blue-800 mt-1",children:"If this error persists, please copy the error details and report it to our support team."})]})]})})]})]})})}return this.props.children}}function m({error:e,reset:t}){return(0,s.jsxs)("div",{className:"p-6 bg-red-50 border border-red-200 rounded-lg",children:[s.jsx("h2",{className:"text-lg font-medium text-red-900 mb-2",children:"Something went wrong"}),s.jsx("p",{className:"text-sm text-red-700 mb-4",children:e.message}),s.jsx(i.z,{onClick:t,size:"sm",children:"Try again"})]})}},89351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Providers:()=>M});var s=r(95344),o=r(19115),n=r(26274),a=r(55849),i=r(99558),l=r(3729),d=r(57916),c=r(73644),u=r(7060),f=r(66138),m=r(45961),p=r(91991),x=r(14513),h=r(38067);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let g=({type:e})=>{let t="h-5 w-5";switch(e){case"success":return s.jsx(u.Z,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(t,"text-green-500")});case"error":return s.jsx(f.Z,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(t,"text-red-500")});case"warning":return s.jsx(m.Z,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(t,"text-yellow-500")});default:return s.jsx(p.Z,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(t,"text-blue-500")})}},b=({notification:e})=>{let{removeNotification:t}=(0,h.G)(),r=()=>{t(e.id)},o={success:"bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800",error:"bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800",warning:"bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800",info:"bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800"}[e.type],n={success:"text-green-800 dark:text-green-200",error:"text-red-800 dark:text-red-200",warning:"text-yellow-800 dark:text-yellow-200",info:"text-blue-800 dark:text-blue-200"}[e.type];return s.jsx(d.E.div,{initial:{opacity:0,y:50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,scale:.5,transition:{duration:.2}},className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border",o),children:s.jsx("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(g,{type:e.type})}),(0,s.jsxs)("div",{className:"ml-3 w-0 flex-1 pt-0.5",children:[s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm font-medium",n),children:e.title}),e.message&&s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("mt-1 text-sm",n,"opacity-90"),children:e.message}),e.actions&&e.actions.length>0&&s.jsx("div",{className:"mt-3 flex space-x-2",children:e.actions.map((e,t)=>s.jsx("button",{onClick:()=>{e.onClick(),r()},className:"text-sm font-medium text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded",children:e.label},t))})]}),s.jsx("div",{className:"ml-4 flex-shrink-0 flex",children:s.jsx("button",{onClick:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-md inline-flex hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",n),"aria-label":"Dismiss notification",children:s.jsx(x.Z,{className:"h-5 w-5"})})})]})})})},v=()=>{let{notifications:e}=(0,h.G)();return s.jsx("div",{"aria-live":"assertive",className:"fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50",children:s.jsx("div",{className:"w-full flex flex-col items-center space-y-4 sm:items-end",children:s.jsx(c.M,{children:e.map(e=>s.jsx(b,{notification:e},e.id))})})})};var N=r(33390),O=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let j=N.zt,w=l.forwardRef(({className:e,...t},r)=>s.jsx(N.l_,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));w.displayName=N.l_.displayName;let y=(0,O.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),E=l.forwardRef(({className:e,variant:t,...r},o)=>s.jsx(N.fC,{ref:o,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y({variant:t}),e),...r}));E.displayName=N.fC.displayName,l.forwardRef(({className:e,...t},r)=>s.jsx(N.aU,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=N.aU.displayName;let _=l.forwardRef(({className:e,...t},r)=>s.jsx(N.x8,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(x.Z,{className:"h-4 w-4"})}));_.displayName=N.x8.displayName;let D=l.forwardRef(({className:e,...t},r)=>s.jsx(N.Dx,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm font-semibold",e),...t}));D.displayName=N.Dx.displayName;let U=l.forwardRef(({className:e,...t},r)=>s.jsx(N.dk,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm opacity-90",e),...t}));U.displayName=N.dk.displayName;var T=r(60339);function C(){let{toasts:e}=(0,T.pm)();return(0,s.jsxs)(j,{children:[e.map(function({id:e,title:t,description:r,action:o,...n}){return(0,s.jsxs)(E,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(D,{children:t}),r&&s.jsx(U,{children:r})]}),o,s.jsx(_,{})]},e)}),s.jsx(w,{})]})}var S=r(81596);function k(){let[e,t]=(0,l.useState)(!1),[r,s]=(0,l.useState)(0);return(0,l.useEffect)(()=>{let e=()=>{let e=Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).getErrors(),t=Object(function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()).getErrorsFromStorage();s(e.length+t.length)};e();let t=setInterval(e,1e3);return()=>clearInterval(t)},[]),null}function M({children:e}){let[t]=(0,l.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<3},mutations:{retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<2}}}));return s.jsx(S.SV,{children:(0,s.jsxs)(n.aH,{client:t,children:[(0,s.jsxs)(i.f,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,children:[s.jsx("div",{className:"relative flex min-h-screen flex-col",children:s.jsx("div",{className:"flex-1",children:e})}),s.jsx(v,{}),s.jsx(C,{}),s.jsx(k,{})]}),s.jsx(a.t,{initialIsOpen:!1})]})})}r(5094),r(19591),r(73229),r(79480),r(33733),r(96885),r(38271),function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/error-logger'");throw e.code="MODULE_NOT_FOUND",e}()},19591:(e,t,r)=>{"use strict";r.d(t,{C:()=>a});var s=r(95344);r(3729);var o=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function a({className:e,variant:t,...r}){return s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(n({variant:t}),e),...r})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(95344),o=r(3729),n=r(32751),a=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(42739);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,loading:a=!1,leftIcon:d,rightIcon:c,children:u,disabled:f,...m},p)=>{let x=o?n.g7:"button",h=f||a;return(0,s.jsxs)(x,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l({variant:t,size:r,className:e})),ref:p,disabled:h,"aria-disabled":h,...m,children:[a&&s.jsx(i.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!a&&d&&s.jsx("span",{className:"mr-2",children:d}),u,!a&&c&&s.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>a,SZ:()=>l,Zb:()=>n,aY:()=>d,ll:()=>i});var s=r(95344),o=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=o.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let a=o.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...t}));a.displayName="CardHeader";let i=o.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...t}));i.displayName="CardTitle";let l=o.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let d=o.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...t}));d.displayName="CardContent",o.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},60339:(e,t,r)=>{"use strict";r.d(t,{pm:()=>f});var s=r(3729);let o=0,n=new Map,a=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},38067:(e,t,r)=>{"use strict";r.d(t,{G:()=>s});let s=(0,r(43158).Ue)((e,t)=>({notifications:[],addNotification:r=>{let s=Math.random().toString(36).substring(2)+Date.now().toString(36),o={id:s,duration:5e3,...r};e(e=>({notifications:[...e.notifications,o]})),o.duration&&o.duration>0&&setTimeout(()=>{t().removeNotification(s)},o.duration)},removeNotification:t=>{e(e=>({notifications:e.notifications.filter(e=>e.id!==t)}))},clearAllNotifications:()=>{e({notifications:[]})}}))},21839:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,generateStaticParams:()=>x,metadata:()=>g});var s=r(25036),o=r(29155),n=r.n(o),a=r(9482),i=r(95399),l=r(88726),d=r(44759),c=r(86843);let u=(0,c.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/providers.tsx`),{__esModule:f,$$typeof:m}=u;u.default;let p=(0,c.createProxy)(String.raw`/mnt/persist/workspace/frontend/src/components/providers.tsx#Providers`);function x(){return d.k1.map(e=>({locale:e}))}async function h({children:e,params:{locale:t}}){d.k1.includes(t)||(0,l.notFound)();let r=await (0,i.Z)();return s.jsx("html",{lang:t,suppressHydrationWarning:!0,children:s.jsx("body",{className:`${n().className} bg-background text-foreground`,children:s.jsx(a.Z,{messages:r,children:s.jsx(p,{children:e})})})})}r(5023);let g={title:{template:"%s | 8,760 Hours",default:"8,760 Hours - Systematic Life Planning"},description:"Transform your life with systematic annual planning using the 8,760 Hours methodology.",keywords:["life planning","goal setting","personal development","productivity","life areas"],authors:[{name:"8,760 Hours Team"}],creator:"8,760 Hours",publisher:"8,760 Hours",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"),alternates:{canonical:"/",languages:{"en-US":"/en","es-ES":"/es","fr-FR":"/fr","de-DE":"/de","ja-JP":"/ja","zh-CN":"/zh","af-ZA":"/af","zu-ZA":"/zu"}},openGraph:{title:"8,760 Hours - Systematic Life Planning",description:"Transform your life with systematic annual planning using the 8,760 Hours methodology.",url:"/",siteName:"8,760 Hours",locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"8,760 Hours - Systematic Life Planning",description:"Transform your life with systematic annual planning using the 8,760 Hours methodology.",creator:"@8760hours"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:process.env.GOOGLE_SITE_VERIFICATION}}},21342:(e,t,r)=>{"use strict";function s({children:e}){return e}r.r(t),r.d(t,{default:()=>s})},44759:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>a,k1:()=>n});var s=r(88726),o=r(17231);let n=["en","es","fr","de","ja","zh","af","zu"],a=(0,o.Z)(async({locale:e})=>(n.includes(e)||(0,s.notFound)(),{locale:e,messages:(await r(98491)(`./${e}.json`)).default}))},5023:()=>{}};