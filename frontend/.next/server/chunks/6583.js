"use strict";exports.id=6583,exports.ids=[6583],exports.modules={45904:(e,t,n)=>{n.d(t,{Ry:()=>l});var r=new WeakMap,o=new WeakMap,a={},c=0,u=function(e){return e&&(e.host||u(e.parentNode))},i=function(e,t,n,i){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:null}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,v=new Set(l),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};l.forEach(m);var p=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))p(e);else try{var t=e.getAttribute(i),a=null!==t&&"false"!==t,c=(r.get(e)||0)+1,u=(s.get(e)||0)+1;r.set(e,c),s.set(e,u),d.push(e),1===c&&a&&o.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(i,"true")}catch(e){}})};return p(t),f.clear(),c++,function(){d.forEach(function(e){var t=r.get(e)-1,a=s.get(e)-1;r.set(e,t),s.set(e,a),t||(o.has(e)||e.removeAttribute(i),o.delete(e)),a||e.removeAttribute(n)}),--c||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),a=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),i(o,a,n,"aria-hidden")):function(){return null}}},37792:(e,t,n)=>{n.d(t,{Z:()=>B});var r,o=n(62824),a=n(3729),c="right-scroll-bar-position",u="width-before-scroll-bar";function i(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var l=a.useEffect,s=new WeakMap;function d(e){return e}var f=function(e){void 0===e&&(e={});var t,n,r,a=(void 0===t&&(t=d),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},c=function(){return Promise.resolve().then(a)};c(),n={push:function(e){t.push(e),c()},filter:function(e){return t=t.filter(e),n}}}});return a.options=(0,o.pi)({async:!0,ssr:!1},e),a}(),v=function(){},m=a.forwardRef(function(e,t){var n,r,c,u,d=a.useRef(null),m=a.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),p=m[0],h=m[1],g=e.forwardProps,b=e.children,y=e.className,E=e.removeScrollBar,w=e.enabled,S=e.shards,A=e.sideCar,C=e.noRelative,M=e.noIsolation,R=e.inert,T=e.allowPinchZoom,x=e.as,k=e.gapMode,N=(0,o._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),F=(n=[d,t],r=function(e){return n.forEach(function(t){return i(t,e)})},(c=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return c.value},set current(value){var e=c.value;e!==value&&(c.value=value,c.callback(value,e))}}}})[0]).callback=r,u=c.facade,l(function(){var e=s.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||i(e,null)}),r.forEach(function(e){t.has(e)||i(e,o)})}s.set(u,n)},[n]),u),L=(0,o.pi)((0,o.pi)({},N),p);return a.createElement(a.Fragment,null,w&&a.createElement(A,{sideCar:f,removeScrollBar:E,shards:S,noRelative:C,noIsolation:M,inert:R,setCallbacks:h,allowPinchZoom:!!T,lockRef:d,gapMode:k}),g?a.cloneElement(a.Children.only(b),(0,o.pi)((0,o.pi)({},L),{ref:F})):a.createElement(void 0===x?"div":x,(0,o.pi)({},L,{className:y,ref:F}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:u,zeroRight:c};var p=function(e){var t=e.sideCar,n=(0,o._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,(0,o.pi)({},n))};p.isSideCarExport=!0;var h=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,c;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),c=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(c)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},g=function(){var e=h();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=g();return function(t){return e(t.styles,t.dynamic),null}},y={left:0,top:0,right:0,gap:0},E=b(),w="data-scroll-locked",S=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(w,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(w,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(w)||"0",10);return isFinite(e)?e:0},C=function(){a.useEffect(function(){return document.body.setAttribute(w,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(w):document.body.setAttribute(w,e.toString())}},[])},M=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;C();var c=a.useMemo(function(){return y},[o]);return a.createElement(E,{styles:S(c,!t,o,n?"":"!important")})},R=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},T=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),x(e,r)){var o=k(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},x=function(e,t){return"v"===e?R(t,"overflowY"):R(t,"overflowX")},k=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},N=function(e,t,n,r,o){var a,c=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=c*r,i=n.target,l=t.contains(i),s=!1,d=u>0,f=0,v=0;do{if(!i)break;var m=k(e,i),p=m[0],h=m[1]-m[2]-c*p;(p||h)&&x(e,i)&&(f+=h,v+=p);var g=i.parentNode;i=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!l&&i!==document.body||l&&(t.contains(i)||t===i));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},L=function(e){return[e.deltaX,e.deltaY]},I=function(e){return e&&"current"in e?e.current:e},P=0,D=[];let W=(f.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),c=a.useState(P++)[0],u=a.useState(b)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(c));var t=(0,o.ev)([e.lockRef.current],(e.shards||[]).map(I),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(c))})}}},[e.inert,e.lockRef.current,e.shards]);var l=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=F(e),c=n.current,u="deltaX"in e?e.deltaX:c[0]-a[0],l="deltaY"in e?e.deltaY:c[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=T(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=T(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var v=r.current||o;return N(v,t,e,"h"===v?u:l,!0)},[]),s=a.useCallback(function(e){if(D.length&&D[D.length-1]===u){var n="deltaY"in e?L(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(I).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),v=a.useCallback(function(t){d(t.type,L(t),t.target,l(t,e.lockRef.current))},[]),m=a.useCallback(function(t){d(t.type,F(t),t.target,l(t,e.lockRef.current))},[]);a.useEffect(function(){return D.push(u),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:m}),document.addEventListener("wheel",s,!1),document.addEventListener("touchmove",s,!1),document.addEventListener("touchstart",f,!1),function(){D=D.filter(function(e){return e!==u}),document.removeEventListener("wheel",s,!1),document.removeEventListener("touchmove",s,!1),document.removeEventListener("touchstart",f,!1)}},[]);var p=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(u,{styles:"\n  .block-interactivity-".concat(c," {pointer-events: none;}\n  .allow-interactivity-").concat(c," {pointer-events: all;}\n")}):null,p?a.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),p);var K=a.forwardRef(function(e,t){return a.createElement(m,(0,o.pi)({},e,{ref:t,sideCar:W}))});K.classNames=m.classNames;let B=K},3975:(e,t,n)=>{n.d(t,{gm:()=>a});var r=n(3729);n(95344);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},1106:(e,t,n)=>{n.d(t,{EW:()=>a});var r=n(3729),o=0;function a(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??c()),document.body.insertAdjacentElement("beforeend",e[1]??c()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function c(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},27386:(e,t,n)=>{n.d(t,{M:()=>d});var r=n(3729),o=n(31405),a=n(62409),c=n(2256),u=n(95344),i="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:h,onUnmountAutoFocus:g,...b}=e,[y,E]=r.useState(null),w=(0,c.W)(h),S=(0,c.W)(g),A=r.useRef(null),C=(0,o.e)(t,e=>E(e)),M=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(M.paused||!y)return;let t=e.target;y.contains(t)?A.current=t:m(A.current,{select:!0})},t=function(e){if(M.paused||!y)return;let t=e.relatedTarget;null===t||y.contains(t)||m(A.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(y)});return y&&n.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,y,M.paused]),r.useEffect(()=>{if(y){p.add(M);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(i,s);y.addEventListener(i,w),y.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(y))}return()=>{y.removeEventListener(i,w),setTimeout(()=>{let t=new CustomEvent(l,s);y.addEventListener(l,S),y.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),y.removeEventListener(l,S),p.remove(M)},0)}}},[y,w,S,M]);let R=r.useCallback(e=>{if(!n&&!d||M.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[v(t,e),v(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(a,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,M.paused]);return(0,u.jsx)(a.WV.div,{tabIndex:-1,...b,ref:C,onKeyDown:R})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var p=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=h(e,t)).unshift(t)},remove(t){e=h(e,t),e[0]?.resume()}}}();function h(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},99048:(e,t,n)=>{n.d(t,{M:()=>i});var r,o=n(3729),a=n(16069),c=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function i(e){let[t,n]=o.useState(c());return(0,a.b)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},34504:(e,t,n)=>{n.d(t,{Pc:()=>w,ck:()=>F,fC:()=>N});var r=n(3729),o=n(85222),a=n(77411),c=n(31405),u=n(98462),i=n(99048),l=n(62409),s=n(2256),d=n(33183),f=n(3975),v=n(95344),m="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,b,y]=(0,a.B)(h),[E,w]=(0,u.b)(h,[y]),[S,A]=E(h),C=r.forwardRef((e,t)=>(0,v.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(M,{...e,ref:t})})}));C.displayName=h;var M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:E,onEntryFocus:w,preventScrollOnEntryFocus:A=!1,...C}=e,M=r.useRef(null),R=(0,c.e)(t,M),T=(0,f.gm)(i),[x,N]=(0,d.T)({prop:g,defaultProp:y??null,onChange:E,caller:h}),[F,L]=r.useState(!1),I=(0,s.W)(w),P=b(n),D=r.useRef(!1),[W,K]=r.useState(0);return r.useEffect(()=>{let e=M.current;if(e)return e.addEventListener(m,I),()=>e.removeEventListener(m,I)},[I]),(0,v.jsx)(S,{scope:n,orientation:a,dir:T,loop:u,currentTabStopId:x,onItemFocus:r.useCallback(e=>N(e),[N]),onItemShiftTab:r.useCallback(()=>L(!0),[]),onFocusableItemAdd:r.useCallback(()=>K(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>K(e=>e-1),[]),children:(0,v.jsx)(l.WV.div,{tabIndex:F||0===W?-1:0,"data-orientation":a,...C,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!F){let t=new CustomEvent(m,p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),A)}}D.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>L(!1))})})}),R="RovingFocusGroupItem",T=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:c=!1,tabStopId:u,children:s,...d}=e,f=(0,i.M)(),m=u||f,p=A(R,n),h=p.currentTabStopId===m,y=b(n),{onFocusableItemAdd:E,onFocusableItemRemove:w,currentTabStopId:S}=p;return r.useEffect(()=>{if(a)return E(),()=>w()},[a,E,w]),(0,v.jsx)(g.ItemSlot,{scope:n,id:m,focusable:a,active:c,children:(0,v.jsx)(l.WV.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?p.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>p.onItemFocus(m)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return x[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>k(n))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=S}):s})})});T.displayName=R;var x={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var N=C,F=T}};