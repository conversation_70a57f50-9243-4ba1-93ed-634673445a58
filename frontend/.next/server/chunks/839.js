exports.id=839,exports.ids=[839],exports.modules={13660:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},35303:()=>{},66138:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7060:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},35341:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},51838:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},17910:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},14513:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54243:(e,t,r)=>{"use strict";r.d(t,{Y:()=>N});var a=r(95344),i=r(3729),s=r(60708),l=r(85453),n=r(3389),o=r(23673),d=r(5094),c=r(46540),u=r(33668),m=r(2690),f=r(19591);(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var h=r(17910),p=r(7060),v=r(35341),x=r(14513),g=r(51838);let b=n.z.object({title:n.z.string().min(1,"Goal title is required"),description:n.z.string().min(10,"Please provide a detailed description"),category:n.z.enum(["major","supporting","habit","experience"]),priority:n.z.enum(["high","medium","low"]),specific:n.z.string().min(1,"Specific description is required"),measurable:n.z.string().min(1,"Measurable criteria is required"),achievable:n.z.string().min(1,"Achievable plan is required"),relevant:n.z.string().min(1,"Relevance explanation is required"),timeBound:n.z.string().min(1,"Time-bound deadline is required"),lifeAreas:n.z.array(n.z.string()).min(1,"Select at least one life area"),targetDate:n.z.string().optional(),tags:n.z.array(n.z.string()).optional(),notes:n.z.string().optional()});function N({goal:e,template:t,onSave:r,onCancel:n,className:N}){let[y,j]=(0,i.useState)(e?.lifeAreas||t?.lifeAreas||[]),[O,w]=(0,i.useState)(e?.tags||t?.tags||[]),[D,_]=(0,i.useState)(""),{register:E,handleSubmit:T,watch:U,setValue:C,formState:{errors:M,isValid:L}}=(0,s.cI)({resolver:(0,l.F)(b),defaultValues:{title:e?.title||t?.title||"",description:e?.description||t?.description||"",category:e?.category||t?.category||"major",priority:e?.priority||"medium",specific:e?.specific||t?.smartTemplate?.specific||"",measurable:e?.measurable||t?.smartTemplate?.measurable||"",achievable:e?.achievable||t?.smartTemplate?.achievable||"",relevant:e?.relevant||t?.smartTemplate?.relevant||"",timeBound:e?.timeBound||t?.smartTemplate?.timeBound||"",lifeAreas:y,targetDate:e?.targetDate?e.targetDate.toISOString().split("T")[0]:"",tags:O,notes:e?.notes||""}}),k=e=>{let t=y.includes(e)?y.filter(t=>t!==e):[...y,e];j(t),C("lifeAreas",t)},F=()=>{if(D.trim()&&!O.includes(D.trim())){let e=[...O,D.trim()];w(e),C("tags",e),_("")}},S=e=>{let t=O.filter(t=>t!==e);w(t),C("tags",t)};return(0,a.jsxs)(o.Zb,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("max-w-4xl mx-auto",N),children:[(0,a.jsxs)(o.Ol,{children:[(0,a.jsxs)(o.ll,{className:"flex items-center",children:[a.jsx(h.Z,{className:"mr-2 h-5 w-5"}),e?.id?"Edit Goal":"Create New Goal"]}),a.jsx(o.SZ,{children:t?`Using template: ${t.title}`:"Define a SMART goal to achieve this year"})]}),a.jsx(o.aY,{children:(0,a.jsxs)("form",{onSubmit:T(t=>{let a={...e,...t,lifeAreas:y,tags:O,targetDate:t.targetDate?new Date(t.targetDate):void 0,updatedAt:new Date};e?.id||(a.id=`goal-${Date.now()}`,a.createdAt=new Date,a.status="not_started",a.progress=0,a.actionItems=[],a.milestones=[]),r(a)}),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"title",children:"Goal Title *"}),a.jsx(c.I,{id:"title",placeholder:"Enter a clear, concise goal title",...E("title"),"aria-invalid":M.title?"true":"false"}),M.title&&a.jsx("p",{className:"text-sm text-destructive",children:M.title.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"targetDate",children:"Target Completion Date"}),a.jsx(c.I,{id:"targetDate",type:"date",...E("targetDate")})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"description",children:"Goal Description *"}),a.jsx(m.g,{id:"description",placeholder:"Describe your goal in detail. What exactly do you want to achieve and why is it important to you?",rows:3,...E("description"),"aria-invalid":M.description?"true":"false"}),M.description&&a.jsx("p",{className:"text-sm text-destructive",children:M.description.message})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(u._,{children:"Goal Category *"}),a.jsx("div",{className:"space-y-2",children:[{value:"major",label:"Major Goal",description:"Significant life-changing goal"},{value:"supporting",label:"Supporting Goal",description:"Supports other major goals"},{value:"habit",label:"Habit Goal",description:"Building consistent habits"},{value:"experience",label:"Experience Goal",description:"New experiences and adventures"}].map(e=>(0,a.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer",children:[a.jsx("input",{type:"radio",value:e.value,...E("category"),className:"mt-1"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.label}),a.jsx("div",{className:"text-sm text-muted-foreground",children:e.description})]})]},e.value))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(u._,{children:"Priority Level *"}),a.jsx("div",{className:"space-y-2",children:[{value:"high",label:"High Priority",color:"bg-red-500"},{value:"medium",label:"Medium Priority",color:"bg-yellow-500"},{value:"low",label:"Low Priority",color:"bg-green-500"}].map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[a.jsx("input",{type:"radio",value:e.value,...E("priority")}),a.jsx("div",{className:`w-3 h-3 rounded-full ${e.color}`}),a.jsx("span",{className:"font-medium",children:e.label})]},e.value))})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(u._,{children:"Related Life Areas *"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Select the life areas this goal will impact"}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:Object(function(){var e=Error("Cannot find module '@/lib/life-areas'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>{let t=e.icon,r=y.includes(e.id);return(0,a.jsxs)("button",{type:"button",onClick:()=>k(e.id),className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center space-x-2 p-2 rounded-lg border text-left transition-all",r?"border-primary bg-primary/10 text-primary":"border-muted hover:border-primary/50"),children:[a.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-1 rounded",e.bgColor),children:a.jsx(t,{className:"h-3 w-3 text-white"})}),a.jsx("span",{className:"text-sm font-medium",children:e.shortName}),r&&a.jsx(p.Z,{className:"h-4 w-4 ml-auto"})]},e.id)})}),M.lifeAreas&&a.jsx("p",{className:"text-sm text-destructive",children:M.lifeAreas.message})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(v.Z,{className:"h-5 w-5 text-primary"}),a.jsx("h3",{className:"text-lg font-semibold",children:"SMART Goal Criteria"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"specific",children:"Specific *"}),a.jsx(m.g,{id:"specific",placeholder:"What exactly will you accomplish? Be clear and detailed.",rows:2,...E("specific"),"aria-invalid":M.specific?"true":"false"}),M.specific&&a.jsx("p",{className:"text-sm text-destructive",children:M.specific.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"measurable",children:"Measurable *"}),a.jsx(m.g,{id:"measurable",placeholder:"How will you measure progress and success? Include numbers, metrics, or milestones.",rows:2,...E("measurable"),"aria-invalid":M.measurable?"true":"false"}),M.measurable&&a.jsx("p",{className:"text-sm text-destructive",children:M.measurable.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"achievable",children:"Achievable *"}),a.jsx(m.g,{id:"achievable",placeholder:"How is this goal realistic and attainable? What resources do you have?",rows:2,...E("achievable"),"aria-invalid":M.achievable?"true":"false"}),M.achievable&&a.jsx("p",{className:"text-sm text-destructive",children:M.achievable.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"relevant",children:"Relevant *"}),a.jsx(m.g,{id:"relevant",placeholder:"Why is this goal important to you? How does it align with your values and other goals?",rows:2,...E("relevant"),"aria-invalid":M.relevant?"true":"false"}),M.relevant&&a.jsx("p",{className:"text-sm text-destructive",children:M.relevant.message})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"timeBound",children:"Time-Bound *"}),a.jsx(m.g,{id:"timeBound",placeholder:"When will you complete this goal? Include deadlines and timeline details.",rows:2,...E("timeBound"),"aria-invalid":M.timeBound?"true":"false"}),M.timeBound&&a.jsx("p",{className:"text-sm text-destructive",children:M.timeBound.message})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(u._,{children:"Tags"}),a.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:O.map(e=>(0,a.jsxs)(f.C,{variant:"secondary",className:"flex items-center space-x-1",children:[a.jsx("span",{children:e}),a.jsx("button",{type:"button",onClick:()=>S(e),className:"ml-1 hover:text-destructive",children:a.jsx(x.Z,{className:"h-3 w-3"})})]},e))}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(c.I,{placeholder:"Add a tag",value:D,onChange:e=>_(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),F())}),a.jsx(d.z,{type:"button",variant:"outline",onClick:F,children:a.jsx(g.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"notes",children:"Additional Notes"}),a.jsx(m.g,{id:"notes",placeholder:"Any additional thoughts, ideas, or context for this goal...",rows:3,...E("notes")})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 pt-4 border-t",children:[a.jsx(d.z,{type:"button",variant:"outline",onClick:n,children:"Cancel"}),(0,a.jsxs)(d.z,{type:"submit",disabled:!L,children:[a.jsx(h.Z,{className:"mr-2 h-4 w-4"}),e?.id?"Update Goal":"Create Goal"]})]})]})})]})}},19591:(e,t,r)=>{"use strict";r.d(t,{C:()=>l});var a=r(95344);r(3729);var i=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let s=(0,i.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return a.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(s({variant:t}),e),...r})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var a=r(95344),i=r(3729),s=r(32751),l=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(42739);let o=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,loading:l=!1,leftIcon:d,rightIcon:c,children:u,disabled:m,...f},h)=>{let p=i?s.g7:"button",v=m||l;return(0,a.jsxs)(p,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o({variant:t,size:r,className:e})),ref:h,disabled:v,"aria-disabled":v,...f,children:[l&&a.jsx(n.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!l&&d&&a.jsx("span",{className:"mr-2",children:d}),u,!l&&c&&a.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>l,SZ:()=>o,Zb:()=>s,aY:()=>d,ll:()=>n});var a=r(95344),i=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let s=i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));s.displayName="Card";let l=i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let n=i.forwardRef(({className:e,...t},r)=>a.jsx("h3",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...t}));n.displayName="CardTitle";let o=i.forwardRef(({className:e,...t},r)=>a.jsx("p",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let d=i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...t}));d.displayName="CardContent",i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},46540:(e,t,r)=>{"use strict";r.d(t,{I:()=>o});var a=r(95344),i=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var s=r(66138),l=r(1222),n=r(53148);let o=i.forwardRef(({className:e,type:t,label:r,error:o,helperText:d,leftIcon:c,rightIcon:u,variant:m="default",id:f,required:h,...p},v)=>{let[x,g]=i.useState(!1),[b]=i.useState(()=>f||`input-${Math.random().toString(36).substr(2,9)}`),N="password"===t,y=N&&x?"text":t,j=!!o||"error"===m;return(0,a.jsxs)("div",{className:"space-y-2",children:[r&&(0,a.jsxs)("label",{htmlFor:b,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[r,h&&a.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[c&&a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:c})}),a.jsx("input",{type:y,id:b,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",j&&"border-destructive focus-visible:ring-destructive","success"===m&&"border-green-500 focus-visible:ring-green-500",c&&"pl-10",(u||N||j)&&"pr-10",e),ref:v,"aria-invalid":j,"aria-describedby":o?`${b}-error`:d?`${b}-helper`:void 0,...p}),j&&a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx(s.Z,{className:"h-4 w-4 text-destructive"})}),N&&a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{g(!x)},"aria-label":x?"Hide password":"Show password",children:x?a.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):a.jsx(n.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),u&&!j&&!N&&a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:u})})]}),o&&a.jsx("p",{id:`${b}-error`,className:"text-sm text-destructive",role:"alert",children:o}),d&&!o&&a.jsx("p",{id:`${b}-helper`,className:"text-sm text-muted-foreground",children:d})]})});o.displayName="Input"},33668:(e,t,r)=>{"use strict";r.d(t,{_:()=>d});var a=r(95344),i=r(3729),s=r(62409),l=i.forwardRef((e,t)=>(0,a.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef(({className:e,...t},r)=>a.jsx(l,{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(o(),e),...t}));d.displayName=l.displayName},47210:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(95344),i=r(3729),s=r(47370);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=i.forwardRef(({className:e,value:t,...r},i)=>a.jsx(s.fC,{ref:i,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:a.jsx(s.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));l.displayName=s.fC.displayName},2690:(e,t,r)=>{"use strict";r.d(t,{g:()=>s});var a=r(95344),i=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let s=i.forwardRef(({className:e,...t},r)=>a.jsx("textarea",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));s.displayName="Textarea"},60339:(e,t,r)=>{"use strict";r.d(t,{pm:()=>m});var a=r(3729);let i=0,s=new Map,l=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?l(r):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=n(d,e),o.forEach(e=>{e(d)})}function u({...e}){let t=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=a.useState(d);return a.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},33468:(e,t,r)=>{"use strict";r.d(t,{t:()=>s});var a=r(43158),i=r(67023);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let s=(0,a.Ue)()((0,i.tJ)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.login(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Login failed. Please try again."}),t}},register:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.register(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Registration failed. Please try again."}),t}},logout:async()=>{e({isLoading:!0});try{await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.logout()}catch(e){}finally{e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}},refreshToken:async()=>{let{token:r}=t();if(!r)throw Error("No token available for refresh");try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.refreshToken();e({user:t.user,token:t.accessToken,isAuthenticated:!0,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,error:"Session expired. Please login again."}),t}},updateProfile:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.updateProfile(t);e({user:r,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Profile update failed. Please try again."}),t}},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},initialize:async()=>{let{token:r}=t();if(r){e({isLoading:!0});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.me();e({user:t,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}}}}),{name:"auth-storage",partialize:e=>({token:e.token,user:e.user}),onRehydrateStorage:()=>e=>{e?.token&&Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e.token)}})),l=null,n=()=>{let{token:e,refreshToken:t,logout:r}=s.getState();if(l&&clearTimeout(l),e)try{let a=JSON.parse(atob(e.split(".")[1])),i=1e3*a.exp,s=Date.now();l=setTimeout(async()=>{try{await t(),n()}catch(e){await r()}},Math.max(i-s-3e5,0))}catch(e){r()}};s.subscribe(e=>e.token,e=>{e?(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e),n()):(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(null),l&&(clearTimeout(l),l=null))})},95021:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>i});var a=r(25036);let i={title:"Dashboard",description:"Your life planning dashboard - track progress and manage goals"};function s({children:e}){return a.jsx(a.Fragment,{children:e})}},21342:(e,t,r)=>{"use strict";function a({children:e}){return e}r.r(t),r.d(t,{default:()=>a})},98462:(e,t,r)=>{"use strict";r.d(t,{b:()=>l,k:()=>s});var a=r(3729),i=r(95344);function s(e,t){let r=a.createContext(t),s=e=>{let{children:t,...s}=e,l=a.useMemo(()=>s,Object.values(s));return(0,i.jsx)(r.Provider,{value:l,children:t})};return s.displayName=e+"Provider",[s,function(i){let s=a.useContext(r);if(s)return s;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],s=()=>{let t=r.map(e=>a.createContext(e));return function(r){let i=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return s.scopeName=e,[function(t,s){let l=a.createContext(s),n=r.length;r=[...r,s];let o=t=>{let{scope:r,children:s,...o}=t,d=r?.[e]?.[n]||l,c=a.useMemo(()=>o,Object.values(o));return(0,i.jsx)(d.Provider,{value:c,children:s})};return o.displayName=t+"Provider",[o,function(r,i){let o=i?.[e]?.[n]||l,d=a.useContext(o);if(d)return d;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:a})=>{let i=r(e)[`__scope${a}`];return{...t,...i}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(s,...t)]}},62409:(e,t,r)=>{"use strict";r.d(t,{WV:()=>n,jH:()=>o});var a=r(3729),i=r(81202),s=r(32751),l=r(95344),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.Z8)(`Primitive.${t}`),i=a.forwardRef((e,a)=>{let{asChild:i,...s}=e,n=i?r:t;return(0,l.jsx)(n,{...s,ref:a})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function o(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},47370:(e,t,r)=>{"use strict";r.d(t,{fC:()=>N,z$:()=>y});var a=r(3729),i=r(98462),s=r(62409),l=r(95344),n="Progress",[o,d]=(0,i.b)(n),[c,u]=o(n),m=a.forwardRef((e,t)=>{let{__scopeProgress:r,value:a=null,max:i,getValueLabel:n=p,...o}=e;(i||0===i)&&g(i);let d=g(i)?i:100;null!==a&&b(a,d);let u=b(a,d)?a:null,m=x(u)?n(u,d):void 0;return(0,l.jsx)(c,{scope:r,value:u,max:d,children:(0,l.jsx)(s.WV.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":x(u)?u:void 0,"aria-valuetext":m,role:"progressbar","data-state":v(u,d),"data-value":u??void 0,"data-max":d,...o,ref:t})})});m.displayName=n;var f="ProgressIndicator",h=a.forwardRef((e,t)=>{let{__scopeProgress:r,...a}=e,i=u(f,r);return(0,l.jsx)(s.WV.div,{"data-state":v(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:t})});function p(e,t){return`${Math.round(e/t*100)}%`}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function x(e){return"number"==typeof e}function g(e){return x(e)&&!isNaN(e)&&e>0}function b(e,t){return x(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=f;var N=m,y=h}};