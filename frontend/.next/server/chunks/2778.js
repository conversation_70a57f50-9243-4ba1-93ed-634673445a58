exports.id=2778,exports.ids=[2778],exports.modules={13660:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},35303:()=>{},66138:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,r(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},20783:(e,t,r)=>{e.exports=r(61476)},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var o=r(95344),i=r(3729),n=r(32751),a=r(92193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var s=r(42739);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,loading:a=!1,leftIcon:d,rightIcon:c,children:u,disabled:f,...m},h)=>{let b=i?n.g7:"button",p=f||a;return(0,o.jsxs)(b,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l({variant:t,size:r,className:e})),ref:h,disabled:p,"aria-disabled":p,...m,children:[a&&o.jsx(s.Z,{className:"mr-2 h-4 w-4 animate-spin"}),!a&&d&&o.jsx("span",{className:"mr-2",children:d}),u,!a&&c&&o.jsx("span",{className:"ml-2",children:c})]})});d.displayName="Button"},23673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>a,SZ:()=>l,Zb:()=>n,aY:()=>d,ll:()=>s});var o=r(95344),i=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let n=i.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let a=i.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...t}));a.displayName="CardHeader";let s=i.forwardRef(({className:e,...t},r)=>o.jsx("h3",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-2xl font-semibold leading-none tracking-tight",e),...t}));s.displayName="CardTitle";let l=i.forwardRef(({className:e,...t},r)=>o.jsx("p",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let d=i.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...t}));d.displayName="CardContent",i.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},46540:(e,t,r)=>{"use strict";r.d(t,{I:()=>l});var o=r(95344),i=r(3729);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(66138),a=r(1222),s=r(53148);let l=i.forwardRef(({className:e,type:t,label:r,error:l,helperText:d,leftIcon:c,rightIcon:u,variant:f="default",id:m,required:h,...b},p)=>{let[O,g]=i.useState(!1),[v]=i.useState(()=>m||`input-${Math.random().toString(36).substr(2,9)}`),N="password"===t,x=N&&O?"text":t,y=!!l||"error"===f;return(0,o.jsxs)("div",{className:"space-y-2",children:[r&&(0,o.jsxs)("label",{htmlFor:v,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[r,h&&o.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,o.jsxs)("div",{className:"relative",children:[c&&o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:c})}),o.jsx("input",{type:x,id:v,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",y&&"border-destructive focus-visible:ring-destructive","success"===f&&"border-green-500 focus-visible:ring-green-500",c&&"pl-10",(u||N||y)&&"pr-10",e),ref:p,"aria-invalid":y,"aria-describedby":l?`${v}-error`:d?`${v}-helper`:void 0,...b}),y&&o.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:o.jsx(n.Z,{className:"h-4 w-4 text-destructive"})}),N&&o.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{g(!O)},"aria-label":O?"Hide password":"Show password",children:O?o.jsx(a.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}):o.jsx(s.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})}),u&&!y&&!N&&o.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:o.jsx("div",{className:"h-4 w-4 text-muted-foreground",children:u})})]}),l&&o.jsx("p",{id:`${v}-error`,className:"text-sm text-destructive",role:"alert",children:l}),d&&!l&&o.jsx("p",{id:`${v}-helper`,className:"text-sm text-muted-foreground",children:d})]})});l.displayName="Input"},41460:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var o=r(3729),i=r(38067);let n=()=>{let{addNotification:e,removeNotification:t,clearAllNotifications:r}=(0,i.G)(),n=(0,o.useCallback)(t=>{e(t)},[e]),a=(0,o.useCallback)((e,t)=>{n({type:"success",title:e,message:t})},[n]),s=(0,o.useCallback)((e,t)=>{n({type:"error",title:e,message:t,duration:8e3})},[n]),l=(0,o.useCallback)((e,t)=>{n({type:"warning",title:e,message:t})},[n]),d=(0,o.useCallback)((e,t)=>{n({type:"info",title:e,message:t})},[n]);return{showNotification:n,showSuccess:a,showError:s,showWarning:l,showInfo:d,dismissNotification:(0,o.useCallback)(e=>{t(e)},[t]),clearAll:(0,o.useCallback)(()=>{r()},[r])}}},60339:(e,t,r)=>{"use strict";r.d(t,{pm:()=>f});var o=r(3729);let i=0,n=new Map,a=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},s=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=s(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=o.useState(d);return o.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},33468:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var o=r(43158),i=r(67023);!function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}();let n=(0,o.Ue)()((0,i.tJ)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.login(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Login failed. Please try again."}),t}},register:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.register(t);e({user:r.user,token:r.accessToken,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Registration failed. Please try again."}),t}},logout:async()=>{e({isLoading:!0});try{await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.logout()}catch(e){}finally{e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}},refreshToken:async()=>{let{token:r}=t();if(!r)throw Error("No token available for refresh");try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.refreshToken();e({user:t.user,token:t.accessToken,isAuthenticated:!0,error:null})}catch(t){throw e({user:null,token:null,isAuthenticated:!1,error:"Session expired. Please login again."}),t}},updateProfile:async t=>{e({isLoading:!0,error:null});try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.updateProfile(t);e({user:r,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}())?t.message:"Profile update failed. Please try again."}),t}},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},initialize:async()=>{let{token:r}=t();if(r){e({isLoading:!0});try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).auth.me();e({user:t,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){e({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null})}}}}),{name:"auth-storage",partialize:e=>({token:e.token,user:e.user}),onRehydrateStorage:()=>e=>{e?.token&&Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e.token)}})),a=null,s=()=>{let{token:e,refreshToken:t,logout:r}=n.getState();if(a&&clearTimeout(a),e)try{let o=JSON.parse(atob(e.split(".")[1])),i=1e3*o.exp,n=Date.now();a=setTimeout(async()=>{try{await t(),s()}catch(e){await r()}},Math.max(i-n-3e5,0))}catch(e){r()}};n.subscribe(e=>e.token,e=>{e?(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(e),s()):(Object(function(){var e=Error("Cannot find module '@/lib/api'");throw e.code="MODULE_NOT_FOUND",e}()).setToken(null),a&&(clearTimeout(a),a=null))})},38067:(e,t,r)=>{"use strict";r.d(t,{G:()=>o});let o=(0,r(43158).Ue)((e,t)=>({notifications:[],addNotification:r=>{let o=Math.random().toString(36).substring(2)+Date.now().toString(36),i={id:o,duration:5e3,...r};e(e=>({notifications:[...e.notifications,i]})),i.duration&&i.duration>0&&setTimeout(()=>{t().removeNotification(o)},i.duration)},removeNotification:t=>{e(e=>({notifications:e.notifications.filter(e=>e.id!==t)}))},clearAllNotifications:()=>{e({notifications:[]})}}))},84295:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>i});var o=r(25036);let i={title:"Authentication",description:"Sign in or create an account for the 8,760 Hours Life Planning Platform"};function n({children:e}){return o.jsx("div",{className:"min-h-screen bg-background",children:e})}},21342:(e,t,r)=>{"use strict";function o({children:e}){return e}r.r(t),r.d(t,{default:()=>o})}};