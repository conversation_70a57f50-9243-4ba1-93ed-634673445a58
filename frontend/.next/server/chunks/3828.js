"use strict";exports.id=3828,exports.ids=[3828],exports.modules={69224:(e,t,r)=>{r.d(t,{Z:()=>u});var n=r(3729),l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),u=(e,t)=>{let r=(0,n.forwardRef)(({color:r="currentColor",size:u=24,strokeWidth:o=2,absoluteStrokeWidth:a,className:s="",children:c,...f},d)=>(0,n.createElement)("svg",{ref:d,...l,width:u,height:u,stroke:r,strokeWidth:a?24*Number(o)/Number(u):o,className:["lucide",`lucide-${i(e)}`,s].join(" "),...f},[...t.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r}},42739:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},95344:(e,t,r)=>{e.exports=r(16372).vendored["react-ssr"].ReactJsxRuntime},30080:(e,t,r)=>{/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(3729);"function"==typeof Object.is&&Object.is,n.useState,n.useEffect,n.useLayoutEffect,n.useDebugValue,t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:function(e,t){return t()}},27986:(e,t,r)=>{/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(3729),l=r(8145),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},u=l.useSyncExternalStore,o=n.useRef,a=n.useEffect,s=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,l){var f=o(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=u(e,(f=s(function(){function e(e){if(!a){if(a=!0,u=e,e=n(e),void 0!==l&&d.hasValue){var t=d.value;if(l(t,e))return o=t}return o=e}if(t=o,i(u,e))return t;var r=n(e);return void 0!==l&&l(t,r)?(u=e,t):(u=e,o=r)}var u,o,a=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,l]))[0],f[1]);return a(function(){d.hasValue=!0,d.value=p},[p]),c(p),p}},8145:(e,t,r)=>{e.exports=r(30080)},34657:(e,t,r)=>{e.exports=r(27986)},25036:(e,t,r)=>{e.exports=r(50482).vendored["react-rsc"].ReactJsxRuntime},31405:(e,t,r)=>{r.d(t,{F:()=>i,e:()=>u});var n=r(3729);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function u(...e){return n.useCallback(i(...e),e)}},32751:(e,t,r)=>{r.d(t,{Z8:()=>u,g7:()=>o});var n=r(3729),l=r(31405),i=r(95344);function u(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,u;let o=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,a=function(e,t){let r={...t};for(let n in t){let l=e[n],i=t[n];/^on[A-Z]/.test(n)?l&&i?r[n]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...i}:"className"===n&&(r[n]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(a.ref=t?(0,l.F)(t,o):o),n.cloneElement(r,a)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...u}=e,o=n.Children.toArray(l),a=o.find(s);if(a){let e=a.props.children,l=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...u,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...u,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var o=u("Slot"),a=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},92193:(e,t,r)=>{r.d(t,{j:()=>i});let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=function(){for(var e,t,r=0,n="",l=arguments.length;r<l;r++)(e=arguments[r])&&(t=function e(t){var r,n,l="";if("string"==typeof t||"number"==typeof t)l+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(l&&(l+=" "),l+=n)}else for(n in t)t[n]&&(l&&(l+=" "),l+=n)}return l}(e))&&(n&&(n+=" "),n+=t);return n},i=(e,t)=>r=>{var i;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:u,defaultVariants:o}=t,a=Object.keys(u).map(e=>{let t=null==r?void 0:r[e],l=null==o?void 0:o[e];if(null===t)return null;let i=n(t)||n(l);return u[e][i]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,a,null==t?void 0:null===(i=t.compoundVariants)||void 0===i?void 0:i.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...s}[t]):({...o,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},43158:(e,t,r)=>{r.d(t,{Ue:()=>d});let n=e=>{let t;let r=new Set,n=(e,n)=>{let l="function"==typeof e?e(t):e;if(!Object.is(l,t)){let e=t;t=(null!=n?n:"object"!=typeof l||null===l)?l:Object.assign({},t,l),r.forEach(r=>r(t,e))}},l=()=>t,i={setState:n,getState:l,getInitialState:()=>u,subscribe:e=>(r.add(e),()=>r.delete(e)),destroy:()=>{r.clear()}},u=t=e(n,l,i);return i},l=e=>e?n(e):n;var i=r(3729),u=r(34657);let{useDebugValue:o}=i,{useSyncExternalStoreWithSelector:a}=u,s=!1,c=e=>e,f=e=>{let t="function"==typeof e?l(e):e,r=(e,r)=>(function(e,t=c,r){r&&!s&&(s=!0);let n=a(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return o(n),n})(t,e,r);return Object.assign(r,t),r},d=e=>e?f(e):f}};