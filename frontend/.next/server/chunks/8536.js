"use strict";exports.id=8536,exports.ids=[8536],exports.modules={62312:(t,e,n)=>{n.d(e,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},37574:(t,e,n)=>{n.d(e,{ee:()=>tF,Eh:()=>tN,VY:()=>tB,fC:()=>tV,D7:()=>tR});var r=n(3729);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=t=>({x:t,y:t}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function g(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}function y(t){return["top","bottom"].includes(p(t))?"y":"x"}function x(t){return t.replace(/start|end/g,t=>c[t])}function w(t){return t.replace(/left|right|bottom|top/g,t=>u[t])}function v(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function b(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function A(t,e,n){let r,{reference:i,floating:o}=t,l=y(e),a=g(y(e)),f=m(a),s=p(e),u="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,x=i[f]/2-o[f]/2;switch(s){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[a]-=x*(n&&u?-1:1);break;case"end":r[a]+=x*(n&&u?-1:1)}return r}let R=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(e)),s=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:u,y:c}=A(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:g}=a[n],{x:m,y:y,data:x,reset:w}=await g({x:u,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:t,floating:e}});u=null!=m?m:u,c=null!=y?y:c,p={...p,[o]:{...p[o],...x}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):w.rects),{x:u,y:c}=A(s,d,f)),n=-1)}return{x:u,y:c,placement:d,strategy:i,middlewareData:p}};async function O(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=t,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(e,t),g=v(h),m=a[p?"floating"===c?"reference":"floating":c],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:f})),x="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),A=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:x,offsetParent:w,strategy:f}):x);return{top:(y.top-R.top+g.top)/A.y,bottom:(R.bottom-y.bottom+g.bottom)/A.y,left:(y.left-R.left+g.left)/A.x,right:(R.right-y.right+g.right)/A.x}}function S(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function P(t){return i.some(e=>t[e]>=0)}async function E(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),s=["left","top"].includes(l)?-1:1,u=o&&f?-1:1,c=d(e,t),{mainAxis:g,crossAxis:m,alignmentAxis:x}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof x&&(m="end"===a?-1*x:x),f?{x:m*u,y:g*s}:{x:g*s,y:m*u}}function C(t){var e;return e=0,"#document"}function L(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function T(t){var e,n;return null==(e=(n=0,t.document||window.document))?void 0:e.documentElement}function k(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=j(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(i)}function D(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function H(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function W(t){return["html","body","#document"].includes(C(t))}function j(t){return L(t).getComputedStyle(t)}function $(t){return{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function z(t){return"html"===C(t)?t:t.assignedSlot||t.parentNode||T(t)}function M(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=z(e);return W(n)?e.ownerDocument?e.ownerDocument.body:e.body:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=L(i);if(o){let t=V(l);return e.concat(l,l.visualViewport||[],k(i)?i:[],t&&n?M(t):[])}return e.concat(i,M(i,[],n))}function V(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function F(t){return t.contextElement}function B(t){return F(t),s(1)}let N=s(0);function Y(t){let e=L(t);return H()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:N}function I(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=F(t),a=s(1);e&&(r||(a=B(t)));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===L(l))&&i)?Y(l):s(0),u=(o.left+f.x)/a.x,c=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let t=L(l),e=V(t);for(;e&&r&&r!==t;){let n=B(e),r=e.getBoundingClientRect(),i=j(e),o=r.left+(e.clientLeft+parseFloat(i.paddingLeft))*n.x,l=r.top+(e.clientTop+parseFloat(i.paddingTop))*n.y;u*=n.x,c*=n.y,d*=n.x,p*=n.y,u+=o,c+=l,e=V(t=L(e))}}return b({width:d,height:p,x:u,y:c})}function X(t,e){let n=$(t).scrollLeft;return e?e.left+n:I(T(t)).left+n}function _(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:X(t,r)),y:r.top+e.scrollTop}}function q(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=L(t),r=T(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let t=H();(!t||t&&"fixed"===e)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(t,n);else if("document"===e)r=function(t){let e=T(t),n=$(t),r=t.ownerDocument.body,i=l(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=l(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+X(t),f=-n.scrollTop;return"rtl"===j(r).direction&&(a+=l(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(T(t));else{let n=Y(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return b(r)}function Z(t,e){let n=L(t);if(D(t))return n;{let e=z(t);for(;e&&!W(e);)e=z(e);return n}}let G=async function(t){let e=this.getOffsetParent||Z,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=T(e),i="fixed"===n,o=I(t,!0,i,e),l={scrollLeft:0,scrollTop:0},a=s(0);if(!i){("body"!==C(e)||k(r))&&(l=$(e));r&&(a.x=X(r))}i&&r&&(a.x=X(r));let f=!r||i?s(0):_(r,l);return{x:o.left+l.scrollLeft-a.x-f.x,y:o.top+l.scrollTop-a.y-f.y,width:o.width,height:o.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},J={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=T(r),a=!!e&&D(e.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},u=s(1),c=s(0);o||("body"!==C(r)||k(l))&&(f=$(r));let d=!l||o?s(0):_(l,f,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-f.scrollTop*u.y+c.y+d.y}},getDocumentElement:T,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t,a=[..."clippingAncestors"===n?D(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=M(t,[],!1).filter(t=>!1);return"fixed"===j(t).position&&z(t),e.set(t,r),r}(e,this._c):[].concat(n),r],f=a[0],s=a.reduce((t,n)=>{let r=q(e,n,i);return t.top=l(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=l(r.left,t.left),t},q(e,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:Z,getElementRects:G,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=function(t){let e=j(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=n,o=r,l=a(n)!==i||a(r)!==o;return l&&(n=i,r=o),{width:n,height:r,$:l}}(t);return{width:e,height:n}},getScale:B,isElement:function(t){return!1},isRTL:function(t){return"rtl"===j(t).direction}};function K(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}let Q=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:u}=e,{element:c,padding:p=0}=d(t,e)||{};if(null==c)return{};let x=v(p),w={x:n,y:r},b=g(y(i)),A=m(b),R=await f.getDimensions(c),O="y"===b,S=O?"clientHeight":"clientWidth",P=a.reference[A]+a.reference[b]-w[b]-a.floating[A],E=w[b]-a.reference[b],C=await (null==f.getOffsetParent?void 0:f.getOffsetParent(c)),L=C?C[S]:0;L&&await (null==f.isElement?void 0:f.isElement(C))||(L=s.floating[S]||a.floating[A]);let T=L/2-R[A]/2-1,k=o(x[O?"top":"left"],T),D=o(x[O?"bottom":"right"],T),H=L-R[A]-D,W=L/2-R[A]/2+(P/2-E/2),j=l(k,o(W,H)),$=!u.arrow&&null!=h(i)&&W!==j&&a.reference[A]/2-(W<k?k:D)-R[A]/2<0,z=$?W<k?W-k:W-H:0;return{[b]:w[b]+z,data:{[b]:j,centerOffset:W-j-z,...$&&{alignmentOffset:z}},reset:$}}}),U=(t,e,n)=>{let r=new Map,i={platform:J,...n},o={...i.platform,_c:r};return R(t,e,{...i,platform:o})};var tt=n(81202),te="undefined"!=typeof document?r.useLayoutEffect:function(){};function tn(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!tn(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!tn(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function tr(t,e){return Math.round(1*e)/1}function ti(t){let e=r.useRef(t);return te(()=>{e.current=t}),e}let to=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?Q({element:n.current,padding:r}).fn(e):{}:n?Q({element:n,padding:r}).fn(e):{}}}),tl=(t,e)=>({...function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=e,f=await E(e,t);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(t),options:[t,e]}),ta=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:i}=e,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...u}=d(t,e),c={x:n,y:r},h=await O(e,u),m=y(p(i)),x=g(m),w=c[x],v=c[m];if(a){let t="y"===x?"top":"left",e="y"===x?"bottom":"right",n=w+h[t],r=w-h[e];w=l(n,o(w,r))}if(f){let t="y"===m?"top":"left",e="y"===m?"bottom":"right",n=v+h[t],r=v-h[e];v=l(n,o(v,r))}let b=s.fn({...e,[x]:w,[m]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[x]:a,[m]:f}}}}}}(t),options:[t,e]}),tf=(t,e)=>({...function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(t,e),u={x:n,y:r},c=y(i),h=g(c),m=u[h],x=u[c],w=d(a,e),v="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(f){let t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+v.mainAxis,n=o.reference[h]+o.reference[t]-v.mainAxis;m<e?m=e:m>n&&(m=n)}if(s){var b,A;let t="y"===h?"width":"height",e=["top","left"].includes(p(i)),n=o.reference[c]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[c])||0)+(e?0:v.crossAxis),r=o.reference[c]+o.reference[t]+(e?0:(null==(A=l.offset)?void 0:A[c])||0)-(e?v.crossAxis:0);x<n?x=n:x>r&&(x=r)}return{[h]:m,[c]:x}}}}(t),options:[t,e]}),ts=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:u,platform:c,elements:v}=e,{mainAxis:b=!0,crossAxis:A=!0,fallbackPlacements:R,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:E=!0,...C}=d(t,e);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let L=p(a),T=y(u),k=p(u)===u,D=await (null==c.isRTL?void 0:c.isRTL(v.floating)),H=R||(k||!E?[w(u)]:function(t){let e=w(t);return[x(t),e,x(e)]}(u)),W="none"!==P;!R&&W&&H.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){let r=["left","right"],i=["right","left"];switch(t){case"top":case"bottom":if(n)return e?i:r;return e?r:i;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(p(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(x)))),o}(u,E,P,D));let j=[u,...H],$=await O(e,C),z=[],M=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&z.push($[L]),A){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=g(y(t)),o=m(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=w(l)),[l,w(l)]}(a,s,D);z.push($[t[0]],$[t[1]])}if(M=[...M,{placement:a,overflows:z}],!z.every(t=>t<=0)){let t=((null==(i=f.flip)?void 0:i.index)||0)+1,e=j[t];if(e&&(!("alignment"===A&&T!==y(e))||M.every(t=>t.overflows[0]>0&&y(t.placement)===T)))return{data:{index:t,overflows:M},reset:{placement:e}};let n=null==(o=M.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let t=null==(l=M.filter(t=>{if(W){let e=y(t.placement);return e===T||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}}(t),options:[t,e]}),tu=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let i,a;let{placement:f,rects:s,platform:u,elements:c}=e,{apply:g=()=>{},...m}=d(t,e),x=await O(e,m),w=p(f),v=h(f),b="y"===y(f),{width:A,height:R}=s.floating;"top"===w||"bottom"===w?(i=w,a=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=w,i="end"===v?"top":"bottom");let S=R-x.top-x.bottom,P=A-x.left-x.right,E=o(R-x[i],S),C=o(A-x[a],P),L=!e.middlewareData.shift,T=E,k=C;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=P),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(T=S),L&&!v){let t=l(x.left,0),e=l(x.right,0),n=l(x.top,0),r=l(x.bottom,0);b?k=A-2*(0!==t||0!==e?t+e:l(x.left,x.right)):T=R-2*(0!==n||0!==r?n+r:l(x.top,x.bottom))}await g({...e,availableWidth:k,availableHeight:T});let D=await u.getDimensions(c.floating);return A!==D.width||R!==D.height?{reset:{rects:!0}}:{}}}}(t),options:[t,e]}),tc=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=d(t,e);switch(r){case"referenceHidden":{let t=S(await O(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:P(t)}}}case"escaped":{let t=S(await O(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:P(t)}}}default:return{}}}}}(t),options:[t,e]}),td=(t,e)=>({...to(t),options:[t,e]});var tp=n(62409),th=n(95344),tg=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,th.jsx)(tp.WV.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,th.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tg.displayName="Arrow";var tm=n(31405),ty=n(98462),tx=n(2256),tw=n(16069),tv=n(63085),tb="Popper",[tA,tR]=(0,ty.b)(tb),[tO,tS]=tA(tb),tP=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,th.jsx)(tO,{scope:e,anchor:i,onAnchorChange:o,children:n})};tP.displayName=tb;var tE="PopperAnchor",tC=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,l=tS(tE,n),a=r.useRef(null),f=(0,tm.e)(e,a);return r.useEffect(()=>{l.onAnchorChange(i?.current||a.current)}),i?null:(0,th.jsx)(tp.WV.div,{...o,ref:f})});tC.displayName=tE;var tL="PopperContent",[tT,tk]=tA(tL),tD=r.forwardRef((t,e)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:s="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:g="partial",hideWhenDetached:m=!1,updatePositionStrategy:y="optimized",onPlaced:x,...w}=t,v=tS(tL,n),[b,A]=r.useState(null),R=(0,tm.e)(e,t=>A(t)),[O,S]=r.useState(null),P=(0,tv.t)(O),E=P?.width??0,C=P?.height??0,L="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},k=Array.isArray(p)?p:[p],D=k.length>0,H={padding:L,boundary:k.filter(t$),altBoundary:D},{refs:W,floatingStyles:j,placement:$,isPositioned:z,middlewareData:V}=function(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:u}=t,[c,d]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);tn(p,i)||h(i);let[g,m]=r.useState(null),[y,x]=r.useState(null),w=r.useCallback(t=>{t!==R.current&&(R.current=t,m(t))},[]),v=r.useCallback(t=>{t!==O.current&&(O.current=t,x(t))},[]),b=l||g,A=a||y,R=r.useRef(null),O=r.useRef(null),S=r.useRef(c),P=null!=s,E=ti(s),C=ti(o),L=ti(u),T=r.useCallback(()=>{if(!R.current||!O.current)return;let t={placement:e,strategy:n,middleware:p};C.current&&(t.platform=C.current),U(R.current,O.current,t).then(t=>{let e={...t,isPositioned:!1!==L.current};k.current&&!tn(S.current,e)&&(S.current=e,tt.flushSync(()=>{d(e)}))})},[p,e,n,C,L]);te(()=>{!1===u&&S.current.isPositioned&&(S.current.isPositioned=!1,d(t=>({...t,isPositioned:!1})))},[u]);let k=r.useRef(!1);te(()=>(k.current=!0,()=>{k.current=!1}),[]),te(()=>{if(b&&(R.current=b),A&&(O.current=A),b&&A){if(E.current)return E.current(b,A,T);T()}},[b,A,T,E,P]);let D=r.useMemo(()=>({reference:R,floating:O,setReference:w,setFloating:v}),[w,v]),H=r.useMemo(()=>({reference:b,floating:A}),[b,A]),W=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!H.floating)return t;let e=tr(H.floating,c.x),r=tr(H.floating,c.y);return f?{...t,transform:"translate("+e+"px, "+r+"px)",...(H.floating,!1)}:{position:n,left:e,top:r}},[n,f,H.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:T,refs:D,elements:H,floatingStyles:W}),[c,T,D,H,W])}({strategy:"fixed",placement:i+("center"!==s?"-"+s:""),whileElementsMounted:(...t)=>(function(t,e,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=F(t),h=a||s?[...p?M(p):[],...M(e)]:[];h.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)});let g=p&&c?function(t,e){let n,r=null,i=T(t);function a(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return function s(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),a();let d=t.getBoundingClientRect(),{left:p,top:h,width:g,height:m}=d;if(u||e(),!g||!m)return;let y=f(h),x=f(i.clientWidth-(p+g)),w={rootMargin:-y+"px "+-x+"px "+-f(i.clientHeight-(h+m))+"px "+-f(p)+"px",threshold:l(0,o(1,c))||1},v=!0;function b(e){let r=e[0].intersectionRatio;if(r!==c){if(!v)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||K(d,t.getBoundingClientRect())||s(),v=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(t){r=new IntersectionObserver(b,w)}r.observe(t)}(!0),a}(p,n):null,m=-1,y=null;u&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let x=d?I(t):null;return d&&function e(){let r=I(t);x&&!K(x,r)&&n(),x=r,i=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{a&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)}),null==g||g(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(i)}})(...t,{animationFrame:"always"===y}),elements:{reference:v.anchor},middleware:[tl({mainAxis:a+C,alignmentAxis:u}),d&&ta({mainAxis:!0,crossAxis:!1,limiter:"partial"===g?tf():void 0,...H}),d&&ts({...H}),tu({...H,apply:({elements:t,rects:e,availableWidth:n,availableHeight:r})=>{let{width:i,height:o}=e.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${i}px`),l.setProperty("--radix-popper-anchor-height",`${o}px`)}}),O&&td({element:O,padding:c}),tz({arrowWidth:E,arrowHeight:C}),m&&tc({strategy:"referenceHidden",...H})]}),[B,N]=tM($),Y=(0,tx.W)(x);(0,tw.b)(()=>{z&&Y?.()},[z,Y]);let X=V.arrow?.x,_=V.arrow?.y,q=V.arrow?.centerOffset!==0,[Z,G]=r.useState();return(0,tw.b)(()=>{b&&G(window.getComputedStyle(b).zIndex)},[b]),(0,th.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...j,transform:z?j.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Z,"--radix-popper-transform-origin":[V.transformOrigin?.x,V.transformOrigin?.y].join(" "),...V.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,th.jsx)(tT,{scope:n,placedSide:B,onArrowChange:S,arrowX:X,arrowY:_,shouldHideArrow:q,children:(0,th.jsx)(tp.WV.div,{"data-side":B,"data-align":N,...w,ref:R,style:{...w.style,animation:z?void 0:"none"}})})})});tD.displayName=tL;var tH="PopperArrow",tW={top:"bottom",right:"left",bottom:"top",left:"right"},tj=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=tk(tH,n),o=tW[i.placedSide];return(0,th.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,th.jsx)(tg,{...r,ref:e,style:{...r.style,display:"block"}})})});function t$(t){return null!==t}tj.displayName=tH;var tz=t=>({name:"transformOrigin",options:t,fn(e){let{placement:n,rects:r,middlewareData:i}=e,o=i.arrow?.centerOffset!==0,l=o?0:t.arrowWidth,a=o?0:t.arrowHeight,[f,s]=tM(n),u={start:"0%",center:"50%",end:"100%"}[s],c=(i.arrow?.x??0)+l/2,d=(i.arrow?.y??0)+a/2,p="",h="";return"bottom"===f?(p=o?u:`${c}px`,h=`${-a}px`):"top"===f?(p=o?u:`${c}px`,h=`${r.floating.height+a}px`):"right"===f?(p=`${-a}px`,h=o?u:`${d}px`):"left"===f&&(p=`${r.floating.width+a}px`,h=o?u:`${d}px`),{data:{x:p,y:h}}}});function tM(t){let[e,n="center"]=t.split("-");return[e,n]}var tV=tP,tF=tC,tB=tD,tN=tj},63085:(t,e,n)=>{n.d(e,{t:()=>o});var r=n(3729),i=n(16069);function o(t){let[e,n]=r.useState(void 0);return(0,i.b)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}}};