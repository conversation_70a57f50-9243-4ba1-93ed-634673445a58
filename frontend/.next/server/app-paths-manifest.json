{"/_not-found": "app/_not-found.js", "/api/health/live/route": "app/api/health/live/route.js", "/api/health/route": "app/api/health/route.js", "/page": "app/page.js", "/api/health/ready/route": "app/api/health/ready/route.js", "/(dashboard)/assessment/page": "app/(dashboard)/assessment/page.js", "/(dashboard)/assessment/results/page": "app/(dashboard)/assessment/results/page.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/(dashboard)/onboarding/page": "app/(dashboard)/onboarding/page.js", "/(dashboard)/planning/page": "app/(dashboard)/planning/page.js", "/(dashboard)/planning/session/page": "app/(dashboard)/planning/session/page.js", "/(dashboard)/profile/page": "app/(dashboard)/profile/page.js", "/(dashboard)/progress/page": "app/(dashboard)/progress/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/[locale]/analytics/page": "app/[locale]/analytics/page.js", "/[locale]/assessment/page": "app/[locale]/assessment/page.js", "/[locale]/dashboard/page": "app/[locale]/dashboard/page.js", "/[locale]/login/page": "app/[locale]/login/page.js", "/[locale]/onboarding/page": "app/[locale]/onboarding/page.js", "/[locale]/page": "app/[locale]/page.js", "/[locale]/planning/page": "app/[locale]/planning/page.js", "/[locale]/profile/page": "app/[locale]/profile/page.js", "/[locale]/progress/page": "app/[locale]/progress/page.js", "/[locale]/register/page": "app/[locale]/register/page.js", "/[locale]/reviews/page": "app/[locale]/reviews/page.js", "/[locale]/debug/page": "app/[locale]/debug/page.js"}