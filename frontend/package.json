{"name": "8760-hours-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "npm run test:unit && npm run test:e2e", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:unit && npm run test:api", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test:e2e:chromium": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "playwright test --project=webkit", "test:e2e:mobile": "playwright test --project=\"Mobile Chrome\"", "test:accessibility": "playwright test --grep @accessibility", "test:api": "node test-runner.js", "test:unit": "jest tests/unit", "test:behave": "cd tests/behave && behave", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lighthouse": "lhci autorun", "docker:dev": "../scripts/docker-dev.sh", "docker:start": "../scripts/docker-dev.sh start", "docker:stop": "../scripts/docker-dev.sh stop", "docker:restart": "../scripts/docker-dev.sh restart", "docker:status": "../scripts/docker-dev.sh status", "docker:logs": "../scripts/docker-dev.sh logs", "docker:cleanup": "../scripts/docker-dev.sh cleanup", "db:migrate": "echo 'Database migrations would run here'", "test:service-urls": "node ../scripts/test-service-urls.js"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "next": "14.0.4", "next-intl": "^3.26.5", "next-themes": "^0.4.6", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-intl": "^6.8.9", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@faker-js/faker": "^9.8.0", "@lhci/cli": "^0.12.0", "@playwright/test": "^1.40.1", "@storybook/addon-essentials": "^7.5.3", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "^7.5.3", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.5.3", "@storybook/nextjs": "^7.5.3", "@storybook/react": "^7.5.3", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "axe-core": "^4.10.3", "eslint": "^8.54.0", "eslint-config-next": "14.0.4", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "storybook": "^7.5.3", "tailwindcss": "^3.3.5", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}