/**
 * Integration tests for task store with API endpoints.
 * 
 * These tests verify that the task store properly integrates with
 * the backend API and handles state management correctly.
 */

import { useTaskStore } from '@/stores/task-store';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Mock the API client
jest.mock('@/lib/api-client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

const { apiClient } = require('@/lib/api-client');

// Test data
const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Test Task 1',
    description: 'First test task',
    status: TaskStatus.TODO,
    priority: TaskPriority.HIGH,
    estimatedHours: 2,
    actualHours: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['test'],
  },
  {
    id: '2',
    title: 'Test Task 2',
    description: 'Second test task',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 4,
    actualHours: 1.5,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    tags: ['test', 'important'],
  },
];

describe('Task Store Integration Tests', () => {
  let store: ReturnType<typeof useTaskStore>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Get fresh store instance
    store = useTaskStore.getState();
    store.reset();
    
    // Setup default API responses
    apiClient.get.mockResolvedValue({ data: mockTasks });
    apiClient.post.mockResolvedValue({ data: { id: '3', ...mockTasks[0] } });
    apiClient.put.mockResolvedValue({ data: mockTasks[0] });
    apiClient.delete.mockResolvedValue({ data: { success: true } });
  });

  describe('Task Loading', () => {
    it('loads tasks from API and updates store', async () => {
      await store.loadTasks();

      expect(apiClient.get).toHaveBeenCalledWith('tasks', 'list', expect.any(Object));
      expect(store.tasks).toEqual(mockTasks);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('handles API errors during loading', async () => {
      const errorMessage = 'Failed to load tasks';
      apiClient.get.mockRejectedValue(new Error(errorMessage));

      await store.loadTasks();

      expect(store.tasks).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBe(errorMessage);
    });

    it('sets loading state during API call', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      apiClient.get.mockReturnValue(promise);

      const loadPromise = store.loadTasks();
      
      // Check loading state is set
      expect(store.isLoading).toBe(true);
      expect(store.error).toBeNull();

      // Resolve the API call
      resolvePromise!({ data: mockTasks });
      await loadPromise;

      // Check loading state is cleared
      expect(store.isLoading).toBe(false);
    });

    it('loads tasks with filters and pagination', async () => {
      const filters = {
        status: [TaskStatus.TODO],
        priority: [TaskPriority.HIGH],
        search: 'test',
      };

      store.setFilters(filters);
      await store.loadTasks();

      expect(apiClient.get).toHaveBeenCalledWith('tasks', 'list', expect.objectContaining({
        status: 'todo',
        priority: 'high',
        search: 'test',
        limit: 20,
        offset: 0,
      }));
    });
  });

  describe('Task Creation', () => {
    it('creates task via API and updates store', async () => {
      const newTaskData = {
        title: 'New Task',
        description: 'New task description',
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        estimatedHours: 3,
        tags: ['new'],
      };

      const createdTask = { id: '3', ...newTaskData, actualHours: 0, createdAt: new Date(), updatedAt: new Date() };
      apiClient.post.mockResolvedValue({ data: createdTask });

      await store.createTask(newTaskData);

      expect(apiClient.post).toHaveBeenCalledWith('tasks', 'create', newTaskData);
      expect(store.tasks).toContainEqual(createdTask);
    });

    it('handles task creation errors', async () => {
      const errorMessage = 'Failed to create task';
      apiClient.post.mockRejectedValue(new Error(errorMessage));

      const newTaskData = {
        title: 'New Task',
        status: TaskStatus.TODO,
        priority: TaskPriority.MEDIUM,
        estimatedHours: 0,
        tags: [],
      };

      await expect(store.createTask(newTaskData)).rejects.toThrow(errorMessage);
      expect(store.error).toBe(errorMessage);
    });

    it('optimistically adds task before API response', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      apiClient.post.mockReturnValue(promise);

      const newTaskData = {
        title: 'New Task',
        status: TaskStatus.TODO,
        priority: TaskPriority.MEDIUM,
        estimatedHours: 0,
        tags: [],
      };

      // Start creation
      const createPromise = store.createTask(newTaskData);

      // Check optimistic update (if implemented)
      // This would depend on the store implementation

      // Resolve API call
      const createdTask = { id: '3', ...newTaskData, actualHours: 0, createdAt: new Date(), updatedAt: new Date() };
      resolvePromise!({ data: createdTask });
      await createPromise;

      expect(store.tasks).toContainEqual(createdTask);
    });
  });

  describe('Task Updates', () => {
    beforeEach(async () => {
      // Load initial tasks
      await store.loadTasks();
    });

    it('updates task status via API', async () => {
      const taskId = '1';
      const newStatus = TaskStatus.DONE;

      await store.updateTaskStatus(taskId, newStatus);

      expect(apiClient.put).toHaveBeenCalledWith('tasks', 'update', {
        status: newStatus,
      }, { id: taskId });

      // Check task is updated in store
      const updatedTask = store.tasks.find(t => t.id === taskId);
      expect(updatedTask?.status).toBe(newStatus);
    });

    it('updates full task via API', async () => {
      const taskId = '1';
      const updateData = {
        title: 'Updated Task',
        description: 'Updated description',
        priority: TaskPriority.LOW,
      };

      await store.updateTask(taskId, updateData);

      expect(apiClient.put).toHaveBeenCalledWith('tasks', 'update', updateData, { id: taskId });

      // Check task is updated in store
      const updatedTask = store.tasks.find(t => t.id === taskId);
      expect(updatedTask?.title).toBe('Updated Task');
      expect(updatedTask?.description).toBe('Updated description');
      expect(updatedTask?.priority).toBe(TaskPriority.LOW);
    });

    it('handles update errors and reverts optimistic changes', async () => {
      const taskId = '1';
      const originalTask = store.tasks.find(t => t.id === taskId)!;
      const newStatus = TaskStatus.DONE;

      apiClient.put.mockRejectedValue(new Error('Update failed'));

      await expect(store.updateTaskStatus(taskId, newStatus)).rejects.toThrow('Update failed');

      // Check task is reverted to original state
      const task = store.tasks.find(t => t.id === taskId);
      expect(task?.status).toBe(originalTask.status);
      expect(store.error).toBe('Update failed');
    });
  });

  describe('Task Deletion', () => {
    beforeEach(async () => {
      // Load initial tasks
      await store.loadTasks();
    });

    it('deletes task via API and removes from store', async () => {
      const taskId = '1';
      const initialTaskCount = store.tasks.length;

      await store.deleteTask(taskId);

      expect(apiClient.delete).toHaveBeenCalledWith('tasks', 'delete', { id: taskId });
      expect(store.tasks).toHaveLength(initialTaskCount - 1);
      expect(store.tasks.find(t => t.id === taskId)).toBeUndefined();
    });

    it('handles deletion errors and restores task', async () => {
      const taskId = '1';
      const originalTask = store.tasks.find(t => t.id === taskId)!;

      apiClient.delete.mockRejectedValue(new Error('Delete failed'));

      await expect(store.deleteTask(taskId)).rejects.toThrow('Delete failed');

      // Check task is still in store
      expect(store.tasks.find(t => t.id === taskId)).toEqual(originalTask);
      expect(store.error).toBe('Delete failed');
    });
  });

  describe('Filtering and Search', () => {
    beforeEach(async () => {
      await store.loadTasks();
    });

    it('applies filters correctly', () => {
      const filters = {
        status: [TaskStatus.TODO],
        priority: [TaskPriority.HIGH],
      };

      store.setFilters(filters);
      const filteredTasks = store.getFilteredTasks();

      expect(filteredTasks).toHaveLength(1);
      expect(filteredTasks[0].id).toBe('1');
    });

    it('applies search filter', () => {
      store.setFilters({ search: 'Second' });
      const filteredTasks = store.getFilteredTasks();

      expect(filteredTasks).toHaveLength(1);
      expect(filteredTasks[0].id).toBe('2');
    });

    it('combines multiple filters', () => {
      store.setFilters({
        status: [TaskStatus.IN_PROGRESS],
        search: 'test',
      });
      const filteredTasks = store.getFilteredTasks();

      expect(filteredTasks).toHaveLength(1);
      expect(filteredTasks[0].id).toBe('2');
    });

    it('clears filters', () => {
      store.setFilters({ status: [TaskStatus.TODO] });
      expect(store.getFilteredTasks()).toHaveLength(1);

      store.clearFilters();
      expect(store.getFilteredTasks()).toHaveLength(2);
    });
  });

  describe('Statistics and Analytics', () => {
    beforeEach(async () => {
      await store.loadTasks();
    });

    it('calculates task statistics', () => {
      const stats = store.getTaskStatistics();

      expect(stats.total).toBe(2);
      expect(stats.completed).toBe(0);
      expect(stats.inProgress).toBe(1);
      expect(stats.todo).toBe(1);
      expect(stats.totalEstimatedHours).toBe(6);
      expect(stats.totalActualHours).toBe(1.5);
    });

    it('loads analytics from API', async () => {
      const mockAnalytics = {
        completionRate: 0.5,
        averageCompletionTime: 2.5,
        productivityTrend: 'increasing',
      };

      apiClient.get.mockResolvedValue({ data: mockAnalytics });

      const analytics = await store.getTaskAnalytics();

      expect(apiClient.get).toHaveBeenCalledWith('tasks', 'analytics');
      expect(analytics).toEqual(mockAnalytics);
    });
  });

  describe('Concurrent Operations', () => {
    beforeEach(async () => {
      await store.loadTasks();
    });

    it('handles concurrent updates correctly', async () => {
      const taskId = '1';
      
      // Start multiple concurrent updates
      const update1 = store.updateTaskStatus(taskId, TaskStatus.IN_PROGRESS);
      const update2 = store.updateTask(taskId, { title: 'Updated Title' });

      await Promise.all([update1, update2]);

      // Verify both API calls were made
      expect(apiClient.put).toHaveBeenCalledTimes(2);
    });

    it('handles race conditions in task creation', async () => {
      const taskData1 = { title: 'Task 1', status: TaskStatus.TODO, priority: TaskPriority.MEDIUM, estimatedHours: 0, tags: [] };
      const taskData2 = { title: 'Task 2', status: TaskStatus.TODO, priority: TaskPriority.MEDIUM, estimatedHours: 0, tags: [] };

      // Mock different response times
      apiClient.post.mockImplementation((url, data) => {
        const delay = data.title === 'Task 1' ? 100 : 50;
        return new Promise(resolve => 
          setTimeout(() => resolve({ 
            data: { id: data.title === 'Task 1' ? '3' : '4', ...data, actualHours: 0, createdAt: new Date(), updatedAt: new Date() }
          }), delay)
        );
      });

      // Start concurrent creations
      const create1 = store.createTask(taskData1);
      const create2 = store.createTask(taskData2);

      await Promise.all([create1, create2]);

      // Verify both tasks are in store
      expect(store.tasks).toHaveLength(4); // 2 initial + 2 created
      expect(store.tasks.find(t => t.title === 'Task 1')).toBeDefined();
      expect(store.tasks.find(t => t.title === 'Task 2')).toBeDefined();
    });
  });

  describe('Error Recovery', () => {
    it('recovers from network errors', async () => {
      // First call fails, second succeeds
      apiClient.get.mockRejectedValueOnce(new Error('Network error'))
                 .mockResolvedValueOnce({ data: mockTasks });

      // First attempt fails
      await store.loadTasks();
      expect(store.error).toBe('Network error');
      expect(store.tasks).toEqual([]);

      // Second attempt succeeds
      await store.loadTasks();
      expect(store.error).toBeNull();
      expect(store.tasks).toEqual(mockTasks);
    });

    it('maintains data consistency during partial failures', async () => {
      await store.loadTasks();
      const initialTasks = [...store.tasks];

      // Mock partial failure scenario
      apiClient.put.mockRejectedValue(new Error('Update failed'));

      try {
        await store.updateTaskStatus('1', TaskStatus.DONE);
      } catch (error) {
        // Verify store state is consistent
        expect(store.tasks).toEqual(initialTasks);
        expect(store.error).toBe('Update failed');
      }
    });
  });
});
