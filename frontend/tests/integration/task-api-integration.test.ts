/**
 * Integration tests for task API endpoints.
 * 
 * These tests verify that the task API endpoints work correctly
 * and handle various scenarios properly.
 */

import { apiClient } from '@/lib/api-client';
import { Task, TaskStatus, TaskPriority, CreateTaskRequest, UpdateTaskRequest } from '@/types/task';

// Mock fetch for testing
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Test data
const mockTask: Task = {
  id: '1',
  title: 'Test Task',
  description: 'Test task description',
  status: TaskStatus.TODO,
  priority: TaskPriority.HIGH,
  estimatedHours: 2,
  actualHours: 0,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  tags: ['test'],
};

const mockTasks: Task[] = [
  mockTask,
  {
    id: '2',
    title: 'Second Task',
    description: 'Second test task',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 4,
    actualHours: 1.5,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    tags: ['test', 'important'],
  },
];

// Helper function to create mock response
function createMockResponse(data: any, status = 200) {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  } as Response);
}

describe('Task API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/tasks', () => {
    it('fetches all tasks successfully', async () => {
      mockFetch.mockResolvedValue(createMockResponse(mockTasks));

      const response = await apiClient.get('/api/tasks');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/tasks'),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      );

      expect(response.data).toEqual(mockTasks);
    });

    it('fetches tasks with query parameters', async () => {
      mockFetch.mockResolvedValue(createMockResponse(mockTasks));

      const params = {
        status: [TaskStatus.TODO],
        priority: [TaskPriority.HIGH],
        search: 'test',
        page: 1,
        limit: 10,
      };

      await apiClient.get('/api/tasks', { params });

      const expectedUrl = expect.stringMatching(/\/api\/tasks\?.*status=todo.*priority=high.*search=test.*page=1.*limit=10/);
      expect(mockFetch).toHaveBeenCalledWith(
        expectedUrl,
        expect.any(Object)
      );
    });

    it('handles API errors gracefully', async () => {
      const errorResponse = { error: 'Internal server error' };
      mockFetch.mockResolvedValue(createMockResponse(errorResponse, 500));

      await expect(apiClient.get('/api/tasks')).rejects.toThrow();
    });

    it('handles network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(apiClient.get('/api/tasks')).rejects.toThrow('Network error');
    });

    it('includes authentication headers when available', async () => {
      // Mock authentication token
      const mockToken = 'mock-jwt-token';
      localStorage.setItem('auth-token', mockToken);

      mockFetch.mockResolvedValue(createMockResponse(mockTasks));

      await apiClient.get('/api/tasks');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockToken}`,
          }),
        })
      );

      localStorage.removeItem('auth-token');
    });
  });

  describe('POST /api/tasks', () => {
    it('creates a new task successfully', async () => {
      const newTaskData: CreateTaskRequest = {
        title: 'New Task',
        description: 'New task description',
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        estimatedHours: 3,
        tags: ['new'],
      };

      const createdTask = { id: '3', ...newTaskData, actualHours: 0, createdAt: new Date(), updatedAt: new Date() };
      mockFetch.mockResolvedValue(createMockResponse(createdTask, 201));

      const response = await apiClient.post('/api/tasks', newTaskData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/tasks'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(newTaskData),
        })
      );

      expect(response.data).toEqual(createdTask);
    });

    it('handles validation errors', async () => {
      const invalidTaskData = {
        title: '', // Invalid: empty title
        status: TaskStatus.TODO,
        priority: TaskPriority.MEDIUM,
        estimatedHours: 0,
        tags: [],
      };

      const errorResponse = {
        error: 'Validation failed',
        details: { title: 'Title is required' },
      };
      mockFetch.mockResolvedValue(createMockResponse(errorResponse, 400));

      await expect(apiClient.post('/api/tasks', invalidTaskData)).rejects.toThrow();
    });

    it('handles server errors during creation', async () => {
      const newTaskData: CreateTaskRequest = {
        title: 'New Task',
        status: TaskStatus.TODO,
        priority: TaskPriority.MEDIUM,
        estimatedHours: 0,
        tags: [],
      };

      mockFetch.mockResolvedValue(createMockResponse({ error: 'Database error' }, 500));

      await expect(apiClient.post('/api/tasks', newTaskData)).rejects.toThrow();
    });
  });

  describe('PUT /api/tasks/:id', () => {
    it('updates a task successfully', async () => {
      const taskId = '1';
      const updateData: UpdateTaskRequest = {
        title: 'Updated Task',
        description: 'Updated description',
        priority: TaskPriority.LOW,
      };

      const updatedTask = { ...mockTask, ...updateData };
      mockFetch.mockResolvedValue(createMockResponse(updatedTask));

      const response = await apiClient.put(`/api/tasks/${taskId}`, updateData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining(`/api/tasks/${taskId}`),
        expect.objectContaining({
          method: 'PUT',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(updateData),
        })
      );

      expect(response.data).toEqual(updatedTask);
    });

    it('updates task status only', async () => {
      const taskId = '1';
      const statusUpdate = { status: TaskStatus.DONE };

      const updatedTask = { ...mockTask, status: TaskStatus.DONE };
      mockFetch.mockResolvedValue(createMockResponse(updatedTask));

      const response = await apiClient.put(`/api/tasks/${taskId}`, statusUpdate);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining(`/api/tasks/${taskId}`),
        expect.objectContaining({
          body: JSON.stringify(statusUpdate),
        })
      );

      expect(response.data.status).toBe(TaskStatus.DONE);
    });

    it('handles task not found errors', async () => {
      const taskId = 'nonexistent';
      const updateData = { title: 'Updated Task' };

      mockFetch.mockResolvedValue(createMockResponse({ error: 'Task not found' }, 404));

      await expect(apiClient.put(`/api/tasks/${taskId}`, updateData)).rejects.toThrow();
    });

    it('handles concurrent update conflicts', async () => {
      const taskId = '1';
      const updateData = { title: 'Updated Task' };

      const conflictResponse = {
        error: 'Conflict',
        message: 'Task was modified by another user',
      };
      mockFetch.mockResolvedValue(createMockResponse(conflictResponse, 409));

      await expect(apiClient.put(`/api/tasks/${taskId}`, updateData)).rejects.toThrow();
    });
  });

  describe('DELETE /api/tasks/:id', () => {
    it('deletes a task successfully', async () => {
      const taskId = '1';

      mockFetch.mockResolvedValue(createMockResponse({ success: true }));

      const response = await apiClient.delete(`/api/tasks/${taskId}`);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining(`/api/tasks/${taskId}`),
        expect.objectContaining({
          method: 'DELETE',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      );

      expect(response.data.success).toBe(true);
    });

    it('handles task not found during deletion', async () => {
      const taskId = 'nonexistent';

      mockFetch.mockResolvedValue(createMockResponse({ error: 'Task not found' }, 404));

      await expect(apiClient.delete(`/api/tasks/${taskId}`)).rejects.toThrow();
    });

    it('handles deletion of task with dependencies', async () => {
      const taskId = '1';

      const errorResponse = {
        error: 'Cannot delete task',
        message: 'Task has active time entries',
      };
      mockFetch.mockResolvedValue(createMockResponse(errorResponse, 400));

      await expect(apiClient.delete(`/api/tasks/${taskId}`)).rejects.toThrow();
    });
  });

  describe('GET /api/tasks/analytics', () => {
    it('fetches task analytics successfully', async () => {
      const mockAnalytics = {
        totalTasks: 10,
        completedTasks: 6,
        completionRate: 0.6,
        averageCompletionTime: 2.5,
        productivityTrend: 'increasing',
        tasksByStatus: {
          [TaskStatus.TODO]: 2,
          [TaskStatus.IN_PROGRESS]: 2,
          [TaskStatus.DONE]: 6,
          [TaskStatus.BLOCKED]: 0,
          [TaskStatus.DEFERRED]: 0,
        },
        tasksByPriority: {
          [TaskPriority.HIGH]: 3,
          [TaskPriority.MEDIUM]: 4,
          [TaskPriority.LOW]: 3,
        },
      };

      mockFetch.mockResolvedValue(createMockResponse(mockAnalytics));

      const response = await apiClient.get('/api/tasks/analytics');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/tasks/analytics'),
        expect.objectContaining({
          method: 'GET',
        })
      );

      expect(response.data).toEqual(mockAnalytics);
    });

    it('fetches analytics with date range', async () => {
      const dateRange = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      };

      mockFetch.mockResolvedValue(createMockResponse({}));

      await apiClient.get('/api/tasks/analytics', { params: dateRange });

      const expectedUrl = expect.stringMatching(/\/api\/tasks\/analytics\?.*startDate=2024-01-01.*endDate=2024-01-31/);
      expect(mockFetch).toHaveBeenCalledWith(expectedUrl, expect.any(Object));
    });
  });

  describe('Request/Response Handling', () => {
    it('handles request timeouts', async () => {
      mockFetch.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      await expect(apiClient.get('/api/tasks')).rejects.toThrow('Request timeout');
    });

    it('handles malformed JSON responses', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.reject(new Error('Invalid JSON')),
        text: () => Promise.resolve('Invalid JSON response'),
      } as Response);

      await expect(apiClient.get('/api/tasks')).rejects.toThrow();
    });

    it('includes proper headers for all requests', async () => {
      mockFetch.mockResolvedValue(createMockResponse({}));

      await apiClient.get('/api/tasks');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }),
        })
      );
    });

    it('handles rate limiting responses', async () => {
      const rateLimitResponse = {
        error: 'Rate limit exceeded',
        retryAfter: 60,
      };
      mockFetch.mockResolvedValue(createMockResponse(rateLimitResponse, 429));

      await expect(apiClient.get('/api/tasks')).rejects.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('provides detailed error information', async () => {
      const errorResponse = {
        error: 'Validation failed',
        details: {
          title: 'Title is required',
          estimatedHours: 'Must be a positive number',
        },
        code: 'VALIDATION_ERROR',
      };
      mockFetch.mockResolvedValue(createMockResponse(errorResponse, 400));

      try {
        await apiClient.post('/api/tasks', {});
      } catch (error: any) {
        expect(error.message).toContain('Validation failed');
        expect(error.details).toEqual(errorResponse.details);
        expect(error.code).toBe('VALIDATION_ERROR');
      }
    });

    it('handles different HTTP error codes appropriately', async () => {
      const testCases = [
        { status: 400, error: 'Bad Request' },
        { status: 401, error: 'Unauthorized' },
        { status: 403, error: 'Forbidden' },
        { status: 404, error: 'Not Found' },
        { status: 500, error: 'Internal Server Error' },
      ];

      for (const testCase of testCases) {
        mockFetch.mockResolvedValue(createMockResponse({ error: testCase.error }, testCase.status));

        await expect(apiClient.get('/api/tasks')).rejects.toThrow();
      }
    });
  });
});
