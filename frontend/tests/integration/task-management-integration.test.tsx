/**
 * Integration tests for task management UI components with API endpoints.
 * 
 * These tests verify that the task management components properly integrate
 * with the backend API endpoints and handle data flow correctly.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TaskList } from '@/components/tasks/task-list';
import { TaskForm } from '@/components/tasks/task-form';
import { useTaskStore } from '@/stores/task-store';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Mock the API client
jest.mock('@/lib/api-client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/tasks',
}));

const { apiClient } = require('@/lib/api-client');

// Test data
const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Test Task 1',
    description: 'First test task',
    status: TaskStatus.TODO,
    priority: TaskPriority.HIGH,
    estimatedHours: 2,
    actualHours: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['test'],
  },
  {
    id: '2',
    title: 'Test Task 2',
    description: 'Second test task',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 4,
    actualHours: 1.5,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    tags: ['test', 'important'],
  },
];

// Test wrapper component
function TestWrapper({ children }: { children: React.ReactNode }) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

describe('Task Management Integration Tests', () => {
  let mockStore: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset the store
    mockStore = useTaskStore.getState();
    mockStore.reset();
    
    // Setup default API responses
    apiClient.get.mockResolvedValue({ data: mockTasks });
    apiClient.post.mockResolvedValue({ data: { id: '3', ...mockTasks[0] } });
    apiClient.put.mockResolvedValue({ data: mockTasks[0] });
    apiClient.delete.mockResolvedValue({ data: { success: true } });
  });

  describe('TaskList Integration', () => {
    it('loads tasks from API on mount', async () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for API call
      await waitFor(() => {
        expect(apiClient.get).toHaveBeenCalledWith('/api/tasks', expect.any(Object));
      });

      // Verify tasks are displayed
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
        expect(screen.getByText('Test Task 2')).toBeInTheDocument();
      });
    });

    it('handles API errors gracefully', async () => {
      apiClient.get.mockRejectedValue(new Error('API Error'));

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Error loading tasks')).toBeInTheDocument();
        expect(screen.getByText('API Error')).toBeInTheDocument();
      });
    });

    it('retries loading tasks when retry button clicked', async () => {
      apiClient.get.mockRejectedValueOnce(new Error('API Error'))
                 .mockResolvedValueOnce({ data: mockTasks });

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText('Error loading tasks')).toBeInTheDocument();
      });

      // Click retry button
      const retryButton = screen.getByText('Try Again');
      await userEvent.click(retryButton);

      // Verify tasks load successfully
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      expect(apiClient.get).toHaveBeenCalledTimes(2);
    });

    it('filters tasks via API when search is used', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Enter search query
      const searchInput = screen.getByTestId('task-search-input');
      await user.type(searchInput, 'important');

      // Verify API is called with search parameters
      await waitFor(() => {
        expect(apiClient.get).toHaveBeenCalledWith('/api/tasks', {
          params: expect.objectContaining({
            search: 'important',
          }),
        });
      });
    });

    it('applies filters via API', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Open filters
      const filtersButton = screen.getByTestId('filters-toggle-button');
      await user.click(filtersButton);

      // Select status filter
      const todoFilter = screen.getByTestId('status-filter-todo');
      await user.click(todoFilter);

      // Apply filters
      const applyButton = screen.getByTestId('apply-filters-button');
      await user.click(applyButton);

      // Verify API is called with filter parameters
      await waitFor(() => {
        expect(apiClient.get).toHaveBeenCalledWith('/api/tasks', {
          params: expect.objectContaining({
            status: ['todo'],
          }),
        });
      });
    });
  });

  describe('Task Creation Integration', () => {
    it('creates task via API and updates list', async () => {
      const user = userEvent.setup();
      const newTask = {
        id: '3',
        title: 'New Task',
        description: 'New task description',
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        estimatedHours: 3,
        actualHours: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: ['new'],
      };

      apiClient.post.mockResolvedValue({ data: newTask });

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Open create form
      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);

      // Fill form
      const titleInput = screen.getByTestId('task-title-input');
      await user.type(titleInput, 'New Task');

      const descInput = screen.getByTestId('task-description-input');
      await user.type(descInput, 'New task description');

      // Submit form
      const saveButton = screen.getByTestId('save-task-button');
      await user.click(saveButton);

      // Verify API call
      await waitFor(() => {
        expect(apiClient.post).toHaveBeenCalledWith('/api/tasks', {
          title: 'New Task',
          description: 'New task description',
          status: TaskStatus.TODO,
          priority: TaskPriority.MEDIUM,
          estimatedHours: 0,
          tags: [],
          dueDate: undefined,
          goalId: undefined,
          lifeAreaId: undefined,
          parentTaskId: undefined,
        });
      });

      // Verify task list is refreshed
      expect(apiClient.get).toHaveBeenCalledTimes(2); // Initial load + refresh
    });

    it('handles task creation errors', async () => {
      const user = userEvent.setup();
      apiClient.post.mockRejectedValue(new Error('Creation failed'));

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Open create form
      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);

      // Fill and submit form
      const titleInput = screen.getByTestId('task-title-input');
      await user.type(titleInput, 'New Task');

      const saveButton = screen.getByTestId('save-task-button');
      await user.click(saveButton);

      // Verify error is shown
      await waitFor(() => {
        expect(screen.getByText('Failed to save task. Please try again.')).toBeInTheDocument();
      });
    });
  });

  describe('Task Update Integration', () => {
    it('updates task status via API', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for tasks to load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Click status toggle
      const statusToggle = screen.getAllByTestId('task-status-toggle')[0];
      await user.click(statusToggle);

      // Verify API call
      await waitFor(() => {
        expect(apiClient.put).toHaveBeenCalledWith('/api/tasks/1', {
          status: TaskStatus.DONE,
        });
      });
    });

    it('updates task details via API', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for tasks to load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Click edit button
      const editButton = screen.getAllByTestId('task-edit-button')[0];
      await user.click(editButton);

      // Update title
      const titleInput = screen.getByTestId('task-title-input');
      await user.clear(titleInput);
      await user.type(titleInput, 'Updated Task');

      // Save changes
      const saveButton = screen.getByTestId('save-task-button');
      await user.click(saveButton);

      // Verify API call
      await waitFor(() => {
        expect(apiClient.put).toHaveBeenCalledWith('/api/tasks/1', expect.objectContaining({
          title: 'Updated Task',
        }));
      });
    });
  });

  describe('Task Deletion Integration', () => {
    it('deletes task via API and updates list', async () => {
      const user = userEvent.setup();
      
      // Mock window.confirm
      const mockConfirm = jest.fn().mockReturnValue(true);
      Object.defineProperty(window, 'confirm', { value: mockConfirm });

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for tasks to load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Click delete button
      const deleteButton = screen.getAllByTestId('task-delete-button')[0];
      await user.click(deleteButton);

      // Verify API call
      await waitFor(() => {
        expect(apiClient.delete).toHaveBeenCalledWith('/api/tasks/1');
      });

      // Verify task list is refreshed
      expect(apiClient.get).toHaveBeenCalledTimes(2); // Initial load + refresh
    });
  });

  describe('Real-time Updates', () => {
    it('handles concurrent updates gracefully', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Simulate concurrent status updates
      const statusToggles = screen.getAllByTestId('task-status-toggle');
      
      // Click multiple toggles rapidly
      await user.click(statusToggles[0]);
      await user.click(statusToggles[1]);

      // Verify both API calls are made
      await waitFor(() => {
        expect(apiClient.put).toHaveBeenCalledTimes(2);
      });
    });

    it('handles optimistic updates correctly', async () => {
      const user = userEvent.setup();
      
      // Delay API response to test optimistic updates
      apiClient.put.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ data: mockTasks[0] }), 100))
      );

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Click status toggle
      const statusToggle = screen.getAllByTestId('task-status-toggle')[0];
      await user.click(statusToggle);

      // Verify optimistic update is applied immediately
      // (This would require checking the UI state before API response)
      
      // Wait for API call to complete
      await waitFor(() => {
        expect(apiClient.put).toHaveBeenCalled();
      });
    });
  });

  describe('Error Recovery', () => {
    it('recovers from network errors', async () => {
      const user = userEvent.setup();
      
      // First call fails, second succeeds
      apiClient.get.mockRejectedValueOnce(new Error('Network error'))
                 .mockResolvedValueOnce({ data: mockTasks });

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText('Error loading tasks')).toBeInTheDocument();
      });

      // Retry
      const retryButton = screen.getByText('Try Again');
      await user.click(retryButton);

      // Verify recovery
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });
    });

    it('handles partial failures in batch operations', async () => {
      const user = userEvent.setup();
      
      // Mock mixed success/failure responses
      apiClient.put.mockResolvedValueOnce({ data: mockTasks[0] })
                 .mockRejectedValueOnce(new Error('Update failed'));

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      });

      // Attempt multiple updates
      const statusToggles = screen.getAllByTestId('task-status-toggle');
      await user.click(statusToggles[0]); // Should succeed
      await user.click(statusToggles[1]); // Should fail

      // Verify both attempts were made
      await waitFor(() => {
        expect(apiClient.put).toHaveBeenCalledTimes(2);
      });

      // Verify error handling for failed update
      // (This would require checking for error indicators in the UI)
    });
  });
});
