#!/usr/bin/env node

/**
 * Integration test runner for task management components.
 * 
 * This script runs integration tests that verify the interaction
 * between UI components, stores, and API endpoints.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  testDir: 'tests/integration',
  setupFile: '<rootDir>/tests/setup.ts',
  timeout: 30000,
  verbose: process.argv.includes('--verbose'),
  watch: process.argv.includes('--watch'),
  coverage: process.argv.includes('--coverage'),
  bail: process.argv.includes('--bail'),
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${colors.bright}${colors.blue}${message}${colors.reset}`);
  log('='.repeat(message.length), colors.blue);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.cyan);
}

async function checkPrerequisites() {
  logHeader('Checking Prerequisites');

  // Check if Jest is available
  try {
    const jestVersion = require('jest/package.json').version;
    logSuccess(`Jest v${jestVersion} is available`);
  } catch (error) {
    logError('Jest is not installed. Run: npm install --save-dev jest');
    process.exit(1);
  }

  // Check if test files exist
  const testFiles = [
    'tests/integration/task-management-integration.test.tsx',
    'tests/integration/task-store-integration.test.ts',
    'tests/integration/task-api-integration.test.ts',
  ];

  for (const testFile of testFiles) {
    if (fs.existsSync(testFile)) {
      logSuccess(`Found ${testFile}`);
    } else {
      logError(`Missing test file: ${testFile}`);
      process.exit(1);
    }
  }

  // Check if setup file exists
  if (fs.existsSync(config.setupFile)) {
    logSuccess(`Found setup file: ${config.setupFile}`);
  } else {
    logWarning(`Setup file not found: ${config.setupFile}`);
  }
}

function buildJestCommand() {
  const jestArgs = [
    'jest',
    config.testDir,
    '--testEnvironment=jsdom',
    `--setupFilesAfterEnv=${config.setupFile}`,
    `--testTimeout=${config.timeout}`,
  ];

  if (config.verbose) {
    jestArgs.push('--verbose');
  }

  if (config.watch) {
    jestArgs.push('--watch');
  }

  if (config.coverage) {
    jestArgs.push('--coverage');
    jestArgs.push('--coverageDirectory=coverage/integration');
    jestArgs.push('--collectCoverageFrom=src/**/*.{ts,tsx}');
    jestArgs.push('--coverageReporters=text,lcov,html');
  }

  if (config.bail) {
    jestArgs.push('--bail');
  }

  // Add specific test pattern if provided
  const testPattern = process.argv.find(arg => arg.startsWith('--testNamePattern='));
  if (testPattern) {
    jestArgs.push(testPattern);
  }

  return jestArgs;
}

function runJest() {
  return new Promise((resolve, reject) => {
    logHeader('Running Integration Tests');

    const jestArgs = buildJestCommand();
    logInfo(`Command: npx ${jestArgs.join(' ')}`);

    const jest = spawn('npx', jestArgs, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });

    jest.on('close', (code) => {
      if (code === 0) {
        logSuccess('All integration tests passed!');
        resolve(code);
      } else {
        logError(`Integration tests failed with exit code ${code}`);
        reject(new Error(`Tests failed with exit code ${code}`));
      }
    });

    jest.on('error', (error) => {
      logError(`Failed to start Jest: ${error.message}`);
      reject(error);
    });
  });
}

async function generateReport() {
  if (!config.coverage) return;

  logHeader('Generating Test Report');

  try {
    // Check if coverage directory exists
    if (fs.existsSync('coverage/integration')) {
      logSuccess('Coverage report generated in coverage/integration/');
      
      // Check for HTML report
      if (fs.existsSync('coverage/integration/lcov-report/index.html')) {
        logInfo('HTML coverage report: coverage/integration/lcov-report/index.html');
      }
    } else {
      logWarning('No coverage report generated');
    }
  } catch (error) {
    logError(`Error checking coverage report: ${error.message}`);
  }
}

function showUsage() {
  log(`
${colors.bright}Integration Test Runner${colors.reset}

Usage: node run-integration-tests.js [options]

Options:
  --verbose              Show detailed test output
  --watch               Watch for file changes and re-run tests
  --coverage            Generate code coverage report
  --bail                Stop on first test failure
  --testNamePattern=<pattern>  Run only tests matching pattern

Examples:
  node run-integration-tests.js
  node run-integration-tests.js --verbose --coverage
  node run-integration-tests.js --watch
  node run-integration-tests.js --testNamePattern="Task Creation"
`);
}

async function main() {
  try {
    // Show usage if help requested
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
      showUsage();
      return;
    }

    logHeader('🧪 Task Management Integration Tests');

    // Check prerequisites
    await checkPrerequisites();

    // Run tests
    await runJest();

    // Generate report
    await generateReport();

    logHeader('✨ Integration Tests Complete');

  } catch (error) {
    logError(`Integration tests failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('\n\nReceived SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('\n\nReceived SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runIntegrationTests: main,
  config,
};
