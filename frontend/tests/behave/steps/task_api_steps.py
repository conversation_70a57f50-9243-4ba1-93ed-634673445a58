"""
Step definitions for task management API flow tests.

This module contains the step implementations for testing task management
API workflows using Behave (Gherkin) scenarios.
"""

import requests
import json
import time
from behave import given, when, then
from urllib.parse import urljoin


# Background steps
@given('the API is running and accessible')
def step_api_running(context):
    """Verify the API is accessible."""
    try:
        response = requests.get(f"{context.api_base_url}/health")
        assert response.status_code == 200
    except requests.exceptions.ConnectionError:
        assert False, "API is not accessible"


@given('I have valid authentication credentials')
def step_valid_credentials(context):
    """Set up valid authentication credentials."""
    context.auth_headers = {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
    }


# Task lifecycle steps
@given('I want to test the complete task lifecycle')
def step_test_lifecycle(context):
    """Initialize task lifecycle test."""
    context.created_task_id = None
    context.last_response = None


@when('I create a new task via API with')
def step_create_task_api(context):
    """Create a new task via API with provided data."""
    task_data = {}
    for row in context.table:
        field = row['Field']
        value = row['Value']
        
        if field == 'estimatedHours':
            task_data[field] = float(value)
        elif field == 'tags':
            task_data[field] = [tag.strip() for tag in value.split(',')]
        else:
            task_data[field] = value
    
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    context.last_response = requests.post(
        url, 
        json=task_data, 
        headers=context.auth_headers
    )
    
    if context.last_response.status_code == 201:
        response_data = context.last_response.json()
        context.created_task_id = response_data.get('id')


@then('the API should return status code {status_code:d}')
def step_verify_status_code(context, status_code):
    """Verify the API response status code."""
    assert context.last_response.status_code == status_code, \
        f"Expected {status_code}, got {context.last_response.status_code}. Response: {context.last_response.text}"


@then('the response should contain the created task with an ID')
def step_verify_created_task(context):
    """Verify the response contains a created task with ID."""
    response_data = context.last_response.json()
    assert 'id' in response_data
    assert response_data['id'] is not None
    context.created_task_id = response_data['id']


@then('the task should have status "{status}"')
def step_verify_task_status(context, status):
    """Verify the task has the expected status."""
    response_data = context.last_response.json()
    assert response_data['status'] == status


@when('I retrieve the task by ID via API')
def step_retrieve_task_by_id(context):
    """Retrieve the task by its ID via API."""
    url = urljoin(context.api_base_url, f'/api/v1/tasks/{context.created_task_id}')
    context.last_response = requests.get(url, headers=context.auth_headers)


@then('the response should contain all task details')
def step_verify_task_details(context):
    """Verify the response contains all expected task details."""
    response_data = context.last_response.json()
    required_fields = ['id', 'title', 'status', 'priority', 'estimatedHours', 'actualHours', 'createdAt', 'updatedAt']
    
    for field in required_fields:
        assert field in response_data, f"Missing field: {field}"


@when('I update the task status to "{status}" via API')
def step_update_task_status(context, status):
    """Update the task status via API."""
    url = urljoin(context.api_base_url, f'/api/v1/tasks/{context.created_task_id}')
    update_data = {'status': status}
    context.last_response = requests.put(
        url, 
        json=update_data, 
        headers=context.auth_headers
    )


@then('the task should have an updated timestamp')
def step_verify_updated_timestamp(context):
    """Verify the task has an updated timestamp."""
    response_data = context.last_response.json()
    assert 'updatedAt' in response_data
    # Could add more specific timestamp validation here


@when('I add {hours:f} hours of actual time via API')
def step_add_actual_time(context, hours):
    """Add actual time to the task via API."""
    url = urljoin(context.api_base_url, f'/api/v1/tasks/{context.created_task_id}')
    update_data = {'actualHours': hours}
    context.last_response = requests.put(
        url, 
        json=update_data, 
        headers=context.auth_headers
    )


@then('the task should show {hours:f} actual hours')
def step_verify_actual_hours(context, hours):
    """Verify the task shows the expected actual hours."""
    response_data = context.last_response.json()
    assert response_data['actualHours'] == hours


@when('I mark the task as complete via API')
def step_mark_task_complete(context):
    """Mark the task as complete via API."""
    url = urljoin(context.api_base_url, f'/api/v1/tasks/{context.created_task_id}')
    update_data = {'status': 'done'}
    context.last_response = requests.put(
        url, 
        json=update_data, 
        headers=context.auth_headers
    )


@then('the task should have a completion timestamp')
def step_verify_completion_timestamp(context):
    """Verify the task has a completion timestamp."""
    response_data = context.last_response.json()
    assert 'completedAt' in response_data
    assert response_data['completedAt'] is not None


@when('I delete the task via API')
def step_delete_task(context):
    """Delete the task via API."""
    url = urljoin(context.api_base_url, f'/api/v1/tasks/{context.created_task_id}')
    context.last_response = requests.delete(url, headers=context.auth_headers)


@then('subsequent requests for the task should return 404')
def step_verify_task_deleted(context):
    """Verify the task is deleted by checking for 404."""
    url = urljoin(context.api_base_url, f'/api/v1/tasks/{context.created_task_id}')
    response = requests.get(url, headers=context.auth_headers)
    assert response.status_code == 404


# Pagination and filtering steps
@given('I have created {count:d} tasks via API with different attributes')
def step_create_multiple_tasks(context, count):
    """Create multiple tasks with different attributes."""
    context.created_task_ids = []
    
    for i in range(count):
        task_data = {
            'title': f'Test Task {i+1}',
            'description': f'Description for task {i+1}',
            'priority': ['high', 'medium', 'low'][i % 3],
            'status': ['todo', 'in_progress', 'done'][i % 3],
            'estimatedHours': (i % 5) + 1,
            'tags': [f'tag{i}', 'test']
        }
        
        url = urljoin(context.api_base_url, '/api/v1/tasks')
        response = requests.post(url, json=task_data, headers=context.auth_headers)
        
        if response.status_code == 201:
            task_id = response.json()['id']
            context.created_task_ids.append(task_id)


@when('I request tasks with pagination limit {limit:d} and offset {offset:d}')
def step_request_paginated_tasks(context, limit, offset):
    """Request tasks with pagination parameters."""
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    params = {'limit': limit, 'offset': offset}
    context.last_response = requests.get(url, params=params, headers=context.auth_headers)


@then('the response should contain {count:d} tasks')
def step_verify_task_count(context, count):
    """Verify the response contains the expected number of tasks."""
    response_data = context.last_response.json()
    assert len(response_data['tasks']) == count


@then('the response should indicate hasMore is {has_more}')
def step_verify_has_more(context, has_more):
    """Verify the hasMore pagination indicator."""
    response_data = context.last_response.json()
    expected = has_more.lower() == 'true'
    assert response_data['hasMore'] == expected


@then('the response should show total count of {total:d}')
def step_verify_total_count(context, total):
    """Verify the total count in the response."""
    response_data = context.last_response.json()
    assert response_data['total'] == total


@when('I request the next page with limit {limit:d} and offset {offset:d}')
def step_request_next_page(context, limit, offset):
    """Request the next page of tasks."""
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    params = {'limit': limit, 'offset': offset}
    context.next_page_response = requests.get(url, params=params, headers=context.auth_headers)
    context.last_response = context.next_page_response


@then('the tasks should be different from the first page')
def step_verify_different_tasks(context):
    """Verify the tasks are different from the first page."""
    # This would require storing the first page response and comparing
    # For now, we'll just verify we got a successful response
    assert context.last_response.status_code == 200


@when('I filter tasks by status "{status}" via API')
def step_filter_by_status(context, status):
    """Filter tasks by status via API."""
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    params = {'status': status}
    context.last_response = requests.get(url, params=params, headers=context.auth_headers)


@then('all returned tasks should have status "{status}"')
def step_verify_filtered_status(context, status):
    """Verify all returned tasks have the expected status."""
    response_data = context.last_response.json()
    for task in response_data['tasks']:
        assert task['status'] == status


@when('I filter tasks by priority "{priority}" via API')
def step_filter_by_priority(context, priority):
    """Filter tasks by priority via API."""
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    params = {'priority': priority}
    context.last_response = requests.get(url, params=params, headers=context.auth_headers)


@then('all returned tasks should have priority "{priority}"')
def step_verify_filtered_priority(context, priority):
    """Verify all returned tasks have the expected priority."""
    response_data = context.last_response.json()
    for task in response_data['tasks']:
        assert task['priority'] == priority


@when('I search for tasks containing "{search_term}" via API')
def step_search_tasks(context, search_term):
    """Search for tasks containing the search term."""
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    params = {'search': search_term}
    context.last_response = requests.get(url, params=params, headers=context.auth_headers)


@then('all returned tasks should contain "{search_term}" in title or description')
def step_verify_search_results(context, search_term):
    """Verify all returned tasks contain the search term."""
    response_data = context.last_response.json()
    for task in response_data['tasks']:
        title_match = search_term.lower() in task['title'].lower()
        desc_match = task.get('description', '') and search_term.lower() in task['description'].lower()
        assert title_match or desc_match


# Validation and error handling steps
@when('I try to create a task without a title via API')
def step_create_task_no_title(context):
    """Try to create a task without a title."""
    task_data = {'description': 'Task without title'}
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    context.last_response = requests.post(url, json=task_data, headers=context.auth_headers)


@then('the response should contain validation error for "{field}"')
def step_verify_validation_error(context, field):
    """Verify the response contains a validation error for the specified field."""
    response_data = context.last_response.json()
    assert 'error' in response_data
    assert field.lower() in response_data['error'].lower()


@when('I try to create a task with invalid priority via API')
def step_create_task_invalid_priority(context):
    """Try to create a task with invalid priority."""
    task_data = {
        'title': 'Test Task',
        'priority': 'invalid_priority'
    }
    url = urljoin(context.api_base_url, '/api/v1/tasks')
    context.last_response = requests.post(url, json=task_data, headers=context.auth_headers)


@when('I try to update a non-existent task via API')
def step_update_nonexistent_task(context):
    """Try to update a non-existent task."""
    url = urljoin(context.api_base_url, '/api/v1/tasks/nonexistent-id')
    update_data = {'title': 'Updated Title'}
    context.last_response = requests.put(url, json=update_data, headers=context.auth_headers)


@then('the response should contain "{error_message}" error')
def step_verify_error_message(context, error_message):
    """Verify the response contains the expected error message."""
    response_data = context.last_response.json()
    assert 'error' in response_data
    assert error_message.lower() in response_data['error'].lower()


# Analytics steps
@given('I have tasks with various completion states')
def step_create_tasks_various_states(context):
    """Create tasks with various completion states."""
    context.analytics_task_ids = []
    
    for row in context.table:
        task_data = {
            'title': row['Title'],
            'status': row['Status'],
            'priority': row['Priority'],
            'actualHours': float(row['ActualHours']),
            'lifeAreaId': row['LifeArea']
        }
        
        url = urljoin(context.api_base_url, '/api/v1/tasks')
        response = requests.post(url, json=task_data, headers=context.auth_headers)
        
        if response.status_code == 201:
            task_id = response.json()['id']
            context.analytics_task_ids.append(task_id)


@when('I request task analytics via API')
def step_request_analytics(context):
    """Request task analytics via API."""
    url = urljoin(context.api_base_url, '/api/v1/tasks/analytics')
    context.last_response = requests.get(url, headers=context.auth_headers)


@then('the analytics should show')
def step_verify_analytics_metrics(context):
    """Verify the analytics show expected metrics."""
    response_data = context.last_response.json()
    
    for row in context.table:
        metric = row['Metric']
        expected_value = row['Value']
        
        assert metric in response_data
        if expected_value.replace('.', '').isdigit():
            assert response_data[metric] == float(expected_value)
        else:
            assert str(response_data[metric]) == expected_value


@then('the analytics should include priority breakdown')
def step_verify_priority_breakdown(context):
    """Verify analytics include priority breakdown."""
    response_data = context.last_response.json()
    assert 'tasksByPriority' in response_data
    assert 'high' in response_data['tasksByPriority']
    assert 'medium' in response_data['tasksByPriority']
    assert 'low' in response_data['tasksByPriority']


@then('the analytics should include status breakdown')
def step_verify_status_breakdown(context):
    """Verify analytics include status breakdown."""
    response_data = context.last_response.json()
    assert 'tasksByStatus' in response_data


@then('the analytics should include life area breakdown')
def step_verify_life_area_breakdown(context):
    """Verify analytics include life area breakdown."""
    response_data = context.last_response.json()
    assert 'tasksByLifeArea' in response_data
    assert isinstance(response_data['tasksByLifeArea'], list)


@then('the analytics should include productivity trends')
def step_verify_productivity_trends(context):
    """Verify analytics include productivity trends."""
    response_data = context.last_response.json()
    assert 'productivityTrends' in response_data
    assert isinstance(response_data['productivityTrends'], list)
