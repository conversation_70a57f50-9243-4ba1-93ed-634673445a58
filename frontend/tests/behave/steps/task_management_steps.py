"""
Step definitions for task management BDD tests.

This module contains the step implementations for testing task management
functionality using Behave (Gherkin) scenarios.
"""

from behave import given, when, then, step
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
import time


# Background steps
@given('I am logged into the 8760 Hours application')
def step_logged_in(context):
    """Ensure user is logged into the application."""
    context.driver.get(f"{context.base_url}/login")
    
    # Fill in login form (assuming test user exists)
    email_field = context.driver.find_element(By.NAME, "email")
    password_field = context.driver.find_element(By.NAME, "password")
    
    email_field.send_keys("<EMAIL>")
    password_field.send_keys("testpassword")
    
    login_button = context.driver.find_element(By.XPATH, "//button[@type='submit']")
    login_button.click()
    
    # Wait for redirect to dashboard
    WebDriverWait(context.driver, 10).until(
        EC.url_contains("/dashboard")
    )


@given('I am on the task management page')
def step_on_task_page(context):
    """Navigate to the task management page."""
    context.driver.get(f"{context.base_url}/tasks")
    
    # Wait for page to load
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, "task-list"))
    )


# Task creation steps
@given('I want to create a new task')
def step_want_create_task(context):
    """Prepare to create a new task."""
    pass  # This is just a setup step


@when('I click the "Create Task" button')
def step_click_create_task(context):
    """Click the create task button."""
    create_button = context.driver.find_element(By.XPATH, "//button[contains(text(), 'Create Task')]")
    create_button.click()
    
    # Wait for form to appear
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, "task-form"))
    )


@when('I fill in the task form with')
def step_fill_task_form(context):
    """Fill in the task creation form with provided data."""
    for row in context.table:
        field = row['Field']
        value = row['Value']
        
        if field == 'Title':
            title_field = context.driver.find_element(By.NAME, "title")
            title_field.clear()
            title_field.send_keys(value)
        elif field == 'Description':
            desc_field = context.driver.find_element(By.NAME, "description")
            desc_field.clear()
            desc_field.send_keys(value)
        elif field == 'Priority':
            priority_select = Select(context.driver.find_element(By.NAME, "priority"))
            priority_select.select_by_visible_text(value)
        elif field == 'Estimated Hours':
            hours_field = context.driver.find_element(By.NAME, "estimatedHours")
            hours_field.clear()
            hours_field.send_keys(value)
        elif field == 'Tags':
            tags_field = context.driver.find_element(By.NAME, "tags")
            tags_field.clear()
            tags_field.send_keys(value)


@when('I click "Save Task"')
def step_save_task(context):
    """Save the task form."""
    save_button = context.driver.find_element(By.XPATH, "//button[contains(text(), 'Save Task')]")
    save_button.click()
    
    # Wait for task to be created and form to close
    WebDriverWait(context.driver, 10).until(
        EC.invisibility_of_element_located((By.CLASS_NAME, "task-form"))
    )


@then('I should see the task "{task_name}" in my task list')
def step_see_task_in_list(context, task_name):
    """Verify task appears in the task list."""
    task_element = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.XPATH, f"//div[contains(@class, 'task-item')]//h3[contains(text(), '{task_name}')]"))
    )
    assert task_element.is_displayed()


@then('the task should have status "{status}"')
def step_task_has_status(context, status):
    """Verify task has the expected status."""
    status_element = context.driver.find_element(By.XPATH, f"//span[contains(@class, 'task-status')][contains(text(), '{status}')]")
    assert status_element.is_displayed()


@then('the task should have priority "{priority}"')
def step_task_has_priority(context, priority):
    """Verify task has the expected priority."""
    priority_element = context.driver.find_element(By.XPATH, f"//span[contains(@class, 'task-priority')][contains(text(), '{priority}')]")
    assert priority_element.is_displayed()


@then('the task should have estimated hours "{hours}"')
def step_task_has_estimated_hours(context, hours):
    """Verify task has the expected estimated hours."""
    hours_element = context.driver.find_element(By.XPATH, f"//span[contains(@class, 'estimated-hours')][contains(text(), '{hours}')]")
    assert hours_element.is_displayed()


# Task status update steps
@given('I have a task "{task_name}" with status "{status}"')
def step_have_task_with_status(context, task_name, status):
    """Ensure a task exists with the specified status."""
    # This would typically create test data or verify existing data
    # For now, we'll assume the task exists
    task_element = context.driver.find_element(By.XPATH, f"//div[contains(@class, 'task-item')]//h3[contains(text(), '{task_name}')]")
    assert task_element.is_displayed()


@when('I click on the task "{task_name}"')
def step_click_task(context, task_name):
    """Click on a specific task to edit it."""
    task_element = context.driver.find_element(By.XPATH, f"//div[contains(@class, 'task-item')]//h3[contains(text(), '{task_name}')]")
    task_element.click()
    
    # Wait for edit form or modal to appear
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, "task-edit-form"))
    )


@when('I change the status to "{new_status}"')
def step_change_status(context, new_status):
    """Change the task status."""
    status_select = Select(context.driver.find_element(By.NAME, "status"))
    status_select.select_by_visible_text(new_status)


@when('I save the changes')
def step_save_changes(context):
    """Save the task changes."""
    save_button = context.driver.find_element(By.XPATH, "//button[contains(text(), 'Save')]")
    save_button.click()
    
    # Wait for changes to be saved
    WebDriverWait(context.driver, 10).until(
        EC.invisibility_of_element_located((By.CLASS_NAME, "task-edit-form"))
    )


@then('the task "{task_name}" should have status "{status}"')
def step_verify_task_status(context, task_name, status):
    """Verify the task has the updated status."""
    # Find the task and check its status
    task_row = context.driver.find_element(By.XPATH, f"//div[contains(@class, 'task-item')][.//h3[contains(text(), '{task_name}')]]")
    status_element = task_row.find_element(By.CLASS_NAME, "task-status")
    assert status in status_element.text


@then('the task should show the updated timestamp')
def step_verify_updated_timestamp(context):
    """Verify the task shows an updated timestamp."""
    timestamp_element = context.driver.find_element(By.CLASS_NAME, "task-updated-at")
    assert timestamp_element.is_displayed()
    # Could add more specific timestamp validation here


# Task completion steps
@when('I mark the task "{task_name}" as complete')
def step_mark_task_complete(context, task_name):
    """Mark a task as complete."""
    task_row = context.driver.find_element(By.XPATH, f"//div[contains(@class, 'task-item')][.//h3[contains(text(), '{task_name}')]]")
    complete_button = task_row.find_element(By.CLASS_NAME, "complete-task-button")
    complete_button.click()
    
    # Wait for status update
    time.sleep(1)


@then('the task should have a completion timestamp')
def step_verify_completion_timestamp(context):
    """Verify the task has a completion timestamp."""
    completed_at_element = context.driver.find_element(By.CLASS_NAME, "task-completed-at")
    assert completed_at_element.is_displayed()


@then('the task should be moved to the completed section')
def step_verify_moved_to_completed(context):
    """Verify the task appears in the completed section."""
    completed_section = context.driver.find_element(By.CLASS_NAME, "completed-tasks")
    assert completed_section.is_displayed()


# Filtering steps
@given('I have tasks with different statuses')
def step_have_tasks_different_statuses(context):
    """Ensure tasks with different statuses exist."""
    # This would typically create test data
    # For now, we'll assume the tasks exist as specified in the table
    for row in context.table:
        task_name = row['Task Name']
        status = row['Status']
        # Verify task exists (in a real implementation, we'd create it)
        task_element = context.driver.find_element(By.XPATH, f"//div[contains(@class, 'task-item')]//h3[contains(text(), '{task_name}')]")
        assert task_element.is_displayed()


@when('I filter tasks by status "{status}"')
def step_filter_by_status(context, status):
    """Apply status filter."""
    status_filter = Select(context.driver.find_element(By.NAME, "statusFilter"))
    status_filter.select_by_visible_text(status)
    
    # Wait for filter to be applied
    time.sleep(1)


@then('I should only see tasks with status "{status}"')
def step_verify_filtered_status(context, status):
    """Verify only tasks with the specified status are visible."""
    task_items = context.driver.find_elements(By.CLASS_NAME, "task-item")
    for task_item in task_items:
        if task_item.is_displayed():
            status_element = task_item.find_element(By.CLASS_NAME, "task-status")
            assert status in status_element.text


@then('I should see "{task_name}" in the results')
def step_see_task_in_results(context, task_name):
    """Verify specific task is visible in results."""
    task_element = context.driver.find_element(By.XPATH, f"//div[contains(@class, 'task-item')]//h3[contains(text(), '{task_name}')]")
    assert task_element.is_displayed()


@then('I should not see "{task_name}" in the results')
def step_not_see_task_in_results(context, task_name):
    """Verify specific task is not visible in results."""
    task_elements = context.driver.find_elements(By.XPATH, f"//div[contains(@class, 'task-item')]//h3[contains(text(), '{task_name}')]")
    for element in task_elements:
        assert not element.is_displayed()


# Search steps
@given('I have multiple tasks in my list')
def step_have_multiple_tasks(context):
    """Ensure multiple tasks exist."""
    task_items = context.driver.find_elements(By.CLASS_NAME, "task-item")
    assert len(task_items) > 1


@when('I search for "{search_term}"')
def step_search_tasks(context, search_term):
    """Search for tasks using the search term."""
    search_field = context.driver.find_element(By.NAME, "search")
    search_field.clear()
    search_field.send_keys(search_term)
    
    # Wait for search results
    time.sleep(1)


@then('I should see all tasks containing "{search_term}" in the title')
def step_verify_search_results(context, search_term):
    """Verify search results contain the search term."""
    task_items = context.driver.find_elements(By.CLASS_NAME, "task-item")
    visible_tasks = [item for item in task_items if item.is_displayed()]
    
    for task_item in visible_tasks:
        title_element = task_item.find_element(By.TAG_NAME, "h3")
        assert search_term.lower() in title_element.text.lower()


@then('I should not see unrelated tasks')
def step_verify_no_unrelated_tasks(context):
    """Verify no unrelated tasks are shown."""
    # This is implicitly tested by the previous step
    pass


# Additional helper steps would be added here for:
# - Priority filtering
# - Sorting
# - Task editing
# - Task deletion
# - Analytics viewing
# - Time tracking
# - Dependencies
# - Subtasks
# - Overdue tasks
# - Bulk operations
# - Export functionality
# - Accessibility testing
