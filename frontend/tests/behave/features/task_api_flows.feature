Feature: Task Management API Flows
  As a developer integrating with the 8760 Hours API
  I want to test complete task management workflows
  So that I can ensure the API works correctly for real-world scenarios

  Background:
    Given the API is running and accessible
    And I have valid authentication credentials

  <PERSON><PERSON><PERSON>: Complete task lifecycle via API
    Given I want to test the complete task lifecycle
    When I create a new task via API with:
      | Field           | Value                    |
      | title           | Complete API integration |
      | description     | Integrate task API       |
      | priority        | high                     |
      | estimatedHours  | 8                        |
      | tags            | api, integration        |
    Then the API should return status code 201
    And the response should contain the created task with an ID
    And the task should have status "todo"
    
    When I retrieve the task by ID via API
    Then the API should return status code 200
    And the response should contain all task details
    
    When I update the task status to "in_progress" via API
    Then the API should return status code 200
    And the task should have status "in_progress"
    And the task should have an updated timestamp
    
    When I add 2.5 hours of actual time via API
    Then the API should return status code 200
    And the task should show 2.5 actual hours
    
    When I mark the task as complete via API
    Then the API should return status code 200
    And the task should have status "done"
    And the task should have a completion timestamp
    
    When I delete the task via API
    Then the API should return status code 200
    And subsequent requests for the task should return 404

  Scenario: Task filtering and pagination workflow
    Given I have created 25 tasks via API with different attributes
    When I request tasks with pagination limit 10 and offset 0
    Then the API should return status code 200
    And the response should contain 10 tasks
    And the response should indicate hasMore is true
    And the response should show total count of 25
    
    When I request the next page with limit 10 and offset 10
    Then the API should return status code 200
    And the response should contain 10 tasks
    And the tasks should be different from the first page
    
    When I filter tasks by status "todo" via API
    Then the API should return status code 200
    And all returned tasks should have status "todo"
    
    When I filter tasks by priority "high" via API
    Then the API should return status code 200
    And all returned tasks should have priority "high"
    
    When I search for tasks containing "integration" via API
    Then the API should return status code 200
    And all returned tasks should contain "integration" in title or description

  Scenario: Task validation and error handling
    When I try to create a task without a title via API
    Then the API should return status code 400
    And the response should contain validation error for "title"
    
    When I try to create a task with invalid priority via API
    Then the API should return status code 400
    And the response should contain validation error for "priority"
    
    When I try to update a non-existent task via API
    Then the API should return status code 404
    And the response should contain "Task not found" error
    
    When I try to delete a non-existent task via API
    Then the API should return status code 404
    And the response should contain "Task not found" error
    
    When I try to update a task with negative hours via API
    Then the API should return status code 400
    And the response should contain validation error for hours

  Scenario: Task analytics API workflow
    Given I have tasks with various completion states:
      | Title           | Status      | Priority | ActualHours | LifeArea |
      | Task 1          | done        | high     | 2.5         | career   |
      | Task 2          | done        | medium   | 1.0         | career   |
      | Task 3          | in_progress | high     | 0.5         | health   |
      | Task 4          | todo        | low      | 0.0         | health   |
      | Task 5          | blocked     | medium   | 0.0         | personal |
    When I request task analytics via API
    Then the API should return status code 200
    And the analytics should show:
      | Metric              | Value |
      | totalTasks          | 5     |
      | completedTasks      | 2     |
      | inProgressTasks     | 1     |
      | totalTimeSpent      | 4.0   |
    And the analytics should include priority breakdown
    And the analytics should include status breakdown
    And the analytics should include life area breakdown
    And the analytics should include productivity trends

  Scenario: Concurrent task operations
    Given I have a task created via API
    When I simultaneously update the task from two different clients
    Then one update should succeed with status code 200
    And the other update should handle the conflict appropriately
    And the final task state should be consistent
    
    When I try to delete a task while another client is updating it
    Then the operations should be handled safely
    And data integrity should be maintained

  Scenario: Task dependency workflow via API
    Given I create a parent task "Setup Project" via API
    And I create a dependent task "Write Code" via API
    When I add a dependency from "Write Code" to "Setup Project" via API
    Then the API should return status code 200
    And the dependency should be recorded
    
    When I try to mark "Write Code" as complete before "Setup Project"
    Then the API should prevent the status change
    And return an appropriate error message
    
    When I complete "Setup Project" via API
    And then complete "Write Code" via API
    Then both operations should succeed
    And the dependency constraint should be satisfied

  Scenario: Bulk operations via API
    Given I have 10 tasks created via API
    When I perform a bulk status update to "in_progress" via API
    Then the API should return status code 200
    And all 10 tasks should have status "in_progress"
    And the response should confirm the number of updated tasks
    
    When I perform a bulk priority update to "high" via API
    Then the API should return status code 200
    And all tasks should have priority "high"
    
    When I perform a bulk delete operation via API
    Then the API should return status code 200
    And all tasks should be removed
    And subsequent list requests should return empty results

  Scenario: Task export API workflow
    Given I have multiple tasks with different attributes
    When I request task export in CSV format via API
    Then the API should return status code 200
    And the response should have CSV content type
    And the CSV should contain all task fields
    And the CSV should include proper headers
    
    When I request task export in JSON format via API
    Then the API should return status code 200
    And the response should have JSON content type
    And the JSON should contain all task data

  Scenario: Rate limiting and performance
    When I make 100 rapid API requests to list tasks
    Then the API should handle the load appropriately
    And response times should remain reasonable
    And rate limiting should be applied if configured
    
    When I create 1000 tasks via API
    Then the API should handle the volume
    And pagination should work correctly
    And search performance should remain acceptable

  Scenario: API authentication and authorization
    When I make a request without authentication
    Then the API should return status code 401
    And the response should indicate authentication required
    
    When I make a request with invalid credentials
    Then the API should return status code 401
    And the response should indicate invalid credentials
    
    When I try to access another user's tasks
    Then the API should return status code 403
    And the response should indicate insufficient permissions

  @performance
  Scenario: API performance benchmarks
    Given I have 1000 tasks in the system
    When I request the task list via API
    Then the response time should be under 500ms
    And the API should return status code 200
    
    When I search for tasks via API
    Then the response time should be under 300ms
    
    When I create a new task via API
    Then the response time should be under 200ms
    
    When I update a task via API
    Then the response time should be under 200ms

  @security
  Scenario: API security validation
    When I try to inject SQL in task title via API
    Then the API should sanitize the input
    And return status code 400 for invalid characters
    
    When I try to submit XSS payload in task description via API
    Then the API should sanitize the input
    And store safe content only
    
    When I try to submit extremely large payloads via API
    Then the API should reject requests exceeding size limits
    And return appropriate error messages
