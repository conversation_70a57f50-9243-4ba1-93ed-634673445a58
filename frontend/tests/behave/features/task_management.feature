Feature: Task Management
  As a user of the 8760 Hours application
  I want to manage my tasks effectively
  So that I can track progress towards my life goals

  Background:
    Given I am logged into the 8760 Hours application
    And I am on the task management page

  Scenario: Create a new task
    Given I want to create a new task
    When I click the "Create Task" button
    And I fill in the task form with:
      | Field           | Value                    |
      | Title           | Complete project report  |
      | Description     | Write quarterly report   |
      | Priority        | High                     |
      | Estimated Hours | 4                        |
      | Tags            | work, report             |
    And I click "Save Task"
    Then I should see the task "Complete project report" in my task list
    And the task should have status "To Do"
    And the task should have priority "High"
    And the task should have estimated hours "4"

  Scenario: Update task status
    Given I have a task "Review code changes" with status "To Do"
    When I click on the task "Review code changes"
    And I change the status to "In Progress"
    And I save the changes
    Then the task "Review code changes" should have status "In Progress"
    And the task should show the updated timestamp

  Scenario: Complete a task
    Given I have a task "Update dependencies" with status "In Progress"
    When I mark the task "Update dependencies" as complete
    Then the task should have status "Done"
    And the task should have a completion timestamp
    And the task should be moved to the completed section

  Scenario: Filter tasks by status
    Given I have tasks with different statuses:
      | Task Name           | Status      |
      | Write documentation | To Do       |
      | Review PR           | In Progress |
      | Fix bug             | Done        |
      | Plan meeting        | Blocked     |
    When I filter tasks by status "To Do"
    Then I should only see tasks with status "To Do"
    And I should see "Write documentation" in the results
    And I should not see "Review PR" in the results

  Scenario: Filter tasks by priority
    Given I have tasks with different priorities:
      | Task Name        | Priority |
      | Critical bug fix | High     |
      | Code review      | Medium   |
      | Update docs      | Low      |
    When I filter tasks by priority "High"
    Then I should only see tasks with priority "High"
    And I should see "Critical bug fix" in the results
    And I should not see "Code review" in the results

  Scenario: Search tasks by title
    Given I have multiple tasks in my list
    When I search for "documentation"
    Then I should see all tasks containing "documentation" in the title
    And I should see "Write documentation" in the results
    And I should not see unrelated tasks

  Scenario: Sort tasks by priority
    Given I have tasks with different priorities:
      | Task Name        | Priority |
      | Update docs      | Low      |
      | Critical bug fix | High     |
      | Code review      | Medium   |
    When I sort tasks by priority in descending order
    Then the tasks should be ordered as:
      | Task Name        |
      | Critical bug fix |
      | Code review      |
      | Update docs      |

  Scenario: Edit task details
    Given I have a task "Complete project report"
    When I click on the task to edit it
    And I update the task with:
      | Field           | Value                           |
      | Title           | Complete quarterly project report |
      | Description     | Write detailed quarterly report   |
      | Priority        | Medium                            |
      | Estimated Hours | 6                                 |
    And I save the changes
    Then the task should be updated with the new details
    And I should see "Complete quarterly project report" in my task list

  Scenario: Delete a task
    Given I have a task "Outdated task"
    When I click on the delete button for "Outdated task"
    And I confirm the deletion
    Then the task "Outdated task" should be removed from my task list
    And I should see a confirmation message

  Scenario: View task analytics
    Given I have completed several tasks this week
    When I navigate to the task analytics page
    Then I should see my task completion statistics
    And I should see the total number of tasks
    And I should see the number of completed tasks
    And I should see the total time spent on tasks
    And I should see a breakdown by priority
    And I should see a breakdown by life area

  Scenario: Track time on a task
    Given I have a task "Write documentation" with status "To Do"
    When I start the timer for "Write documentation"
    Then the task status should change to "In Progress"
    And I should see a running timer
    When I stop the timer after working for 30 minutes
    Then the task should show 30 minutes of actual time
    And the timer should stop

  Scenario: Add task dependencies
    Given I have tasks "Setup environment" and "Write code"
    When I set "Write code" to depend on "Setup environment"
    Then "Write code" should show as blocked until "Setup environment" is complete
    When I complete "Setup environment"
    Then "Write code" should become available to start

  Scenario: Create subtasks
    Given I have a task "Complete project"
    When I add subtasks to "Complete project":
      | Subtask Name     |
      | Research topic   |
      | Write outline    |
      | Write content    |
      | Review and edit  |
    Then I should see 4 subtasks under "Complete project"
    And the parent task should show progress based on subtask completion
    When I complete "Research topic" and "Write outline"
    Then the parent task should show 50% progress

  Scenario: View overdue tasks
    Given I have tasks with different due dates:
      | Task Name      | Due Date   | Status      |
      | Urgent report  | Yesterday  | To Do       |
      | Weekly review  | Tomorrow   | In Progress |
      | Monthly plan   | Next week  | To Do       |
    When I view my task dashboard
    Then I should see "Urgent report" marked as overdue
    And I should see an overdue tasks count of 1
    And I should receive a notification about overdue tasks

  Scenario: Bulk update tasks
    Given I have multiple tasks selected:
      | Task Name        | Status |
      | Task 1          | To Do  |
      | Task 2          | To Do  |
      | Task 3          | To Do  |
    When I select all three tasks
    And I change their priority to "High"
    Then all selected tasks should have priority "High"
    And I should see a confirmation message

  Scenario: Export task data
    Given I have multiple tasks in my system
    When I click on "Export Tasks"
    And I select "CSV" format
    Then I should receive a CSV file with my task data
    And the file should contain all task fields
    And the file should be named with the current date

  @accessibility
  Scenario: Task management accessibility
    Given I am using a screen reader
    When I navigate to the task management page
    Then all task elements should have proper ARIA labels
    And I should be able to navigate tasks using keyboard only
    And Task status changes should be announced
    And Form validation errors should be accessible
