/**
 * Accessibility tests for task management components.
 * 
 * These tests ensure WCAG 2.1 AA compliance for all task management
 * UI components including keyboard navigation, screen reader support,
 * and proper ARIA attributes.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TaskList } from '@/components/tasks/task-list';
import { TaskForm } from '@/components/tasks/task-form';
import { TaskCard } from '@/components/tasks/task-card';
import { TaskFilters } from '@/components/tasks/task-filters';
import { TaskTimer } from '@/components/tasks/task-timer';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock the API client
jest.mock('@/lib/api-client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/tasks',
}));

// Test data
const mockTask: Task = {
  id: '1',
  title: 'Test Task',
  description: 'Test task description',
  status: TaskStatus.TODO,
  priority: TaskPriority.HIGH,
  estimatedHours: 2,
  actualHours: 0,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  tags: ['test'],
};

const mockTasks: Task[] = [
  mockTask,
  {
    id: '2',
    title: 'Second Task',
    description: 'Second test task',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 4,
    actualHours: 1.5,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    tags: ['test', 'important'],
  },
];

// Test wrapper component
function TestWrapper({ children }: { children: React.ReactNode }) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

describe('Task Management Accessibility Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('TaskList Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper ARIA labels and roles', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Check for main list role
      const taskList = screen.getByRole('list', { name: /task list/i });
      expect(taskList).toBeInTheDocument();

      // Check for proper heading structure
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByTestId('create-task-button')).toHaveFocus();

      await user.tab();
      expect(screen.getByTestId('task-search-input')).toHaveFocus();

      await user.tab();
      expect(screen.getByTestId('filters-toggle-button')).toHaveFocus();
    });

    it('should announce loading states to screen readers', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Check for loading announcement
      const loadingElement = screen.getByLabelText(/loading tasks/i);
      expect(loadingElement).toHaveAttribute('aria-live', 'polite');
    });

    it('should announce error states to screen readers', () => {
      // Mock error state
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Simulate error state
      const errorElement = screen.queryByRole('alert');
      if (errorElement) {
        expect(errorElement).toHaveAttribute('aria-live', 'assertive');
      }
    });
  });

  describe('TaskCard Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <TaskCard task={mockTask} />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper ARIA labels for actions', () => {
      render(
        <TestWrapper>
          <TaskCard task={mockTask} />
        </TestWrapper>
      );

      // Check action buttons have descriptive labels
      const editButton = screen.getByLabelText(/edit task "test task"/i);
      expect(editButton).toBeInTheDocument();

      const deleteButton = screen.getByLabelText(/delete task "test task"/i);
      expect(deleteButton).toBeInTheDocument();

      const statusToggle = screen.getByLabelText(/mark task "test task" as complete/i);
      expect(statusToggle).toBeInTheDocument();
    });

    it('should support keyboard interaction', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskCard task={mockTask} />
        </TestWrapper>
      );

      // Tab to edit button and activate with Enter
      const editButton = screen.getByLabelText(/edit task/i);
      editButton.focus();
      await user.keyboard('{Enter}');

      // Verify action was triggered (would need mock verification)
    });

    it('should have proper color contrast for status indicators', () => {
      render(
        <TestWrapper>
          <TaskCard task={mockTask} />
        </TestWrapper>
      );

      const statusBadge = screen.getByText(/todo/i);
      expect(statusBadge).toBeInTheDocument();
      
      // Color contrast would be tested with actual CSS values
      // This is a placeholder for contrast ratio testing
    });

    it('should provide context for priority indicators', () => {
      render(
        <TestWrapper>
          <TaskCard task={mockTask} />
        </TestWrapper>
      );

      const priorityIndicator = screen.getByLabelText(/priority: high/i);
      expect(priorityIndicator).toBeInTheDocument();
    });
  });

  describe('TaskForm Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <TaskForm />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper form labels and descriptions', () => {
      render(
        <TestWrapper>
          <TaskForm />
        </TestWrapper>
      );

      // Check for proper form labels
      const titleInput = screen.getByLabelText(/task title/i);
      expect(titleInput).toBeInTheDocument();
      expect(titleInput).toHaveAttribute('required');

      const descriptionInput = screen.getByLabelText(/description/i);
      expect(descriptionInput).toBeInTheDocument();

      // Check for form instructions
      const instructions = screen.getByText(/required fields are marked with/i);
      expect(instructions).toBeInTheDocument();
    });

    it('should announce form validation errors', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskForm />
        </TestWrapper>
      );

      // Submit form without required fields
      const submitButton = screen.getByRole('button', { name: /save task/i });
      await user.click(submitButton);

      // Check for error announcements
      const errorMessage = screen.getByRole('alert');
      expect(errorMessage).toBeInTheDocument();
      expect(errorMessage).toHaveAttribute('aria-live', 'assertive');
    });

    it('should support keyboard navigation through form fields', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskForm />
        </TestWrapper>
      );

      // Tab through form fields
      await user.tab();
      expect(screen.getByLabelText(/task title/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/description/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/priority/i)).toHaveFocus();
    });

    it('should have proper fieldset grouping for related fields', () => {
      render(
        <TestWrapper>
          <TaskForm />
        </TestWrapper>
      );

      // Check for fieldset grouping
      const timeFieldset = screen.getByRole('group', { name: /time estimation/i });
      expect(timeFieldset).toBeInTheDocument();

      const metadataFieldset = screen.getByRole('group', { name: /task metadata/i });
      expect(metadataFieldset).toBeInTheDocument();
    });
  });

  describe('TaskFilters Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <TaskFilters />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper ARIA expanded states', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskFilters />
        </TestWrapper>
      );

      const filtersToggle = screen.getByRole('button', { name: /filters/i });
      expect(filtersToggle).toHaveAttribute('aria-expanded', 'false');

      await user.click(filtersToggle);
      expect(filtersToggle).toHaveAttribute('aria-expanded', 'true');
    });

    it('should announce filter changes to screen readers', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskFilters />
        </TestWrapper>
      );

      // Open filters
      const filtersToggle = screen.getByRole('button', { name: /filters/i });
      await user.click(filtersToggle);

      // Select a filter
      const statusFilter = screen.getByLabelText(/filter by todo status/i);
      await user.click(statusFilter);

      // Check for live region announcement
      const announcement = screen.getByLabelText(/filters applied/i);
      expect(announcement).toHaveAttribute('aria-live', 'polite');
    });

    it('should support keyboard navigation for filter options', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskFilters />
        </TestWrapper>
      );

      // Open filters with keyboard
      const filtersToggle = screen.getByRole('button', { name: /filters/i });
      filtersToggle.focus();
      await user.keyboard('{Enter}');

      // Navigate through filter options
      await user.tab();
      const firstFilter = screen.getByLabelText(/filter by todo status/i);
      expect(firstFilter).toHaveFocus();
    });
  });

  describe('TaskTimer Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <TaskTimer taskId="1" />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should announce timer state changes', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskTimer taskId="1" />
        </TestWrapper>
      );

      const startButton = screen.getByRole('button', { name: /start timer/i });
      await user.click(startButton);

      // Check for timer status announcement
      const timerStatus = screen.getByLabelText(/timer running/i);
      expect(timerStatus).toHaveAttribute('aria-live', 'polite');
    });

    it('should provide accessible time display', () => {
      render(
        <TestWrapper>
          <TaskTimer taskId="1" />
        </TestWrapper>
      );

      const timeDisplay = screen.getByLabelText(/elapsed time/i);
      expect(timeDisplay).toBeInTheDocument();
      expect(timeDisplay).toHaveAttribute('aria-live', 'polite');
    });

    it('should support keyboard control of timer functions', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskTimer taskId="1" />
        </TestWrapper>
      );

      // Start timer with keyboard
      const startButton = screen.getByRole('button', { name: /start timer/i });
      startButton.focus();
      await user.keyboard('{Enter}');

      // Stop timer with keyboard
      const stopButton = screen.getByRole('button', { name: /stop timer/i });
      stopButton.focus();
      await user.keyboard('{Enter}');
    });
  });

  describe('Focus Management', () => {
    it('should manage focus when opening modals', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Open create task modal
      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);

      // Focus should move to modal
      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
      
      // First focusable element in modal should have focus
      const titleInput = screen.getByLabelText(/task title/i);
      expect(titleInput).toHaveFocus();
    });

    it('should restore focus when closing modals', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);

      // Close modal with Escape
      await user.keyboard('{Escape}');

      // Focus should return to create button
      expect(createButton).toHaveFocus();
    });

    it('should trap focus within modals', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Open modal
      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);

      // Tab to last element and continue
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      cancelButton.focus();
      await user.tab();

      // Focus should wrap to first element
      const titleInput = screen.getByLabelText(/task title/i);
      expect(titleInput).toHaveFocus();
    });
  });

  describe('Screen Reader Support', () => {
    it('should provide meaningful page titles', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Check for page title
      expect(document.title).toContain('Tasks');
    });

    it('should use proper heading hierarchy', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Check heading levels
      const h1 = screen.getByRole('heading', { level: 1 });
      expect(h1).toBeInTheDocument();

      const h2Elements = screen.getAllByRole('heading', { level: 2 });
      expect(h2Elements.length).toBeGreaterThan(0);
    });

    it('should provide context for dynamic content', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Check for live regions
      const liveRegion = screen.queryByLabelText(/task list updates/i);
      if (liveRegion) {
        expect(liveRegion).toHaveAttribute('aria-live', 'polite');
      }
    });

    it('should provide skip links for keyboard users', () => {
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Check for skip link
      const skipLink = screen.queryByText(/skip to main content/i);
      if (skipLink) {
        expect(skipLink).toBeInTheDocument();
        expect(skipLink).toHaveAttribute('href', '#main-content');
      }
    });
  });
});
