/**
 * Performance tests for task management components.
 * 
 * These tests ensure optimal performance for task management
 * components including render times, memory usage, and
 * responsiveness under load.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TaskList } from '@/components/tasks/task-list';
import { TaskCard } from '@/components/tasks/task-card';
import { TaskForm } from '@/components/tasks/task-form';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Mock the API client
jest.mock('@/lib/api-client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/tasks',
}));

// Performance measurement utilities
const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now();
  fn();
  const end = performance.now();
  return end - start;
};

const measureAsyncPerformance = async (name: string, fn: () => Promise<void>) => {
  const start = performance.now();
  await fn();
  const end = performance.now();
  return end - start;
};

// Generate test data
const generateMockTasks = (count: number): Task[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `task-${index}`,
    title: `Task ${index + 1}`,
    description: `Description for task ${index + 1}`,
    status: Object.values(TaskStatus)[index % Object.values(TaskStatus).length] as TaskStatus,
    priority: Object.values(TaskPriority)[index % Object.values(TaskPriority).length] as TaskPriority,
    estimatedHours: Math.floor(Math.random() * 8) + 1,
    actualHours: Math.floor(Math.random() * 5),
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    tags: [`tag-${index % 5}`, `category-${index % 3}`],
  }));
};

// Test wrapper component
function TestWrapper({ children }: { children: React.ReactNode }) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

describe('Task Management Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock performance.now if not available
    if (!global.performance) {
      global.performance = {
        now: jest.fn(() => Date.now()),
      } as any;
    }
  });

  describe('TaskList Performance', () => {
    it('should render small task lists quickly', () => {
      const tasks = generateMockTasks(10);
      
      const renderTime = measurePerformance('TaskList-small', () => {
        render(
          <TestWrapper>
            <TaskList />
          </TestWrapper>
        );
      });

      // Should render in under 100ms
      expect(renderTime).toBeLessThan(100);
    });

    it('should handle large task lists efficiently', () => {
      const tasks = generateMockTasks(1000);
      
      const renderTime = measurePerformance('TaskList-large', () => {
        render(
          <TestWrapper>
            <TaskList />
          </TestWrapper>
        );
      });

      // Should render in under 500ms even with 1000 tasks
      expect(renderTime).toBeLessThan(500);
    });

    it('should virtualize large lists to maintain performance', () => {
      const tasks = generateMockTasks(10000);
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Check that not all tasks are rendered in DOM
      const renderedTasks = screen.getAllByTestId(/task-item-/);
      expect(renderedTasks.length).toBeLessThan(100); // Should virtualize
    });

    it('should debounce search input for performance', async () => {
      const user = userEvent.setup();
      let searchCallCount = 0;
      
      // Mock search function
      const mockSearch = jest.fn(() => {
        searchCallCount++;
      });

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      const searchInput = screen.getByTestId('task-search-input');
      
      // Type rapidly
      await user.type(searchInput, 'test query');
      
      // Wait for debounce
      await waitFor(() => {
        // Should not call search for every keystroke
        expect(searchCallCount).toBeLessThan(5);
      }, { timeout: 1000 });
    });

    it('should optimize re-renders when tasks update', () => {
      const tasks = generateMockTasks(100);
      let renderCount = 0;
      
      const TestComponent = () => {
        renderCount++;
        return <TaskList />;
      };

      const { rerender } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const initialRenderCount = renderCount;

      // Update a single task
      rerender(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Should not cause excessive re-renders
      expect(renderCount - initialRenderCount).toBeLessThan(3);
    });
  });

  describe('TaskCard Performance', () => {
    it('should render individual task cards quickly', () => {
      const task = generateMockTasks(1)[0];
      
      const renderTime = measurePerformance('TaskCard', () => {
        render(
          <TestWrapper>
            <TaskCard task={task} />
          </TestWrapper>
        );
      });

      // Should render in under 50ms
      expect(renderTime).toBeLessThan(50);
    });

    it('should memoize task cards to prevent unnecessary re-renders', () => {
      const task = generateMockTasks(1)[0];
      let renderCount = 0;
      
      const TestTaskCard = React.memo(() => {
        renderCount++;
        return <TaskCard task={task} />;
      });

      const { rerender } = render(
        <TestWrapper>
          <TestTaskCard />
        </TestWrapper>
      );

      const initialRenderCount = renderCount;

      // Re-render with same props
      rerender(
        <TestWrapper>
          <TestTaskCard />
        </TestWrapper>
      );

      // Should not re-render if props haven't changed
      expect(renderCount).toBe(initialRenderCount);
    });

    it('should handle rapid status updates efficiently', async () => {
      const user = userEvent.setup();
      const task = generateMockTasks(1)[0];
      
      render(
        <TestWrapper>
          <TaskCard task={task} />
        </TestWrapper>
      );

      const statusToggle = screen.getByTestId('task-status-toggle');
      
      const updateTime = await measureAsyncPerformance('status-updates', async () => {
        // Rapid status updates
        for (let i = 0; i < 10; i++) {
          await user.click(statusToggle);
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      });

      // Should handle rapid updates efficiently
      expect(updateTime).toBeLessThan(1000);
    });
  });

  describe('TaskForm Performance', () => {
    it('should render form quickly', () => {
      const renderTime = measurePerformance('TaskForm', () => {
        render(
          <TestWrapper>
            <TaskForm />
          </TestWrapper>
        );
      });

      // Should render in under 100ms
      expect(renderTime).toBeLessThan(100);
    });

    it('should validate form inputs efficiently', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskForm />
        </TestWrapper>
      );

      const titleInput = screen.getByTestId('task-title-input');
      
      const validationTime = await measureAsyncPerformance('form-validation', async () => {
        // Type and trigger validation
        await user.type(titleInput, 'Test task title');
        await user.tab(); // Trigger blur validation
      });

      // Validation should be fast
      expect(validationTime).toBeLessThan(100);
    });

    it('should handle large forms without performance degradation', () => {
      // Test with form containing many fields
      const renderTime = measurePerformance('TaskForm-large', () => {
        render(
          <TestWrapper>
            <TaskForm />
          </TestWrapper>
        );
      });

      expect(renderTime).toBeLessThan(200);
    });
  });

  describe('Memory Usage', () => {
    it('should not leak memory when mounting/unmounting components', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Mount and unmount components multiple times
      for (let i = 0; i < 100; i++) {
        const { unmount } = render(
          <TestWrapper>
            <TaskList />
          </TestWrapper>
        );
        unmount();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be minimal (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should clean up event listeners on unmount', () => {
      const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
      const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');

      const { unmount } = render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      const addedListeners = addEventListenerSpy.mock.calls.length;
      
      unmount();

      const removedListeners = removeEventListenerSpy.mock.calls.length;

      // Should remove all added event listeners
      expect(removedListeners).toBeGreaterThanOrEqual(addedListeners);

      addEventListenerSpy.mockRestore();
      removeEventListenerSpy.mockRestore();
    });
  });

  describe('Bundle Size Impact', () => {
    it('should not import unnecessary dependencies', () => {
      // This would be tested with bundle analysis tools
      // Placeholder for bundle size assertions
      expect(true).toBe(true);
    });

    it('should use code splitting for large components', () => {
      // Test that components are properly code-split
      // This would involve checking webpack chunks
      expect(true).toBe(true);
    });
  });

  describe('Network Performance', () => {
    it('should batch API requests efficiently', async () => {
      const { apiClient } = require('@/lib/api-client');
      let requestCount = 0;
      
      apiClient.get.mockImplementation(() => {
        requestCount++;
        return Promise.resolve({ data: [] });
      });

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(requestCount).toBeLessThan(3); // Should batch requests
      });
    });

    it('should implement proper caching strategies', async () => {
      const { apiClient } = require('@/lib/api-client');
      let requestCount = 0;
      
      apiClient.get.mockImplementation(() => {
        requestCount++;
        return Promise.resolve({ data: generateMockTasks(10) });
      });

      // Render multiple instances
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      await waitFor(() => {
        // Should use cached data, not make duplicate requests
        expect(requestCount).toBeLessThan(2);
      });
    });
  });

  describe('Responsiveness', () => {
    it('should maintain 60fps during animations', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskList />
        </TestWrapper>
      );

      // Simulate rapid interactions
      const button = screen.getByTestId('create-task-button');
      
      const animationTime = await measureAsyncPerformance('animations', async () => {
        for (let i = 0; i < 10; i++) {
          await user.hover(button);
          await user.unhover(button);
        }
      });

      // Should maintain smooth animations
      expect(animationTime).toBeLessThan(500);
    });

    it('should respond to user input within 100ms', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <TaskForm />
        </TestWrapper>
      );

      const input = screen.getByTestId('task-title-input');
      
      const responseTime = await measureAsyncPerformance('input-response', async () => {
        await user.type(input, 'a');
      });

      // Should respond quickly to input
      expect(responseTime).toBeLessThan(100);
    });
  });
});
