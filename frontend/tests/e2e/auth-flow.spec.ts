import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the home page
    await page.goto('/');
  });

  test.describe('Registration', () => {
    test('should allow user to register with valid credentials', async ({ page }) => {
      // Navigate to registration page
      await page.click('text=Sign Up');
      await expect(page).toHaveURL(/.*\/register/);

      // Fill registration form
      await page.fill('[data-testid="firstName-input"]', 'John');
      await page.fill('[data-testid="lastName-input"]', 'Doe');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'SecurePass123!');
      await page.fill('[data-testid="confirmPassword-input"]', 'SecurePass123!');

      // Submit form
      await page.click('button[type="submit"]');

      // Should show success message and redirect to dashboard
      await expect(page.locator('text=Welcome to 8,760 Hours!')).toBeVisible();
      await expect(page).toHaveURL(/.*\/dashboard/);
    });

    test('should show validation errors for invalid input', async ({ page }) => {
      await page.click('text=Sign Up');

      // Try to submit empty form
      await page.click('button[type="submit"]');

      // Should show validation errors
      await expect(page.locator('text=First name is required')).toBeVisible();
      await expect(page.locator('text=Last name is required')).toBeVisible();
      await expect(page.locator('text=Email is required')).toBeVisible();
      await expect(page.locator('text=Password is required')).toBeVisible();
    });

    test('should validate password requirements', async ({ page }) => {
      await page.click('text=Sign Up');

      // Fill form with weak password
      await page.fill('[data-testid="firstName-input"]', 'John');
      await page.fill('[data-testid="lastName-input"]', 'Doe');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'weak');
      await page.fill('[data-testid="confirmPassword-input"]', 'different');

      await page.click('button[type="submit"]');

      // Should show password validation errors
      await expect(page.locator('text=Password must be at least 8 characters')).toBeVisible();
      await expect(page.locator('text=Passwords don\'t match')).toBeVisible();
    });

    test('should handle registration failure', async ({ page }) => {
      // Mock API to return error
      await page.route('**/api/auth/register', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Email already exists' })
        });
      });

      await page.click('text=Sign Up');

      await page.fill('[data-testid="firstName-input"]', 'John');
      await page.fill('[data-testid="lastName-input"]', 'Doe');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'SecurePass123!');
      await page.fill('[data-testid="confirmPassword-input"]', 'SecurePass123!');

      await page.click('button[type="submit"]');

      // Should show error message
      await expect(page.locator('text=Registration Failed')).toBeVisible();
      await expect(page.locator('text=Email already exists')).toBeVisible();
    });
  });

  test.describe('Login', () => {
    test('should allow user to login with valid credentials', async ({ page }) => {
      // Navigate to login page
      await page.click('text=Sign In');
      await expect(page).toHaveURL(/.*\/login/);

      // Fill login form
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');

      // Submit form
      await page.click('button[type="submit"]');

      // Should redirect to dashboard
      await expect(page).toHaveURL(/.*\/dashboard/);
      await expect(page.locator('text=Welcome back!')).toBeVisible();
    });

    test('should show validation errors for empty fields', async ({ page }) => {
      await page.click('text=Sign In');

      // Try to submit empty form
      await page.click('button[type="submit"]');

      // Should show validation errors
      await expect(page.locator('text=Email is required')).toBeVisible();
      await expect(page.locator('text=Password is required')).toBeVisible();
    });

    test('should handle login failure', async ({ page }) => {
      // Mock API to return error
      await page.route('**/api/auth/login', route => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid credentials' })
        });
      });

      await page.click('text=Sign In');

      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrongpassword');

      await page.click('button[type="submit"]');

      // Should show error message
      await expect(page.locator('text=Login Failed')).toBeVisible();
      await expect(page.locator('text=Invalid email or password')).toBeVisible();
    });

    test('should toggle password visibility', async ({ page }) => {
      await page.click('text=Sign In');

      const passwordInput = page.locator('[data-testid="password-input"]');
      const toggleButton = page.locator('[aria-label="Toggle password visibility"]');

      // Password should be hidden initially
      await expect(passwordInput).toHaveAttribute('type', 'password');

      // Click toggle to show password
      await toggleButton.click();
      await expect(passwordInput).toHaveAttribute('type', 'text');

      // Click toggle to hide password again
      await toggleButton.click();
      await expect(passwordInput).toHaveAttribute('type', 'password');
    });

    test('should remember login preference', async ({ page }) => {
      await page.click('text=Sign In');

      const rememberMeCheckbox = page.locator('[data-testid="rememberMe-checkbox"]');

      // Check remember me
      await rememberMeCheckbox.check();
      await expect(rememberMeCheckbox).toBeChecked();

      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');

      await page.click('button[type="submit"]');

      // Should set appropriate cookies/storage for remember me
      const localStorage = await page.evaluate(() => window.localStorage);
      expect(localStorage.rememberMe).toBe('true');
    });
  });

  test.describe('Logout', () => {
    test.beforeEach(async ({ page }) => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
    });

    test('should allow user to logout', async ({ page }) => {
      // Click user menu
      await page.click('[data-testid="user-menu-trigger"]');

      // Click logout
      await page.click('text=Sign Out');

      // Should redirect to home page
      await expect(page).toHaveURL('/');
      await expect(page.locator('text=You have been logged out')).toBeVisible();
    });

    test('should clear user session on logout', async ({ page }) => {
      await page.click('[data-testid="user-menu-trigger"]');
      await page.click('text=Sign Out');

      // Try to access protected route
      await page.goto('/dashboard');

      // Should redirect to login
      await expect(page).toHaveURL(/.*\/login/);
    });
  });

  test.describe('Protected Routes', () => {
    test('should redirect unauthenticated users to login', async ({ page }) => {
      // Try to access protected route without authentication
      await page.goto('/dashboard');

      // Should redirect to login
      await expect(page).toHaveURL(/.*\/login/);
    });

    test('should redirect to intended page after login', async ({ page }) => {
      // Try to access assessment page without authentication
      await page.goto('/assessment');
      await expect(page).toHaveURL(/.*\/login/);

      // Login
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');

      // Should redirect to originally intended page
      await expect(page).toHaveURL(/.*\/assessment/);
    });
  });

  test.describe('Session Management', () => {
    test('should handle session expiry', async ({ page }) => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');

      // Mock expired session
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Session expired' })
        });
      });

      // Try to access API endpoint
      await page.goto('/dashboard');

      // Should show session expired message and redirect to login
      await expect(page.locator('text=Session expired')).toBeVisible();
      await expect(page).toHaveURL(/.*\/login/);
    });

    test('should refresh token automatically', async ({ page }) => {
      // Mock token refresh endpoint
      await page.route('**/api/auth/refresh', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ token: 'new-token' })
        });
      });

      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');

      // Should automatically refresh token when needed
      await page.waitForTimeout(1000);
      
      // Verify user stays logged in
      await expect(page).toHaveURL(/.*\/dashboard/);
    });
  });
});
