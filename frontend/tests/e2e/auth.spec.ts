import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing auth state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
  });

  test('should display landing page correctly', async ({ page }) => {
    await page.goto('/');
    
    // Check for main heading
    await expect(page.getByRole('heading', { name: /8,760 Hours/i })).toBeVisible();
    
    // Check for key features
    await expect(page.getByText(/Life Areas Assessment/i)).toBeVisible();
    await expect(page.getByText(/Goal Setting/i)).toBeVisible();
    await expect(page.getByText(/Progress Tracking/i)).toBeVisible();
    
    // Check for CTA buttons
    await expect(page.getByRole('link', { name: /Get Started/i })).toBeVisible();
    await expect(page.getByRole('link', { name: /Sign In/i })).toBeVisible();
  });

  test('should navigate to registration page', async ({ page }) => {
    await page.goto('/');
    
    // Click Get Started button
    await page.getByRole('link', { name: /Get Started/i }).click();
    
    // Should be on registration page
    await expect(page).toHaveURL('/register');
    await expect(page.getByRole('heading', { name: /Create Account/i })).toBeVisible();
    
    // Check form fields
    await expect(page.getByLabel(/First Name/i)).toBeVisible();
    await expect(page.getByLabel(/Last Name/i)).toBeVisible();
    await expect(page.getByLabel(/Email/i)).toBeVisible();
    await expect(page.getByLabel(/Password/i)).toBeVisible();
  });

  test('should register a new user successfully', async ({ page }) => {
    await page.goto('/register');
    
    // Fill out registration form
    await page.getByLabel(/First Name/i).fill('John');
    await page.getByLabel(/Last Name/i).fill('Doe');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    
    // Submit form
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Should redirect to onboarding
    await expect(page).toHaveURL('/onboarding');
    await expect(page.getByText(/Welcome to 8,760 Hours/i)).toBeVisible();
  });

  test('should show validation errors for invalid registration', async ({ page }) => {
    await page.goto('/register');
    
    // Try to submit empty form
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Should show validation errors
    await expect(page.getByText(/First name is required/i)).toBeVisible();
    await expect(page.getByText(/Email is required/i)).toBeVisible();
    await expect(page.getByText(/Password is required/i)).toBeVisible();
  });

  test('should navigate to login page', async ({ page }) => {
    await page.goto('/');
    
    // Click Sign In button
    await page.getByRole('link', { name: /Sign In/i }).click();
    
    // Should be on login page
    await expect(page).toHaveURL('/login');
    await expect(page.getByRole('heading', { name: /Sign In/i })).toBeVisible();
    
    // Check form fields
    await expect(page.getByLabel(/Email/i)).toBeVisible();
    await expect(page.getByLabel(/Password/i)).toBeVisible();
  });

  test('should login existing user successfully', async ({ page }) => {
    // First register a user
    await page.goto('/register');
    await page.getByLabel(/First Name/i).fill('Jane');
    await page.getByLabel(/Last Name/i).fill('Smith');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Complete onboarding quickly
    await page.waitForURL('/onboarding');
    await page.getByRole('button', { name: /Get Started/i }).click();
    
    // Logout
    await page.getByRole('button', { name: /Sign Out/i }).click();
    
    // Now login
    await page.goto('/login');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    await page.getByRole('button', { name: /Sign In/i }).click();
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.getByText(/Welcome back/i)).toBeVisible();
  });

  test('should show error for invalid login credentials', async ({ page }) => {
    await page.goto('/login');
    
    // Try invalid credentials
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('wrongpassword');
    await page.getByRole('button', { name: /Sign In/i }).click();
    
    // Should show error message
    await expect(page.getByText(/Invalid email or password/i)).toBeVisible();
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected route
    await page.goto('/dashboard');
    
    // Should redirect to login
    await expect(page).toHaveURL('/login');
  });

  test('should logout user successfully', async ({ page }) => {
    // Register and login a user
    await page.goto('/register');
    await page.getByLabel(/First Name/i).fill('Test');
    await page.getByLabel(/Last Name/i).fill('User');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Complete onboarding
    await page.waitForURL('/onboarding');
    await page.getByRole('button', { name: /Get Started/i }).click();
    
    // Should be on dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Logout
    await page.getByRole('button', { name: /Sign Out/i }).click();
    
    // Should redirect to home
    await expect(page).toHaveURL('/');
    
    // Try to access dashboard again
    await page.goto('/dashboard');
    await expect(page).toHaveURL('/login');
  });
});
