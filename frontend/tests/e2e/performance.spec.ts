import { test, expect } from '@playwright/test';

test.describe('Performance Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Enable performance monitoring
    await page.addInitScript(() => {
      // Mark performance start
      performance.mark('test-start');
    });
  });

  test.describe('Page Load Performance', () => {
    test('homepage should load within performance budget', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/');
      
      // Wait for page to be fully loaded
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
      
      // Check Core Web Vitals
      const webVitals = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const vitals: any = {};
            
            entries.forEach((entry) => {
              if (entry.name === 'first-contentful-paint') {
                vitals.fcp = entry.startTime;
              }
              if (entry.name === 'largest-contentful-paint') {
                vitals.lcp = entry.startTime;
              }
            });
            
            resolve(vitals);
          }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
          
          // Fallback timeout
          setTimeout(() => resolve({}), 5000);
        });
      });
      
      console.log('Web Vitals:', webVitals);
    });

    test('dashboard should load quickly for authenticated users', async ({ page }) => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');
      
      const startTime = Date.now();
      
      await page.goto('/dashboard');
      await page.waitForSelector('[data-testid="dashboard-content"]');
      
      const loadTime = Date.now() - startTime;
      
      // Dashboard should load within 2 seconds for authenticated users
      expect(loadTime).toBeLessThan(2000);
    });

    test('assessment page should load efficiently', async ({ page }) => {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');
      
      const startTime = Date.now();
      
      await page.goto('/assessment');
      await page.waitForSelector('text=Life Areas Assessment');
      
      const loadTime = Date.now() - startTime;
      
      // Assessment page should load within 2.5 seconds
      expect(loadTime).toBeLessThan(2500);
    });
  });

  test.describe('Runtime Performance', () => {
    test('assessment rating interactions should be responsive', async ({ page }) => {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');
      
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      // Measure rating selection performance
      const ratingTimes: number[] = [];
      
      for (let i = 1; i <= 7; i++) {
        const startTime = performance.now();
        
        await page.click(`[data-testid="rating-${i}"]`);
        
        // Wait for visual feedback
        await page.waitForSelector(`[data-testid="rating-${i}"].ring-2`);
        
        const endTime = performance.now();
        ratingTimes.push(endTime - startTime);
      }
      
      // All rating interactions should be under 100ms
      const maxRatingTime = Math.max(...ratingTimes);
      expect(maxRatingTime).toBeLessThan(100);
      
      // Average should be under 50ms
      const avgRatingTime = ratingTimes.reduce((a, b) => a + b, 0) / ratingTimes.length;
      expect(avgRatingTime).toBeLessThan(50);
    });

    test('form typing should not have input lag', async ({ page }) => {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');
      
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      const textarea = page.locator('[data-testid="notes-textarea"]');
      
      // Measure typing performance
      const longText = 'This is a long text that I am typing to test the performance of the textarea input field. '.repeat(10);
      
      const startTime = performance.now();
      
      await textarea.fill(longText);
      
      const endTime = performance.now();
      const typingTime = endTime - startTime;
      
      // Typing should complete within reasonable time (500ms for long text)
      expect(typingTime).toBeLessThan(500);
      
      // Verify text was entered correctly
      const textareaValue = await textarea.inputValue();
      expect(textareaValue).toBe(longText);
    });

    test('navigation between pages should be fast', async ({ page }) => {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');
      
      const pages = ['/dashboard', '/assessment', '/planning', '/progress', '/profile'];
      const navigationTimes: number[] = [];
      
      for (const pagePath of pages) {
        const startTime = performance.now();
        
        await page.goto(pagePath);
        await page.waitForLoadState('networkidle');
        
        const endTime = performance.now();
        navigationTimes.push(endTime - startTime);
      }
      
      // All page navigations should be under 2 seconds
      const maxNavigationTime = Math.max(...navigationTimes);
      expect(maxNavigationTime).toBeLessThan(2000);
      
      // Average navigation time should be under 1 second
      const avgNavigationTime = navigationTimes.reduce((a, b) => a + b, 0) / navigationTimes.length;
      expect(avgNavigationTime).toBeLessThan(1000);
    });
  });

  test.describe('Memory Performance', () => {
    test('should not have memory leaks during assessment', async ({ page }) => {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('button[type="submit"]');
      
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      // Get initial memory usage
      const initialMemory = await page.evaluate(() => {
        if ('memory' in performance) {
          return (performance as any).memory.usedJSHeapSize;
        }
        return 0;
      });
      
      // Simulate going through multiple life areas
      for (let i = 0; i < 5; i++) {
        // Rate current area
        await page.click('[data-testid="rating-5"]');
        
        // Add notes
        await page.fill('[data-testid="notes-textarea"]', `Notes for area ${i + 1}`);
        
        // Navigate to next area if not last
        if (i < 4) {
          await page.click('button:has-text("Next Area")');
          await page.waitForTimeout(100); // Small delay for state updates
        }
      }
      
      // Force garbage collection if available
      await page.evaluate(() => {
        if ('gc' in window) {
          (window as any).gc();
        }
      });
      
      // Get final memory usage
      const finalMemory = await page.evaluate(() => {
        if ('memory' in performance) {
          return (performance as any).memory.usedJSHeapSize;
        }
        return 0;
      });
      
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryIncrease = finalMemory - initialMemory;
        const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;
        
        // Memory increase should be reasonable (less than 50% increase)
        expect(memoryIncreasePercent).toBeLessThan(50);
        
        console.log(`Memory usage: ${initialMemory} -> ${finalMemory} (${memoryIncreasePercent.toFixed(2)}% increase)`);
      }
    });
  });

  test.describe('Network Performance', () => {
    test('should minimize network requests', async ({ page }) => {
      const requests: string[] = [];
      
      page.on('request', request => {
        requests.push(request.url());
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Filter out external requests (fonts, analytics, etc.)
      const internalRequests = requests.filter(url => 
        url.includes('localhost') || url.includes('127.0.0.1')
      );
      
      // Should have reasonable number of requests (less than 20 for homepage)
      expect(internalRequests.length).toBeLessThan(20);
      
      console.log(`Total requests: ${requests.length}, Internal requests: ${internalRequests.length}`);
    });

    test('should handle slow network conditions', async ({ page, context }) => {
      // Simulate slow 3G network
      await context.route('**/*', async route => {
        await new Promise(resolve => setTimeout(resolve, 100)); // 100ms delay
        await route.continue();
      });
      
      const startTime = Date.now();
      
      await page.goto('/dashboard');
      await page.waitForSelector('[data-testid="dashboard-content"]');
      
      const loadTime = Date.now() - startTime;
      
      // Should still load within reasonable time even on slow network (10 seconds)
      expect(loadTime).toBeLessThan(10000);
    });

    test('should cache static assets effectively', async ({ page }) => {
      // First visit
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const firstVisitRequests: string[] = [];
      page.on('request', request => {
        firstVisitRequests.push(request.url());
      });
      
      // Second visit (should use cache)
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Wait a bit for all requests to complete
      await page.waitForTimeout(1000);
      
      // Should have fewer requests on second visit due to caching
      const cachedRequests = firstVisitRequests.filter(url => 
        url.includes('.js') || url.includes('.css') || url.includes('.png') || url.includes('.ico')
      );
      
      console.log(`Cached asset requests: ${cachedRequests.length}`);
    });
  });

  test.describe('Bundle Size Performance', () => {
    test('should have reasonable JavaScript bundle size', async ({ page }) => {
      const jsRequests: { url: string; size: number }[] = [];
      
      page.on('response', async response => {
        if (response.url().includes('.js') && response.status() === 200) {
          try {
            const buffer = await response.body();
            jsRequests.push({
              url: response.url(),
              size: buffer.length
            });
          } catch (error) {
            // Ignore errors for external resources
          }
        }
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const totalJSSize = jsRequests.reduce((total, req) => total + req.size, 0);
      const totalJSSizeKB = totalJSSize / 1024;
      
      // Total JS bundle should be under 1MB (1024KB)
      expect(totalJSSizeKB).toBeLessThan(1024);
      
      console.log(`Total JS bundle size: ${totalJSSizeKB.toFixed(2)}KB`);
      
      // Log individual bundle sizes
      jsRequests.forEach(req => {
        const sizeKB = req.size / 1024;
        console.log(`${req.url}: ${sizeKB.toFixed(2)}KB`);
      });
    });
  });

  test.describe('Lighthouse Performance', () => {
    test('should meet Lighthouse performance thresholds', async ({ page }) => {
      await page.goto('/');
      
      // Run basic performance checks that simulate Lighthouse metrics
      const performanceMetrics = await page.evaluate(() => {
        return new Promise((resolve) => {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const metrics: any = {};
            
            entries.forEach((entry) => {
              if (entry.entryType === 'navigation') {
                const navEntry = entry as PerformanceNavigationTiming;
                metrics.domContentLoaded = navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart;
                metrics.loadComplete = navEntry.loadEventEnd - navEntry.loadEventStart;
              }
              
              if (entry.entryType === 'paint') {
                if (entry.name === 'first-contentful-paint') {
                  metrics.fcp = entry.startTime;
                }
                if (entry.name === 'first-paint') {
                  metrics.fp = entry.startTime;
                }
              }
            });
            
            resolve(metrics);
          });
          
          observer.observe({ entryTypes: ['navigation', 'paint'] });
          
          // Fallback timeout
          setTimeout(() => resolve({}), 5000);
        });
      });
      
      console.log('Performance Metrics:', performanceMetrics);
      
      // Basic performance assertions
      if ((performanceMetrics as any).fcp) {
        // First Contentful Paint should be under 2 seconds
        expect((performanceMetrics as any).fcp).toBeLessThan(2000);
      }
    });
  });
});
