import { test, expect } from '@playwright/test';

test.describe('Progress Tracking', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
    
    // Register and login a user
    await page.goto('/register');
    await page.getByLabel(/First Name/i).fill('Progress');
    await page.getByLabel(/Last Name/i).fill('Tester');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Complete onboarding
    await page.waitForURL('/onboarding');
    await page.getByRole('button', { name: /Get Started/i }).click();
    await page.waitForURL('/dashboard');
    
    // Create a goal first for progress tracking
    await page.goto('/planning');
    await page.getByRole('button', { name: /New Goal/i }).click();
    await page.getByLabel(/Goal Title/i).fill('Test Progress Goal');
    await page.getByLabel(/Goal Description/i).fill('Goal for testing progress tracking');
    await page.getByLabel(/Specific/i).fill('Specific criteria');
    await page.getByLabel(/Measurable/i).fill('Measurable criteria');
    await page.getByLabel(/Achievable/i).fill('Achievable criteria');
    await page.getByLabel(/Relevant/i).fill('Relevant criteria');
    await page.getByLabel(/Time-Bound/i).fill('Time-bound criteria');
    await page.getByText(/Values/i).click();
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Start the goal
    await page.getByRole('button', { name: /⋯/i }).click();
    await page.getByRole('button', { name: /Start/i }).click();
  });

  test('should navigate to progress page', async ({ page }) => {
    // Navigate to progress page
    await page.goto('/progress');
    
    // Should be on progress page
    await expect(page.getByRole('heading', { name: /Progress Tracking/i })).toBeVisible();
    
    // Check for statistics cards
    await expect(page.getByText(/Active Goals/i)).toBeVisible();
    await expect(page.getByText(/Completed/i)).toBeVisible();
    await expect(page.getByText(/Avg Progress/i)).toBeVisible();
    await expect(page.getByText(/Active Habits/i)).toBeVisible();
  });

  test('should show active goals for progress tracking', async ({ page }) => {
    await page.goto('/progress');
    
    // Should show the goal we created
    await expect(page.getByText(/Test Progress Goal/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /Log Progress/i })).toBeVisible();
    
    // Statistics should show 1 active goal
    await expect(page.getByText(/Active Goals.*1/i)).toBeVisible();
  });

  test('should log progress for a goal', async ({ page }) => {
    await page.goto('/progress');
    
    // Click Log Progress button
    await page.getByRole('button', { name: /Log Progress/i }).click();
    
    // Should be on progress form
    await expect(page.getByRole('heading', { name: /Log Progress/i })).toBeVisible();
    await expect(page.getByText(/Test Progress Goal/i)).toBeVisible();
    
    // Fill out progress form
    await page.getByLabel(/Progress Percentage/i).fill('25');
    await page.getByLabel(/Time Spent/i).fill('60');
    
    // Select mood
    await page.getByRole('button', { name: /😊.*Confident/i }).click();
    
    // Select energy level
    await page.getByRole('radio', { name: /High Energy/i }).click();
    
    // Select motivation level
    await page.getByRole('radio', { name: /High Motivation/i }).click();
    
    // Add a win
    await page.getByPlaceholder(/What went well/i).fill('Made significant progress on my goal today');
    await page.getByRole('button', { name: /\+/i }).first().click();
    
    // Add a challenge
    await page.getByPlaceholder(/What challenges did you face/i).fill('Had some difficulty staying focused');
    await page.getByRole('button', { name: /\+/i }).nth(1).click();
    
    // Add a lesson
    await page.getByPlaceholder(/What did you learn/i).fill('Need to eliminate distractions during work time');
    await page.getByRole('button', { name: /\+/i }).nth(2).click();
    
    // Add next step
    await page.getByPlaceholder(/What will you do next/i).fill('Schedule focused work blocks for tomorrow');
    await page.getByRole('button', { name: /\+/i }).nth(3).click();
    
    // Add notes
    await page.getByLabel(/Additional Notes/i).fill('Overall a productive day with good momentum.');
    
    // Submit progress
    await page.getByRole('button', { name: /Log Progress/i }).click();
    
    // Should return to progress page
    await expect(page).toHaveURL('/progress');
    
    // Should show updated progress
    await expect(page.getByText(/25%/i)).toBeVisible();
  });

  test('should track habits', async ({ page }) => {
    await page.goto('/progress');
    
    // Should show habit tracker section
    await expect(page.getByText(/Today's Habits/i)).toBeVisible();
    
    // Add a habit
    await page.getByRole('button', { name: /\+/i }).click();
    
    // Should show success message for adding habit
    await expect(page.getByText(/Habit Added/i)).toBeVisible();
    
    // Should show the new habit
    await expect(page.getByText(/New Habit/i)).toBeVisible();
    
    // Mark habit as complete
    await page.getByRole('button', { name: /○/i }).click(); // Circle icon for incomplete habit
    
    // Should show as completed
    await expect(page.getByRole('button', { name: /✓/i })).toBeVisible(); // Check icon for completed habit
  });

  test('should show habit statistics', async ({ page }) => {
    await page.goto('/progress');
    
    // Add a habit first
    await page.getByRole('button', { name: /\+/i }).click();
    
    // Check habit statistics
    await expect(page.getByText(/Habit Statistics/i)).toBeVisible();
    await expect(page.getByText(/Active Habits.*1/i)).toBeVisible();
  });

  test('should switch between habit views', async ({ page }) => {
    await page.goto('/progress');
    
    // Should start in Today view
    await expect(page.getByText(/Today's Habits/i)).toBeVisible();
    
    // Switch to Week view
    await page.getByRole('button', { name: /Week/i }).click();
    
    // Should show weekly view
    await expect(page.getByText(/Weekly Habit Tracker/i)).toBeVisible();
    
    // Switch back to Today view
    await page.getByRole('button', { name: /Today/i }).click();
    
    // Should show today view again
    await expect(page.getByText(/Today's Habits/i)).toBeVisible();
  });

  test('should show recent activity', async ({ page }) => {
    await page.goto('/progress');
    
    // Log some progress first
    await page.getByRole('button', { name: /Log Progress/i }).click();
    await page.getByLabel(/Progress Percentage/i).fill('30');
    await page.getByRole('button', { name: /😊.*Confident/i }).click();
    await page.getByPlaceholder(/What went well/i).fill('Great progress today');
    await page.getByRole('button', { name: /\+/i }).first().click();
    await page.getByRole('button', { name: /Log Progress/i }).click();
    
    // Should show recent activity section
    await expect(page.getByText(/Recent Activity/i)).toBeVisible();
    
    // Should show the progress entry
    await expect(page.getByText(/Test Progress Goal/i)).toBeVisible();
    await expect(page.getByText(/30%/i)).toBeVisible();
    await expect(page.getByText(/Great progress today/i)).toBeVisible();
  });

  test('should show progress insights', async ({ page }) => {
    await page.goto('/progress');
    
    // Log multiple progress entries to generate insights
    for (let i = 0; i < 3; i++) {
      await page.getByRole('button', { name: /Log Progress/i }).click();
      await page.getByLabel(/Progress Percentage/i).fill(`${(i + 1) * 20}`);
      await page.getByRole('button', { name: /😊.*Confident/i }).click();
      await page.getByRole('button', { name: /Log Progress/i }).click();
      await page.waitForTimeout(1000); // Small delay between entries
    }
    
    // Should show insights section
    await expect(page.getByText(/Progress Insights/i)).toBeVisible();
  });

  test('should handle no active goals state', async ({ page }) => {
    // Complete the goal first
    await page.goto('/planning');
    await page.getByRole('button', { name: /⋯/i }).click();
    await page.getByRole('button', { name: /Complete/i }).click();
    
    // Go to progress page
    await page.goto('/progress');
    
    // Should show no active goals message
    await expect(page.getByText(/No active goals/i)).toBeVisible();
    await expect(page.getByText(/Create some goals in your planning session/i)).toBeVisible();
    
    // Should have link to planning
    await expect(page.getByRole('button', { name: /Go to Planning/i })).toBeVisible();
  });

  test('should handle no habits state', async ({ page }) => {
    await page.goto('/progress');
    
    // Should show no habits message initially
    await expect(page.getByText(/No habits yet/i)).toBeVisible();
    await expect(page.getByText(/Start building positive habits/i)).toBeVisible();
    
    // Should have button to add first habit
    await expect(page.getByRole('button', { name: /Add Your First Habit/i })).toBeVisible();
  });

  test('should persist progress data', async ({ page }) => {
    await page.goto('/progress');
    
    // Log progress
    await page.getByRole('button', { name: /Log Progress/i }).click();
    await page.getByLabel(/Progress Percentage/i).fill('40');
    await page.getByRole('button', { name: /😊.*Confident/i }).click();
    await page.getByRole('button', { name: /Log Progress/i }).click();
    
    // Add and complete a habit
    await page.getByRole('button', { name: /\+/i }).click();
    await page.getByRole('button', { name: /○/i }).click();
    
    // Refresh the page
    await page.reload();
    
    // Progress should be maintained
    await expect(page.getByText(/40%/i)).toBeVisible();
    
    // Habit completion should be maintained
    await expect(page.getByRole('button', { name: /✓/i })).toBeVisible();
  });

  test('should show correct statistics', async ({ page }) => {
    await page.goto('/progress');
    
    // Initial statistics
    await expect(page.getByText(/Active Goals.*1/i)).toBeVisible();
    await expect(page.getByText(/Completed.*0/i)).toBeVisible();
    
    // Log some progress
    await page.getByRole('button', { name: /Log Progress/i }).click();
    await page.getByLabel(/Progress Percentage/i).fill('50');
    await page.getByRole('button', { name: /😊.*Confident/i }).click();
    await page.getByRole('button', { name: /Log Progress/i }).click();
    
    // Average progress should update
    await expect(page.getByText(/Avg Progress.*50%/i)).toBeVisible();
    
    // Add a habit
    await page.getByRole('button', { name: /\+/i }).click();
    
    // Active habits should update
    await expect(page.getByText(/Active Habits.*1/i)).toBeVisible();
  });
});
