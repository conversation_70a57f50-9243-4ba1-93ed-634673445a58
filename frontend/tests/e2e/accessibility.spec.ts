import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test.describe('WCAG 2.1 AA Compliance', () => {
    test('dashboard should be accessible', async ({ page }) => {
      await page.goto('/dashboard');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test('assessment page should be accessible', async ({ page }) => {
      await page.goto('/assessment');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test('assessment results page should be accessible', async ({ page }) => {
      // Mock completed assessment
      await page.addInitScript(() => {
        window.localStorage.setItem('assessment-data', JSON.stringify({
          ratings: { valuesPurpose: 5, contribution: 4 },
          isComplete: true,
          completedAt: new Date().toISOString()
        }));
      });

      await page.goto('/assessment/results');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test('planning session page should be accessible', async ({ page }) => {
      await page.goto('/planning/session');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test('profile page should be accessible', async ({ page }) => {
      await page.goto('/profile');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });
  });

  test.describe('Keyboard Navigation', () => {
    test('should navigate assessment with keyboard only', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');

      // Tab through rating buttons
      await page.keyboard.press('Tab');
      await expect(page.locator('[data-testid="rating-1"]')).toBeFocused();

      // Arrow keys should navigate between ratings
      await page.keyboard.press('ArrowRight');
      await expect(page.locator('[data-testid="rating-2"]')).toBeFocused();

      await page.keyboard.press('ArrowRight');
      await expect(page.locator('[data-testid="rating-3"]')).toBeFocused();

      // Enter should select rating
      await page.keyboard.press('Enter');
      await expect(page.locator('[data-testid="rating-3"]')).toHaveClass(/ring-2/);

      // Tab to notes field
      await page.keyboard.press('Tab');
      await expect(page.locator('[data-testid="notes-textarea"]')).toBeFocused();

      // Tab to next button
      await page.keyboard.press('Tab');
      await expect(page.locator('button:has-text("Next Area")')).toBeFocused();

      // Enter should navigate to next area
      await page.keyboard.press('Enter');
      await expect(page.locator('h2:has-text("Contribution & Impact")')).toBeVisible();
    });

    test('should navigate forms with keyboard', async ({ page }) => {
      await page.goto('/profile');

      // Tab through form fields
      await page.keyboard.press('Tab');
      const firstInput = page.locator('input').first();
      await expect(firstInput).toBeFocused();

      // Continue tabbing through all form elements
      const formElements = await page.locator('input, select, button, textarea').all();
      
      for (let i = 1; i < formElements.length; i++) {
        await page.keyboard.press('Tab');
        await expect(formElements[i]).toBeFocused();
      }
    });

    test('should navigate dropdown menus with keyboard', async ({ page }) => {
      await page.goto('/dashboard');

      // Focus on user menu trigger
      await page.keyboard.press('Tab');
      const userMenuTrigger = page.locator('[data-testid="user-menu-trigger"]');
      
      if (await userMenuTrigger.isVisible()) {
        await userMenuTrigger.focus();
        
        // Open menu with Enter
        await page.keyboard.press('Enter');
        
        // Arrow down to navigate menu items
        await page.keyboard.press('ArrowDown');
        
        // Should focus first menu item
        const firstMenuItem = page.locator('[role="menuitem"]').first();
        await expect(firstMenuItem).toBeFocused();
      }
    });
  });

  test.describe('Screen Reader Support', () => {
    test('should have proper heading structure', async ({ page }) => {
      await page.goto('/assessment');
      
      // Check heading hierarchy
      const h1 = page.locator('h1');
      await expect(h1).toHaveCount(1);
      await expect(h1).toContainText('Life Areas Assessment');

      const h2Elements = page.locator('h2');
      const h2Count = await h2Elements.count();
      expect(h2Count).toBeGreaterThan(0);

      // No h3 should appear before h2
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingTexts = await headings.allTextContents();
      
      // Verify logical heading order (this is a simplified check)
      expect(headingTexts.length).toBeGreaterThan(0);
    });

    test('should have proper ARIA labels', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');

      // Rating buttons should have aria-labels
      const ratingButtons = page.locator('[data-testid^="rating-"]');
      const buttonCount = await ratingButtons.count();
      
      for (let i = 0; i < buttonCount; i++) {
        const button = ratingButtons.nth(i);
        const ariaLabel = await button.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
        expect(ariaLabel).toContain('Rate');
      }

      // Progress indicator should have aria-label
      const progressBar = page.locator('[role="progressbar"]');
      if (await progressBar.isVisible()) {
        const ariaLabel = await progressBar.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
      }
    });

    test('should announce dynamic content changes', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');

      // Click rating button
      await page.click('[data-testid="rating-4"]');

      // Should have aria-live region with announcement
      const liveRegion = page.locator('[aria-live="polite"]');
      if (await liveRegion.isVisible()) {
        const announcement = await liveRegion.textContent();
        expect(announcement).toContain('Rating selected');
      }
    });

    test('should have proper form labels', async ({ page }) => {
      await page.goto('/profile');

      // All form inputs should have associated labels
      const inputs = page.locator('input[type="text"], input[type="email"], textarea, select');
      const inputCount = await inputs.count();

      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const id = await input.getAttribute('id');
        
        if (id) {
          // Check for associated label
          const label = page.locator(`label[for="${id}"]`);
          const hasLabel = await label.count() > 0;
          
          // Or check for aria-label
          const ariaLabel = await input.getAttribute('aria-label');
          
          // Or check for aria-labelledby
          const ariaLabelledBy = await input.getAttribute('aria-labelledby');
          
          expect(hasLabel || ariaLabel || ariaLabelledBy).toBeTruthy();
        }
      }
    });
  });

  test.describe('Color Contrast', () => {
    test('should meet color contrast requirements', async ({ page }) => {
      await page.goto('/dashboard');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2aa'])
        .include('body')
        .analyze();

      // Filter for color contrast violations
      const contrastViolations = accessibilityScanResults.violations.filter(
        violation => violation.id === 'color-contrast'
      );

      expect(contrastViolations).toEqual([]);
    });

    test('should work in high contrast mode', async ({ page }) => {
      // Simulate high contrast mode
      await page.emulateMedia({ colorScheme: 'dark', reducedMotion: 'reduce' });
      await page.goto('/dashboard');

      // Check that content is still visible and functional
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('nav')).toBeVisible();
      
      // Test interactive elements
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        await expect(buttons.nth(i)).toBeVisible();
      }
    });
  });

  test.describe('Focus Management', () => {
    test('should manage focus on page navigation', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Navigate to assessment
      await page.click('text=Start Assessment');
      await expect(page).toHaveURL(/.*\/assessment/);
      
      // Focus should be on main content or first interactive element
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    });

    test('should trap focus in modals', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Look for modal triggers
      const modalTrigger = page.locator('[data-testid="modal-trigger"]');
      
      if (await modalTrigger.isVisible()) {
        await modalTrigger.click();
        
        // Focus should be trapped within modal
        const modal = page.locator('[role="dialog"]');
        await expect(modal).toBeVisible();
        
        // Tab should cycle within modal
        await page.keyboard.press('Tab');
        const focusedElement = page.locator(':focus');
        
        // Focused element should be within modal
        const isWithinModal = await modal.locator(':focus').count() > 0;
        expect(isWithinModal).toBe(true);
      }
    });

    test('should have visible focus indicators', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');

      // Tab to first rating button
      await page.keyboard.press('Tab');
      
      const focusedButton = page.locator('[data-testid="rating-1"]');
      await expect(focusedButton).toBeFocused();
      
      // Check for visible focus indicator
      const focusStyles = await focusedButton.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          outline: styles.outline,
          outlineWidth: styles.outlineWidth,
          boxShadow: styles.boxShadow,
        };
      });
      
      // Should have some form of focus indicator
      const hasFocusIndicator = 
        focusStyles.outline !== 'none' ||
        focusStyles.outlineWidth !== '0px' ||
        focusStyles.boxShadow !== 'none';
      
      expect(hasFocusIndicator).toBe(true);
    });
  });

  test.describe('Responsive Accessibility', () => {
    test('should be accessible on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE
      await page.goto('/assessment');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test('should be accessible on tablet devices', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 }); // iPad
      await page.goto('/dashboard');
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });
  });

  test.describe('Motion and Animation', () => {
    test('should respect reduced motion preferences', async ({ page }) => {
      await page.emulateMedia({ reducedMotion: 'reduce' });
      await page.goto('/dashboard');

      // Check that animations are disabled or reduced
      const animatedElements = page.locator('[class*="animate"], [class*="transition"]');
      const elementCount = await animatedElements.count();

      if (elementCount > 0) {
        // Verify that animations respect reduced motion
        const firstElement = animatedElements.first();
        const animationDuration = await firstElement.evaluate(el => {
          const styles = window.getComputedStyle(el);
          return styles.animationDuration;
        });

        // Animation should be disabled or very short
        expect(animationDuration === '0s' || animationDuration === '0.01s').toBeTruthy();
      }
    });
  });
});
