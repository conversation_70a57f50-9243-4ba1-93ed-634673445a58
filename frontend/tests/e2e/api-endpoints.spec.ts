import { test, expect } from '@playwright/test';

test.describe('API Endpoints Testing', () => {
  let apiContext: any;

  test.beforeAll(async ({ playwright }) => {
    apiContext = await playwright.request.newContext({
      baseURL: process.env.API_BASE_URL || 'http://localhost:3000/api',
      extraHTTPHeaders: {
        'Content-Type': 'application/json',
      },
    });
  });

  test.afterAll(async () => {
    await apiContext.dispose();
  });

  test.describe('Authentication API', () => {
    test('should register new user', async () => {
      const response = await apiContext.post('/auth/register', {
        data: {
          firstName: 'Test',
          lastName: 'User',
          email: `test-${Date.now()}@example.com`,
          password: 'SecurePass123!',
        },
      });

      expect(response.status()).toBe(201);
      
      const data = await response.json();
      expect(data).toHaveProperty('user');
      expect(data).toHaveProperty('token');
      expect(data.user.email).toContain('@example.com');
    });

    test('should reject invalid registration data', async () => {
      const response = await apiContext.post('/auth/register', {
        data: {
          firstName: '',
          lastName: '',
          email: 'invalid-email',
          password: '123',
        },
      });

      expect(response.status()).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('errors');
    });

    test('should login with valid credentials', async () => {
      // First register a user
      const registerResponse = await apiContext.post('/auth/register', {
        data: {
          firstName: 'Login',
          lastName: 'Test',
          email: `login-test-${Date.now()}@example.com`,
          password: 'SecurePass123!',
        },
      });
      
      const registerData = await registerResponse.json();
      
      // Then login
      const loginResponse = await apiContext.post('/auth/login', {
        data: {
          email: registerData.user.email,
          password: 'SecurePass123!',
        },
      });

      expect(loginResponse.status()).toBe(200);
      
      const loginData = await loginResponse.json();
      expect(loginData).toHaveProperty('user');
      expect(loginData).toHaveProperty('token');
    });

    test('should reject invalid login credentials', async () => {
      const response = await apiContext.post('/auth/login', {
        data: {
          email: '<EMAIL>',
          password: 'wrongpassword',
        },
      });

      expect(response.status()).toBe(401);
      
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });
  });

  test.describe('Assessment API', () => {
    let authToken: string;
    let userId: string;

    test.beforeEach(async () => {
      // Create authenticated user for each test
      const registerResponse = await apiContext.post('/auth/register', {
        data: {
          firstName: 'Assessment',
          lastName: 'User',
          email: `assessment-${Date.now()}@example.com`,
          password: 'SecurePass123!',
        },
      });
      
      const userData = await registerResponse.json();
      authToken = userData.token;
      userId = userData.user.id;
    });

    test('should create new assessment', async () => {
      const response = await apiContext.post('/assessments', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
        data: {
          ratings: {
            valuesPurpose: 5,
            contribution: 4,
            location: 3,
          },
          notes: {
            valuesPurpose: 'This is important to me',
          },
        },
      });

      expect(response.status()).toBe(201);
      
      const data = await response.json();
      expect(data).toHaveProperty('id');
      expect(data).toHaveProperty('ratings');
      expect(data.ratings.valuesPurpose).toBe(5);
    });

    test('should get user assessments', async () => {
      // Create an assessment first
      await apiContext.post('/assessments', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
        data: {
          ratings: { valuesPurpose: 6 },
        },
      });

      // Get assessments
      const response = await apiContext.get('/assessments', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(Array.isArray(data)).toBe(true);
    });

    test('should validate assessment data', async () => {
      const response = await apiContext.post('/assessments', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
        data: {
          ratings: {
            valuesPurpose: 10, // Invalid rating (should be 1-7)
            invalidArea: 5,    // Invalid life area
          },
        },
      });

      expect(response.status()).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('errors');
    });

    test('should require authentication', async () => {
      const response = await apiContext.post('/assessments', {
        data: {
          ratings: { valuesPurpose: 5 },
        },
      });

      expect(response.status()).toBe(401);
    });
  });

  test.describe('User Profile API', () => {
    let authToken: string;
    let userId: string;

    test.beforeEach(async () => {
      const registerResponse = await apiContext.post('/auth/register', {
        data: {
          firstName: 'Profile',
          lastName: 'User',
          email: `profile-${Date.now()}@example.com`,
          password: 'SecurePass123!',
        },
      });
      
      const userData = await registerResponse.json();
      authToken = userData.token;
      userId = userData.user.id;
    });

    test('should get user profile', async () => {
      const response = await apiContext.get(`/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('id');
      expect(data).toHaveProperty('email');
      expect(data.id).toBe(userId);
    });

    test('should update user profile', async () => {
      const response = await apiContext.put(`/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
        data: {
          firstName: 'Updated',
          lastName: 'Name',
          preferences: {
            theme: 'dark',
            language: 'es',
          },
        },
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.firstName).toBe('Updated');
      expect(data.lastName).toBe('Name');
    });
  });

  test.describe('Error Handling', () => {
    test('should handle malformed JSON', async () => {
      const response = await apiContext.post('/auth/login', {
        data: 'invalid json',
      });

      expect(response.status()).toBe(400);
    });

    test('should handle missing endpoints', async () => {
      const response = await apiContext.get('/nonexistent-endpoint');
      expect(response.status()).toBe(404);
    });
  });

  test.describe('Rate Limiting', () => {
    test('should enforce rate limits on login attempts', async () => {
      const promises = [];
      
      // Make many requests quickly
      for (let i = 0; i < 10; i++) {
        promises.push(
          apiContext.post('/auth/login', {
            data: {
              email: '<EMAIL>',
              password: 'wrongpassword',
            },
          })
        );
      }

      const responses = await Promise.all(promises);
      
      // Check if any requests were rate limited
      const statusCodes = responses.map(r => r.status());
      const hasRateLimit = statusCodes.some(code => code === 429);
      
      // Rate limiting might not be implemented yet, so this is optional
      if (hasRateLimit) {
        expect(hasRateLimit).toBe(true);
      }
    });
  });

  test.describe('Security Headers', () => {
    test('should include security headers', async () => {
      const response = await apiContext.get('/health');
      
      // Check for common security headers
      const headers = response.headers();
      
      // These might not all be implemented yet
      if (headers['x-frame-options']) {
        expect(headers['x-frame-options']).toBeTruthy();
      }
      
      if (headers['x-content-type-options']) {
        expect(headers['x-content-type-options']).toBe('nosniff');
      }
    });
  });
});
