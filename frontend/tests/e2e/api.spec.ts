import { test, expect } from '@playwright/test';

test.describe('API Tests', () => {
  const baseURL = 'http://localhost:3001';

  test('should serve the homepage', async ({ request }) => {
    const response = await request.get(`${baseURL}/`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    const body = await response.text();
    expect(body).toContain('8,760 Hours');
    expect(body).toContain('Life Planning Platform');
  });

  test('should serve static assets', async ({ request }) => {
    // Test favicon
    const faviconResponse = await request.get(`${baseURL}/favicon.ico`);
    expect(faviconResponse.status()).toBe(200);
  });

  test('should handle 404 for non-existent pages', async ({ request }) => {
    const response = await request.get(`${baseURL}/non-existent-page`);
    expect(response.status()).toBe(404);
  });

  test('should serve the registration page', async ({ request }) => {
    const response = await request.get(`${baseURL}/register`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    const body = await response.text();
    expect(body).toContain('8,760 Hours');
    expect(body).toContain('Create Account');
  });

  test('should serve the login page', async ({ request }) => {
    const response = await request.get(`${baseURL}/login`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    const body = await response.text();
    expect(body).toContain('8,760 Hours');
    expect(body).toContain('Sign In');
  });

  test('should serve the dashboard page', async ({ request }) => {
    const response = await request.get(`${baseURL}/dashboard`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    const body = await response.text();
    expect(body).toContain('8,760 Hours');
    expect(body).toContain('Dashboard');
  });

  test('should serve the assessment page', async ({ request }) => {
    const response = await request.get(`${baseURL}/assessment`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    const body = await response.text();
    expect(body).toContain('8,760 Hours');
    expect(body).toContain('Life Areas Assessment');
  });

  test('should serve the planning page', async ({ request }) => {
    const response = await request.get(`${baseURL}/planning`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    const body = await response.text();
    expect(body).toContain('8,760 Hours');
    expect(body).toContain('Planning Session');
  });

  test('should serve the progress page', async ({ request }) => {
    const response = await request.get(`${baseURL}/progress`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    const body = await response.text();
    expect(body).toContain('8,760 Hours');
    expect(body).toContain('Progress Tracking');
  });

  test('should have proper security headers', async ({ request }) => {
    const response = await request.get(`${baseURL}/`);
    
    expect(response.status()).toBe(200);
    
    // Check for security headers
    expect(response.headers()['x-frame-options']).toBe('DENY');
    expect(response.headers()['x-content-type-options']).toBe('nosniff');
    expect(response.headers()['referrer-policy']).toBe('strict-origin-when-cross-origin');
    expect(response.headers()['permissions-policy']).toContain('camera=()');
  });

  test('should have proper caching headers', async ({ request }) => {
    const response = await request.get(`${baseURL}/`);
    
    expect(response.status()).toBe(200);
    
    // Check for cache control headers
    expect(response.headers()['cache-control']).toContain('no-store');
  });

  test('should serve Next.js pages correctly', async ({ request }) => {
    const response = await request.get(`${baseURL}/`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['x-powered-by']).toBe('Next.js');
    
    const body = await response.text();
    
    // Check for Next.js specific elements
    expect(body).toContain('__next');
    expect(body).toContain('_next');
  });

  test('should handle CORS properly', async ({ request }) => {
    const response = await request.get(`${baseURL}/`, {
      headers: {
        'Origin': 'https://example.com'
      }
    });
    
    expect(response.status()).toBe(200);
    
    // CORS headers should be present for cross-origin requests
    // Note: This depends on your CORS configuration
  });

  test('should serve with proper content encoding', async ({ request }) => {
    const response = await request.get(`${baseURL}/`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br'
      }
    });
    
    expect(response.status()).toBe(200);
    
    // Check if content is compressed (optional, depends on server config)
    const body = await response.text();
    expect(body.length).toBeGreaterThan(0);
  });

  test('should handle different user agents', async ({ request }) => {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
    ];

    for (const userAgent of userAgents) {
      const response = await request.get(`${baseURL}/`, {
        headers: {
          'User-Agent': userAgent
        }
      });
      
      expect(response.status()).toBe(200);
      
      const body = await response.text();
      expect(body).toContain('8,760 Hours');
    }
  });

  test('should handle HEAD requests', async ({ request }) => {
    const response = await request.head(`${baseURL}/`);
    
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/html');
    
    // HEAD requests should not have a body
    const body = await response.text();
    expect(body).toBe('');
  });

  test('should serve robots.txt', async ({ request }) => {
    const response = await request.get(`${baseURL}/robots.txt`);
    
    // robots.txt might not exist, but if it does, it should be served properly
    if (response.status() === 200) {
      expect(response.headers()['content-type']).toContain('text/plain');
    } else {
      expect(response.status()).toBe(404);
    }
  });

  test('should serve sitemap.xml', async ({ request }) => {
    const response = await request.get(`${baseURL}/sitemap.xml`);
    
    // sitemap.xml might not exist, but if it does, it should be served properly
    if (response.status() === 200) {
      expect(response.headers()['content-type']).toContain('xml');
    } else {
      expect(response.status()).toBe(404);
    }
  });
});
