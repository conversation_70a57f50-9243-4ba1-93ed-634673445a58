import { test, expect } from '@playwright/test';

test.describe('Visual Regression Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Set consistent viewport for visual tests
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test.describe('Page Screenshots', () => {
    test('dashboard page visual test', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Wait for content to load
      await page.waitForSelector('[data-testid="dashboard-content"]', { timeout: 5000 });
      
      // Hide dynamic content that changes between runs
      await page.addStyleTag({
        content: `
          [data-testid="current-time"],
          [data-testid="last-updated"],
          .animate-pulse {
            visibility: hidden !important;
          }
        `
      });
      
      await expect(page).toHaveScreenshot('dashboard-page.png');
    });

    test('assessment page visual test', async ({ page }) => {
      await page.goto('/assessment');
      
      // Wait for assessment content
      await page.waitForSelector('text=Life Areas Assessment', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('assessment-start-page.png');
    });

    test('assessment in progress visual test', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      // Wait for first life area to load
      await page.waitForSelector('h2:has-text("Values & Purpose")', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('assessment-in-progress.png');
    });

    test('assessment with rating selected visual test', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      // Select a rating
      await page.click('[data-testid="rating-5"]');
      
      // Add some notes
      await page.fill('[data-testid="notes-textarea"]', 'This area is very important to me and I feel I\'m doing well here.');
      
      await expect(page).toHaveScreenshot('assessment-with-rating.png');
    });

    test('assessment results visual test', async ({ page }) => {
      // Mock completed assessment
      await page.addInitScript(() => {
        const mockRatings = {
          valuesPurpose: 6,
          contribution: 5,
          location: 4,
          finances: 3,
          career: 7,
          health: 2,
          education: 5,
          relationships: 6,
          emotions: 4,
          character: 5,
          productivity: 3,
          adventure: 4
        };
        
        window.localStorage.setItem('assessment-data', JSON.stringify({
          ratings: mockRatings,
          isComplete: true,
          completedAt: '2024-01-15T10:30:00.000Z' // Fixed date for consistency
        }));
      });

      await page.goto('/assessment/results');
      
      // Wait for results to load
      await page.waitForSelector('text=Assessment Results', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('assessment-results.png');
    });

    test('planning session visual test', async ({ page }) => {
      await page.goto('/planning/session');
      
      // Wait for planning content
      await page.waitForSelector('text=Planning Session', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('planning-session.png');
    });

    test('profile page visual test', async ({ page }) => {
      await page.goto('/profile');
      
      // Wait for profile content
      await page.waitForSelector('text=Profile', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('profile-page.png');
    });
  });

  test.describe('Component Screenshots', () => {
    test('rating scale component visual test', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      // Focus on rating scale component
      const ratingScale = page.locator('[data-testid="rating-scale"]');
      await expect(ratingScale).toBeVisible();
      
      await expect(ratingScale).toHaveScreenshot('rating-scale-component.png');
    });

    test('rating scale with selection visual test', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      // Select rating 4
      await page.click('[data-testid="rating-4"]');
      
      const ratingScale = page.locator('[data-testid="rating-scale"]');
      await expect(ratingScale).toHaveScreenshot('rating-scale-selected.png');
    });

    test('navigation component visual test', async ({ page }) => {
      await page.goto('/dashboard');
      
      const navigation = page.locator('nav');
      await expect(navigation).toBeVisible();
      
      await expect(navigation).toHaveScreenshot('navigation-component.png');
    });

    test('user menu dropdown visual test', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Open user menu
      await page.click('[data-testid="user-menu-trigger"]');
      
      const dropdown = page.locator('[data-testid="user-menu-content"]');
      await expect(dropdown).toBeVisible();
      
      await expect(dropdown).toHaveScreenshot('user-menu-dropdown.png');
    });

    test('notification component visual test', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Trigger a notification
      await page.evaluate(() => {
        // Mock notification system
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg';
        notification.textContent = 'Success! Your changes have been saved.';
        notification.setAttribute('data-testid', 'notification');
        document.body.appendChild(notification);
      });
      
      const notification = page.locator('[data-testid="notification"]');
      await expect(notification).toBeVisible();
      
      await expect(notification).toHaveScreenshot('notification-component.png');
    });
  });

  test.describe('Responsive Visual Tests', () => {
    test('mobile dashboard visual test', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE
      await page.goto('/dashboard');
      
      await page.waitForSelector('[data-testid="dashboard-content"]', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('mobile-dashboard.png');
    });

    test('tablet assessment visual test', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 }); // iPad
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
      
      await page.waitForSelector('h2:has-text("Values & Purpose")', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('tablet-assessment.png');
    });

    test('desktop wide screen visual test', async ({ page }) => {
      await page.setViewportSize({ width: 1920, height: 1080 });
      await page.goto('/dashboard');
      
      await page.waitForSelector('[data-testid="dashboard-content"]', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('desktop-wide-dashboard.png');
    });
  });

  test.describe('Theme Visual Tests', () => {
    test('dark mode visual test', async ({ page }) => {
      // Enable dark mode
      await page.emulateMedia({ colorScheme: 'dark' });
      await page.goto('/dashboard');
      
      // Add dark mode class if needed
      await page.evaluate(() => {
        document.documentElement.classList.add('dark');
      });
      
      await page.waitForSelector('[data-testid="dashboard-content"]', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('dark-mode-dashboard.png');
    });

    test('light mode visual test', async ({ page }) => {
      // Ensure light mode
      await page.emulateMedia({ colorScheme: 'light' });
      await page.goto('/dashboard');
      
      await page.evaluate(() => {
        document.documentElement.classList.remove('dark');
      });
      
      await page.waitForSelector('[data-testid="dashboard-content"]', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('light-mode-dashboard.png');
    });
  });

  test.describe('State Visual Tests', () => {
    test('loading state visual test', async ({ page }) => {
      // Intercept API calls to simulate loading
      await page.route('**/api/**', route => {
        // Delay response to show loading state
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ data: 'mock data' })
          });
        }, 2000);
      });
      
      await page.goto('/dashboard');
      
      // Capture loading state
      const loadingElement = page.locator('[data-testid="loading-spinner"]');
      if (await loadingElement.isVisible()) {
        await expect(loadingElement).toHaveScreenshot('loading-state.png');
      }
    });

    test('error state visual test', async ({ page }) => {
      // Mock error state
      await page.goto('/dashboard');
      
      await page.evaluate(() => {
        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded';
        errorDiv.textContent = 'Error: Unable to load data. Please try again.';
        errorDiv.setAttribute('data-testid', 'error-message');
        document.body.appendChild(errorDiv);
      });
      
      const errorMessage = page.locator('[data-testid="error-message"]');
      await expect(errorMessage).toBeVisible();
      
      await expect(errorMessage).toHaveScreenshot('error-state.png');
    });

    test('empty state visual test', async ({ page }) => {
      // Mock empty assessment state
      await page.addInitScript(() => {
        window.localStorage.setItem('assessment-data', JSON.stringify({
          ratings: {},
          notes: {},
          isComplete: false
        }));
      });
      
      await page.goto('/assessment/results');
      
      // Should show empty state
      await page.waitForSelector('text=No assessment data', { timeout: 5000 });
      
      await expect(page).toHaveScreenshot('empty-state.png');
    });
  });

  test.describe('Interactive State Visual Tests', () => {
    test('button hover state visual test', async ({ page }) => {
      await page.goto('/dashboard');
      
      const button = page.locator('button:has-text("Start Assessment")').first();
      await button.hover();
      
      await expect(button).toHaveScreenshot('button-hover-state.png');
    });

    test('form focus state visual test', async ({ page }) => {
      await page.goto('/profile');
      
      const input = page.locator('input[type="text"]').first();
      await input.focus();
      
      await expect(input).toHaveScreenshot('input-focus-state.png');
    });

    test('dropdown open state visual test', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Open language switcher if available
      const languageSwitcher = page.locator('[data-testid="language-switcher"]');
      if (await languageSwitcher.isVisible()) {
        await languageSwitcher.click();
        
        const dropdown = page.locator('[data-testid="language-dropdown"]');
        await expect(dropdown).toBeVisible();
        
        await expect(dropdown).toHaveScreenshot('language-dropdown-open.png');
      }
    });
  });
});
