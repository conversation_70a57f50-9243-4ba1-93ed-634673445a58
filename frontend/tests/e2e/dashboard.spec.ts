import { test, expect } from '@playwright/test';

test.describe('Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
    
    // Register and login a user
    await page.goto('/register');
    await page.getByLabel(/First Name/i).fill('Dashboard');
    await page.getByLabel(/Last Name/i).fill('Tester');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Complete onboarding
    await page.waitForURL('/onboarding');
    await page.getByRole('button', { name: /Get Started/i }).click();
    await page.waitForURL('/dashboard');
  });

  test('should display dashboard correctly', async ({ page }) => {
    // Should be on dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Check for main heading
    await expect(page.getByRole('heading', { name: /Welcome to 8,760 Hours/i })).toBeVisible();
    
    // Check for user greeting
    await expect(page.getByText(/Dashboard/i)).toBeVisible();
    
    // Check for quick action cards
    await expect(page.getByText(/Start Planning Session/i)).toBeVisible();
    await expect(page.getByText(/Assess Life Areas/i)).toBeVisible();
    await expect(page.getByText(/Track Progress/i)).toBeVisible();
  });

  test('should show correct user information', async ({ page }) => {
    // Check for user name in header
    await expect(page.getByText(/Dashboard/i)).toBeVisible();
    
    // Check for sign out button
    await expect(page.getByRole('button', { name: /Sign Out/i })).toBeVisible();
  });

  test('should navigate to planning from dashboard', async ({ page }) => {
    // Click on planning card
    await page.getByText(/Start Planning Session/i).click();
    
    // Should navigate to planning page
    await expect(page).toHaveURL('/planning');
    await expect(page.getByRole('heading', { name: /Planning Session/i })).toBeVisible();
  });

  test('should navigate to assessment from dashboard', async ({ page }) => {
    // Click on assessment card
    await page.getByText(/Assess Life Areas/i).click();
    
    // Should navigate to assessment page
    await expect(page).toHaveURL('/assessment');
    await expect(page.getByRole('heading', { name: /Life Areas Assessment/i })).toBeVisible();
  });

  test('should navigate to progress from dashboard', async ({ page }) => {
    // Click on progress card
    await page.getByText(/Track Progress/i).click();
    
    // Should navigate to progress page
    await expect(page).toHaveURL('/progress');
    await expect(page.getByRole('heading', { name: /Progress Tracking/i })).toBeVisible();
  });

  test('should show getting started section', async ({ page }) => {
    // Check for getting started section
    await expect(page.getByText(/Getting Started/i)).toBeVisible();
    await expect(page.getByText(/Complete Your Life Assessment/i)).toBeVisible();
    await expect(page.getByText(/Create Your Annual Plan/i)).toBeVisible();
    
    // Check for action buttons
    await expect(page.getByRole('button', { name: /Start Assessment/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Start Planning/i })).toBeVisible();
  });

  test('should navigate from getting started buttons', async ({ page }) => {
    // Click Start Assessment button
    await page.getByRole('button', { name: /Start Assessment/i }).click();
    
    // Should navigate to assessment
    await expect(page).toHaveURL('/assessment');
    
    // Go back to dashboard
    await page.goto('/dashboard');
    
    // Click Start Planning button
    await page.getByRole('button', { name: /Start Planning/i }).click();
    
    // Should navigate to planning
    await expect(page).toHaveURL('/planning');
  });

  test('should show year information', async ({ page }) => {
    const currentYear = new Date().getFullYear();
    
    // Check for current year in planning card
    await expect(page.getByText(currentYear.toString())).toBeVisible();
    
    // Check for year context
    await expect(page.getByText(/Begin your annual life review/i)).toBeVisible();
  });

  test('should display statistics correctly', async ({ page }) => {
    // Initial state should show zeros
    await expect(page.getByText(/0\/12/i)).toBeVisible(); // Assessment areas
    await expect(page.getByText(/Areas assessed this year/i)).toBeVisible();
    
    // Progress entries should be 0 initially
    await expect(page.getByText(/Progress entries this week/i)).toBeVisible();
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Dashboard should still be accessible
    await expect(page.getByRole('heading', { name: /Welcome to 8,760 Hours/i })).toBeVisible();
    await expect(page.getByText(/Start Planning Session/i)).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    
    // Dashboard should still be accessible
    await expect(page.getByRole('heading', { name: /Welcome to 8,760 Hours/i })).toBeVisible();
    
    // Reset to desktop
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should show header navigation', async ({ page }) => {
    // Check for 8,760 Hours branding
    await expect(page.getByText(/8,760 Hours/i)).toBeVisible();
    
    // Check for clock icon
    await expect(page.locator('svg')).toBeVisible();
  });

  test('should handle sign out', async ({ page }) => {
    // Click sign out button
    await page.getByRole('button', { name: /Sign Out/i }).click();
    
    // Should redirect to home page
    await expect(page).toHaveURL('/');
    
    // Should show landing page content
    await expect(page.getByRole('heading', { name: /8,760 Hours/i })).toBeVisible();
    await expect(page.getByRole('link', { name: /Get Started/i })).toBeVisible();
  });

  test('should maintain authentication state', async ({ page }) => {
    // Refresh the page
    await page.reload();
    
    // Should still be on dashboard (authenticated)
    await expect(page).toHaveURL('/dashboard');
    await expect(page.getByRole('heading', { name: /Welcome to 8,760 Hours/i })).toBeVisible();
  });

  test('should show appropriate content for new users', async ({ page }) => {
    // New users should see getting started content
    await expect(page.getByText(/Getting Started/i)).toBeVisible();
    await expect(page.getByText(/Complete Your Life Assessment/i)).toBeVisible();
    await expect(page.getByText(/Create Your Annual Plan/i)).toBeVisible();
    
    // Should show zero statistics
    await expect(page.getByText(/0\/12/i)).toBeVisible();
    await expect(page.getByText(/0/i)).toBeVisible(); // Progress entries
  });

  test('should handle dark mode', async ({ page }) => {
    // Check if dark mode toggle exists (if implemented)
    // For now, just verify the page loads correctly
    await expect(page.getByRole('heading', { name: /Welcome to 8,760 Hours/i })).toBeVisible();
    
    // The page should be styled for dark mode by default based on our implementation
    const body = page.locator('body');
    await expect(body).toBeVisible();
  });

  test('should show correct card layouts', async ({ page }) => {
    // Quick action cards should be in a grid
    const planningCard = page.getByText(/Start Planning Session/i).locator('..');
    const assessmentCard = page.getByText(/Assess Life Areas/i).locator('..');
    const progressCard = page.getByText(/Track Progress/i).locator('..');
    
    await expect(planningCard).toBeVisible();
    await expect(assessmentCard).toBeVisible();
    await expect(progressCard).toBeVisible();
    
    // Cards should be clickable
    await expect(planningCard).toHaveAttribute('class', /cursor-pointer/);
    await expect(assessmentCard).toHaveAttribute('class', /cursor-pointer/);
    await expect(progressCard).toHaveAttribute('class', /cursor-pointer/);
  });

  test('should show proper loading states', async ({ page }) => {
    // The page should load without showing loading spinners on dashboard
    // (since we're using localStorage, not API calls)
    await expect(page.getByRole('heading', { name: /Welcome to 8,760 Hours/i })).toBeVisible();
    
    // Should not show loading spinner
    await expect(page.locator('.animate-spin')).not.toBeVisible();
  });
});
