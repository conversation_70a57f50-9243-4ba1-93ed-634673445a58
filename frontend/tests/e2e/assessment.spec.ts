import { test, expect } from '@playwright/test';

test.describe('Life Areas Assessment', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
    
    // Register and login a user
    await page.goto('/register');
    await page.getByLabel(/First Name/i).fill('Assessment');
    await page.getByLabel(/Last Name/i).fill('Tester');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Complete onboarding
    await page.waitForURL('/onboarding');
    await page.getByRole('button', { name: /Get Started/i }).click();
    await page.waitForURL('/dashboard');
  });

  test('should navigate to assessment page', async ({ page }) => {
    // Click on assessment card from dashboard
    await page.getByText(/Assess Life Areas/i).click();
    
    // Should be on assessment page
    await expect(page).toHaveURL('/assessment');
    await expect(page.getByRole('heading', { name: /Life Areas Assessment/i })).toBeVisible();
    
    // Check for progress indicators
    await expect(page.getByText(/Progress/i)).toBeVisible();
    await expect(page.getByText(/0%/i)).toBeVisible();
  });

  test('should display all 12 life areas', async ({ page }) => {
    await page.goto('/assessment');
    
    // Check for all life areas
    const lifeAreas = [
      'Values & Purpose',
      'Contribution & Impact',
      'Location & Tangibles',
      'Money & Finances',
      'Career & Work',
      'Health & Fitness',
      'Education & Skill Development',
      'Social Life & Relationships',
      'Emotions & Well-Being',
      'Character & Identity',
      'Productivity & Organization',
      'Adventure & Creativity'
    ];
    
    for (const area of lifeAreas) {
      await expect(page.getByText(area)).toBeVisible();
    }
  });

  test('should allow rating a life area', async ({ page }) => {
    await page.goto('/assessment');
    
    // Find the first life area card and rate it
    const firstCard = page.locator('.space-y-6 > div').first();
    
    // Click on rating 5
    await firstCard.getByTestId('rating-5').click();
    
    // Should show the rating label
    await expect(firstCard.getByText(/Excellent/i)).toBeVisible();
    
    // Progress should update
    await expect(page.getByText(/8%/i)).toBeVisible(); // 1/12 = ~8%
  });

  test('should allow adding notes to life areas', async ({ page }) => {
    await page.goto('/assessment');
    
    // Find the first life area card
    const firstCard = page.locator('.space-y-6 > div').first();
    
    // Add a note
    const noteText = 'This is a test note for my life area assessment.';
    await firstCard.getByPlaceholder(/Share your thoughts/i).fill(noteText);
    
    // The note should be saved (auto-save)
    await page.waitForTimeout(3000); // Wait for auto-save
    
    // Refresh page and check if note persists
    await page.reload();
    await expect(firstCard.getByDisplayValue(noteText)).toBeVisible();
  });

  test('should show expandable details for life areas', async ({ page }) => {
    await page.goto('/assessment');
    
    // Find the first life area card
    const firstCard = page.locator('.space-y-6 > div').first();
    
    // Click "Show Details"
    await firstCard.getByText(/Show Details/i).click();
    
    // Should show detailed description
    await expect(firstCard.getByText(/About This Life Area/i)).toBeVisible();
    await expect(firstCard.getByText(/Improvement Areas/i)).toBeVisible();
    
    // Click "Hide Details"
    await firstCard.getByText(/Hide Details/i).click();
    
    // Details should be hidden
    await expect(firstCard.getByText(/About This Life Area/i)).not.toBeVisible();
  });

  test('should show help and examples', async ({ page }) => {
    await page.goto('/assessment');
    
    // Find the first life area card
    const firstCard = page.locator('.space-y-6 > div').first();
    
    // Click "Help"
    await firstCard.getByText(/Help/i).click();
    
    // Should show assessment questions
    await expect(firstCard.getByText(/Assessment Questions/i)).toBeVisible();
    
    // Click "Examples"
    await firstCard.getByText(/Examples/i).click();
    
    // Should show examples
    await expect(firstCard.getByText(/Poor.*Examples/i)).toBeVisible();
    await expect(firstCard.getByText(/Excellent.*Examples/i)).toBeVisible();
  });

  test('should filter life areas by category', async ({ page }) => {
    await page.goto('/assessment');
    
    // Select Foundation category
    await page.selectOption('select', 'foundation');
    
    // Should show only foundation areas
    await expect(page.getByText(/Values & Purpose/i)).toBeVisible();
    await expect(page.getByText(/Location & Tangibles/i)).toBeVisible();
    await expect(page.getByText(/Money & Finances/i)).toBeVisible();
    
    // Should not show growth areas
    await expect(page.getByText(/Career & Work/i)).not.toBeVisible();
  });

  test('should complete full assessment', async ({ page }) => {
    await page.goto('/assessment');
    
    // Rate all 12 life areas
    const ratings = [7, 6, 5, 4, 6, 5, 7, 6, 5, 6, 4, 7];
    
    for (let i = 0; i < 12; i++) {
      const card = page.locator('.space-y-6 > div').nth(i);
      await card.getByTestId(`rating-${ratings[i]}`).click();
      await page.waitForTimeout(500); // Small delay between ratings
    }
    
    // Progress should be 100%
    await expect(page.getByText(/100%/i)).toBeVisible();
    
    // Complete Assessment button should be enabled
    const completeButton = page.getByRole('button', { name: /Complete Assessment/i });
    await expect(completeButton).toBeEnabled();
    
    // Click Complete Assessment
    await completeButton.click();
    
    // Should navigate to results page
    await expect(page).toHaveURL('/assessment/results');
  });

  test('should show assessment results', async ({ page }) => {
    // First complete an assessment
    await page.goto('/assessment');
    
    // Rate all areas quickly
    for (let i = 1; i <= 12; i++) {
      const card = page.locator('.space-y-6 > div').nth(i - 1);
      await card.getByTestId(`rating-${5}`).click(); // Rate all as 5
    }
    
    // Complete assessment
    await page.getByRole('button', { name: /Complete Assessment/i }).click();
    
    // Should be on results page
    await expect(page).toHaveURL('/assessment/results');
    await expect(page.getByRole('heading', { name: /Assessment Results/i })).toBeVisible();
    
    // Check for key metrics
    await expect(page.getByText(/Overall Average/i)).toBeVisible();
    await expect(page.getByText(/5.0/i)).toBeVisible(); // Average should be 5.0
    
    // Check for category breakdown
    await expect(page.getByText(/Category Breakdown/i)).toBeVisible();
    
    // Check for next steps
    await expect(page.getByText(/Next Steps/i)).toBeVisible();
    await expect(page.getByText(/Create Your Annual Planning Session/i)).toBeVisible();
  });

  test('should save and auto-save assessment progress', async ({ page }) => {
    await page.goto('/assessment');
    
    // Rate a few areas
    await page.locator('.space-y-6 > div').first().getByTestId('rating-6').click();
    await page.locator('.space-y-6 > div').nth(1).getByTestId('rating-4').click();
    
    // Wait for auto-save
    await page.waitForTimeout(3000);
    
    // Refresh page
    await page.reload();
    
    // Progress should be maintained
    await expect(page.getByText(/17%/i)).toBeVisible(); // 2/12 = ~17%
    
    // Ratings should be maintained
    const firstCard = page.locator('.space-y-6 > div').first();
    await expect(firstCard.getByText(/Outstanding/i)).toBeVisible(); // Rating 6
  });

  test('should handle accessibility features', async ({ page }) => {
    await page.goto('/assessment');
    
    // Check for proper ARIA labels on rating buttons
    const firstCard = page.locator('.space-y-6 > div').first();
    const ratingButton = firstCard.getByTestId('rating-5');
    
    await expect(ratingButton).toHaveAttribute('aria-label', /Rate 5 - Excellent/i);
    
    // Test keyboard navigation
    await ratingButton.focus();
    await expect(ratingButton).toBeFocused();
    
    // Test with keyboard
    await page.keyboard.press('Enter');
    await expect(firstCard.getByText(/Excellent/i)).toBeVisible();
  });
});
