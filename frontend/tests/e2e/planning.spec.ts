import { test, expect } from '@playwright/test';

test.describe('Planning System', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
    
    // Register and login a user
    await page.goto('/register');
    await page.getByLabel(/First Name/i).fill('Planning');
    await page.getByLabel(/Last Name/i).fill('Tester');
    await page.getByLabel(/Email/i).fill('<EMAIL>');
    await page.getByLabel(/Password/i).fill('SecurePassword123!');
    await page.getByRole('button', { name: /Create Account/i }).click();
    
    // Complete onboarding
    await page.waitForURL('/onboarding');
    await page.getByRole('button', { name: /Get Started/i }).click();
    await page.waitForURL('/dashboard');
  });

  test('should navigate to planning page', async ({ page }) => {
    // Click on planning card from dashboard
    await page.getByText(/Start Planning Session/i).click();
    
    // Should be on planning page
    await expect(page).toHaveURL('/planning');
    await expect(page.getByRole('heading', { name: /Planning Session/i })).toBeVisible();
  });

  test('should show goal templates for new users', async ({ page }) => {
    await page.goto('/planning');
    
    // Should show goal templates section
    await expect(page.getByText(/Goal Templates/i)).toBeVisible();
    await expect(page.getByText(/Get started with these proven goal templates/i)).toBeVisible();
    
    // Check for template cards
    await expect(page.getByText(/Improve Physical Health & Fitness/i)).toBeVisible();
    await expect(page.getByText(/Advance Career & Professional Development/i)).toBeVisible();
    await expect(page.getByText(/Improve Financial Security/i)).toBeVisible();
  });

  test('should create goal from template', async ({ page }) => {
    await page.goto('/planning');
    
    // Click on first template
    await page.getByRole('button', { name: /Use Template/i }).first().click();
    
    // Should be on goal form
    await expect(page.getByRole('heading', { name: /Create New Goal/i })).toBeVisible();
    
    // Template data should be pre-filled
    await expect(page.getByDisplayValue(/Improve Physical Health & Fitness/i)).toBeVisible();
    
    // Fill in additional details
    await page.getByLabel(/Target Completion Date/i).fill('2024-12-31');
    
    // Submit the goal
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Should return to planning page with goal created
    await expect(page).toHaveURL('/planning');
    await expect(page.getByText(/Improve Physical Health & Fitness/i)).toBeVisible();
  });

  test('should create custom goal', async ({ page }) => {
    await page.goto('/planning');
    
    // Click New Goal button
    await page.getByRole('button', { name: /New Goal/i }).click();
    
    // Should be on goal form
    await expect(page.getByRole('heading', { name: /Create New Goal/i })).toBeVisible();
    
    // Fill out the form
    await page.getByLabel(/Goal Title/i).fill('Learn Spanish Fluently');
    await page.getByLabel(/Goal Description/i).fill('Become conversationally fluent in Spanish by practicing daily and taking classes.');
    
    // Select category
    await page.getByRole('radio', { name: /Major Goal/i }).click();
    
    // Select priority
    await page.getByRole('radio', { name: /High Priority/i }).click();
    
    // Fill SMART criteria
    await page.getByLabel(/Specific/i).fill('Achieve conversational fluency in Spanish');
    await page.getByLabel(/Measurable/i).fill('Pass B2 level Spanish exam and hold 30-minute conversations');
    await page.getByLabel(/Achievable/i).fill('Study 1 hour daily and take weekly classes');
    await page.getByLabel(/Relevant/i).fill('Important for career advancement and personal growth');
    await page.getByLabel(/Time-Bound/i).fill('Complete by December 31, 2024');
    
    // Select life areas
    await page.getByText(/Education/i).click(); // Education & Skills life area
    
    // Set target date
    await page.getByLabel(/Target Completion Date/i).fill('2024-12-31');
    
    // Add tags
    await page.getByPlaceholder(/Add a tag/i).fill('language');
    await page.getByRole('button', { name: /\+/i }).click();
    
    // Submit the goal
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Should return to planning page
    await expect(page).toHaveURL('/planning');
    await expect(page.getByText(/Learn Spanish Fluently/i)).toBeVisible();
  });

  test('should validate goal form', async ({ page }) => {
    await page.goto('/planning');
    await page.getByRole('button', { name: /New Goal/i }).click();
    
    // Try to submit empty form
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Should show validation errors
    await expect(page.getByText(/Goal title is required/i)).toBeVisible();
    await expect(page.getByText(/Please provide a detailed description/i)).toBeVisible();
    await expect(page.getByText(/Specific description is required/i)).toBeVisible();
  });

  test('should edit existing goal', async ({ page }) => {
    await page.goto('/planning');
    
    // Create a goal first
    await page.getByRole('button', { name: /New Goal/i }).click();
    await page.getByLabel(/Goal Title/i).fill('Test Goal');
    await page.getByLabel(/Goal Description/i).fill('This is a test goal for editing.');
    await page.getByLabel(/Specific/i).fill('Test specific criteria');
    await page.getByLabel(/Measurable/i).fill('Test measurable criteria');
    await page.getByLabel(/Achievable/i).fill('Test achievable criteria');
    await page.getByLabel(/Relevant/i).fill('Test relevant criteria');
    await page.getByLabel(/Time-Bound/i).fill('Test time-bound criteria');
    await page.getByText(/Values/i).click(); // Select a life area
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Now edit the goal
    await page.getByRole('button', { name: /Edit/i }).click();
    
    // Should be on edit form
    await expect(page.getByRole('heading', { name: /Edit Goal/i })).toBeVisible();
    
    // Update the title
    await page.getByLabel(/Goal Title/i).fill('Updated Test Goal');
    
    // Submit changes
    await page.getByRole('button', { name: /Update Goal/i }).click();
    
    // Should show updated goal
    await expect(page.getByText(/Updated Test Goal/i)).toBeVisible();
  });

  test('should filter and view goals', async ({ page }) => {
    await page.goto('/planning');
    
    // Create multiple goals with different statuses
    // Goal 1 - Major goal
    await page.getByRole('button', { name: /New Goal/i }).click();
    await page.getByLabel(/Goal Title/i).fill('Major Goal 1');
    await page.getByLabel(/Goal Description/i).fill('Description for major goal');
    await page.getByRole('radio', { name: /Major Goal/i }).click();
    await page.getByLabel(/Specific/i).fill('Specific criteria');
    await page.getByLabel(/Measurable/i).fill('Measurable criteria');
    await page.getByLabel(/Achievable/i).fill('Achievable criteria');
    await page.getByLabel(/Relevant/i).fill('Relevant criteria');
    await page.getByLabel(/Time-Bound/i).fill('Time-bound criteria');
    await page.getByText(/Values/i).click();
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Goal 2 - Habit goal
    await page.getByRole('button', { name: /New Goal/i }).click();
    await page.getByLabel(/Goal Title/i).fill('Habit Goal 1');
    await page.getByLabel(/Goal Description/i).fill('Description for habit goal');
    await page.getByRole('radio', { name: /Habit Goal/i }).click();
    await page.getByLabel(/Specific/i).fill('Specific criteria');
    await page.getByLabel(/Measurable/i).fill('Measurable criteria');
    await page.getByLabel(/Achievable/i).fill('Achievable criteria');
    await page.getByLabel(/Relevant/i).fill('Relevant criteria');
    await page.getByLabel(/Time-Bound/i).fill('Time-bound criteria');
    await page.getByText(/Health/i).click();
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Filter by category
    await page.selectOption('select[value="all"]', 'major');
    await expect(page.getByText(/Major Goal 1/i)).toBeVisible();
    await expect(page.getByText(/Habit Goal 1/i)).not.toBeVisible();
    
    // Switch to list view
    await page.getByRole('button', { name: /List/i }).click();
    
    // Switch back to grid view
    await page.getByRole('button', { name: /Grid/i }).click();
  });

  test('should manage goal status', async ({ page }) => {
    await page.goto('/planning');
    
    // Create a goal
    await page.getByRole('button', { name: /New Goal/i }).click();
    await page.getByLabel(/Goal Title/i).fill('Status Test Goal');
    await page.getByLabel(/Goal Description/i).fill('Goal for testing status changes');
    await page.getByLabel(/Specific/i).fill('Specific criteria');
    await page.getByLabel(/Measurable/i).fill('Measurable criteria');
    await page.getByLabel(/Achievable/i).fill('Achievable criteria');
    await page.getByLabel(/Relevant/i).fill('Relevant criteria');
    await page.getByLabel(/Time-Bound/i).fill('Time-bound criteria');
    await page.getByText(/Values/i).click();
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Click on goal actions menu
    await page.getByRole('button', { name: /⋯/i }).click();
    
    // Start the goal
    await page.getByRole('button', { name: /Start/i }).click();
    
    // Goal status should update
    await expect(page.getByText(/In Progress/i)).toBeVisible();
    
    // Complete the goal
    await page.getByRole('button', { name: /⋯/i }).click();
    await page.getByRole('button', { name: /Complete/i }).click();
    
    // Goal should show as completed
    await expect(page.getByText(/Completed/i)).toBeVisible();
  });

  test('should show planning statistics', async ({ page }) => {
    await page.goto('/planning');
    
    // Initially should show 0 goals
    await expect(page.getByText(/Total Goals.*0/i)).toBeVisible();
    
    // Create a goal
    await page.getByRole('button', { name: /New Goal/i }).click();
    await page.getByLabel(/Goal Title/i).fill('Stats Test Goal');
    await page.getByLabel(/Goal Description/i).fill('Goal for testing statistics');
    await page.getByLabel(/Specific/i).fill('Specific criteria');
    await page.getByLabel(/Measurable/i).fill('Measurable criteria');
    await page.getByLabel(/Achievable/i).fill('Achievable criteria');
    await page.getByLabel(/Relevant/i).fill('Relevant criteria');
    await page.getByLabel(/Time-Bound/i).fill('Time-bound criteria');
    await page.getByText(/Values/i).click();
    await page.getByRole('button', { name: /Create Goal/i }).click();
    
    // Statistics should update
    await expect(page.getByText(/Total Goals.*1/i)).toBeVisible();
    await expect(page.getByText(/In Progress.*0/i)).toBeVisible();
    
    // Start the goal
    await page.getByRole('button', { name: /⋯/i }).click();
    await page.getByRole('button', { name: /Start/i }).click();
    
    // In Progress count should update
    await expect(page.getByText(/In Progress.*1/i)).toBeVisible();
  });
});
