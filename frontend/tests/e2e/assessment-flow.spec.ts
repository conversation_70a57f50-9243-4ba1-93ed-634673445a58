import { test, expect } from '@playwright/test';

test.describe('Assessment Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test.describe('Starting Assessment', () => {
    test('should start new assessment from dashboard', async ({ page }) => {
      // Navigate to assessment from dashboard
      await page.click('text=Start Assessment');
      await expect(page).toHaveURL(/.*\/assessment/);

      // Should show first life area
      await expect(page.locator('h2:has-text("Values & Purpose")')).toBeVisible();
      await expect(page.locator('text=Your core values, life mission, and sense of purpose')).toBeVisible();
    });

    test('should show assessment overview before starting', async ({ page }) => {
      await page.goto('/assessment');

      // Should show assessment introduction
      await expect(page.locator('text=Life Areas Assessment')).toBeVisible();
      await expect(page.locator('text=Evaluate your current state across all life areas')).toBeVisible();
      
      // Should show start button
      await expect(page.locator('button:has-text("Start Assessment")')).toBeVisible();
    });

    test('should continue existing assessment', async ({ page }) => {
      // Mock existing assessment data
      await page.addInitScript(() => {
        window.localStorage.setItem('assessment-data', JSON.stringify({
          ratings: { valuesPurpose: 5 },
          currentArea: 'contribution',
          isComplete: false
        }));
      });

      await page.goto('/assessment');

      // Should show continue button instead of start
      await expect(page.locator('button:has-text("Continue Assessment")')).toBeVisible();
      
      await page.click('button:has-text("Continue Assessment")');
      
      // Should resume from where left off
      await expect(page.locator('h2:has-text("Contribution & Impact")')).toBeVisible();
    });
  });

  test.describe('Rating Life Areas', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
    });

    test('should allow rating selection', async ({ page }) => {
      // Should show rating scale
      await expect(page.locator('text=Rate this life area')).toBeVisible();

      // Click rating 5
      await page.click('[data-testid="rating-5"]');

      // Should highlight selected rating
      await expect(page.locator('[data-testid="rating-5"]')).toHaveClass(/ring-2/);
      
      // Should show rating label
      await expect(page.locator('text=5 - Excellent')).toBeVisible();
    });

    test('should allow changing rating', async ({ page }) => {
      // Select initial rating
      await page.click('[data-testid="rating-4"]');
      await expect(page.locator('[data-testid="rating-4"]')).toHaveClass(/ring-2/);

      // Change to different rating
      await page.click('[data-testid="rating-6"]');
      await expect(page.locator('[data-testid="rating-6"]')).toHaveClass(/ring-2/);
      await expect(page.locator('[data-testid="rating-4"]')).not.toHaveClass(/ring-2/);
    });

    test('should allow adding notes', async ({ page }) => {
      const notesTextarea = page.locator('[data-testid="notes-textarea"]');
      
      await notesTextarea.fill('This area is important to me because...');
      
      // Should save notes
      await expect(notesTextarea).toHaveValue('This area is important to me because...');
    });

    test('should show rating descriptions on hover', async ({ page }) => {
      // Hover over rating button
      await page.hover('[data-testid="rating-7"]');
      
      // Should show description tooltip
      await expect(page.locator('text=Perfect - This area of my life is exactly where I want it to be')).toBeVisible();
    });

    test('should validate rating before proceeding', async ({ page }) => {
      // Try to proceed without rating
      const nextButton = page.locator('button:has-text("Next Area")');
      
      // Next button should be disabled
      await expect(nextButton).toBeDisabled();
      
      // Select rating
      await page.click('[data-testid="rating-3"]');
      
      // Next button should be enabled
      await expect(nextButton).toBeEnabled();
    });
  });

  test.describe('Navigation Between Areas', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
    });

    test('should navigate to next area', async ({ page }) => {
      // Rate current area
      await page.click('[data-testid="rating-5"]');
      
      // Click next
      await page.click('button:has-text("Next Area")');
      
      // Should move to next area
      await expect(page.locator('h2:has-text("Contribution & Impact")')).toBeVisible();
    });

    test('should navigate to previous area', async ({ page }) => {
      // Rate first area and move to second
      await page.click('[data-testid="rating-5"]');
      await page.click('button:has-text("Next Area")');
      
      // Should be on second area
      await expect(page.locator('h2:has-text("Contribution & Impact")')).toBeVisible();
      
      // Click previous
      await page.click('button:has-text("Previous Area")');
      
      // Should return to first area
      await expect(page.locator('h2:has-text("Values & Purpose")')).toBeVisible();
      
      // Should preserve previous rating
      await expect(page.locator('[data-testid="rating-5"]')).toHaveClass(/ring-2/);
    });

    test('should show progress indicator', async ({ page }) => {
      // Should show progress
      await expect(page.locator('text=1 of 12')).toBeVisible();
      
      // Rate and move to next area
      await page.click('[data-testid="rating-4"]');
      await page.click('button:has-text("Next Area")');
      
      // Progress should update
      await expect(page.locator('text=2 of 12')).toBeVisible();
    });

    test('should not show previous button on first area', async ({ page }) => {
      // Should not show previous button on first area
      await expect(page.locator('button:has-text("Previous Area")')).not.toBeVisible();
    });

    test('should show area navigation breadcrumbs', async ({ page }) => {
      // Should show current area in breadcrumb
      await expect(page.locator('[data-testid="area-breadcrumb"]')).toContainText('Values & Purpose');
      
      // Rate and move to next
      await page.click('[data-testid="rating-5"]');
      await page.click('button:has-text("Next Area")');
      
      // Breadcrumb should update
      await expect(page.locator('[data-testid="area-breadcrumb"]')).toContainText('Contribution & Impact');
    });
  });

  test.describe('Assessment Completion', () => {
    test('should complete full assessment', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');

      // Rate all 12 life areas
      const lifeAreas = [
        'Values & Purpose', 'Contribution & Impact', 'Location & Tangibles',
        'Money & Finances', 'Career & Work', 'Health & Fitness',
        'Education & Skill Development', 'Social Life & Relationships',
        'Emotions & Well-Being', 'Character & Identity',
        'Productivity & Organization', 'Adventure & Creativity'
      ];

      for (let i = 0; i < lifeAreas.length; i++) {
        // Verify current area
        await expect(page.locator(`h2:has-text("${lifeAreas[i]}")`)).toBeVisible();
        
        // Rate the area
        const rating = (i % 7) + 1; // Vary ratings 1-7
        await page.click(`[data-testid="rating-${rating}"]`);
        
        // Add optional note
        if (i % 3 === 0) {
          await page.fill('[data-testid="notes-textarea"]', `Notes for ${lifeAreas[i]}`);
        }
        
        // Navigate to next area or complete
        if (i < lifeAreas.length - 1) {
          await page.click('button:has-text("Next Area")');
        } else {
          await page.click('button:has-text("Complete Assessment")');
        }
      }

      // Should redirect to results page
      await expect(page).toHaveURL(/.*\/assessment\/results/);
      await expect(page.locator('text=Assessment Complete!')).toBeVisible();
    });

    test('should save progress automatically', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');

      // Rate first area
      await page.click('[data-testid="rating-6"]');
      await page.fill('[data-testid="notes-textarea"]', 'Important notes');

      // Refresh page
      await page.reload();

      // Should restore progress
      await expect(page.locator('[data-testid="rating-6"]')).toHaveClass(/ring-2/);
      await expect(page.locator('[data-testid="notes-textarea"]')).toHaveValue('Important notes');
    });

    test('should handle assessment interruption', async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');

      // Rate a few areas
      await page.click('[data-testid="rating-5"]');
      await page.click('button:has-text("Next Area")');
      await page.click('[data-testid="rating-4"]');

      // Navigate away
      await page.goto('/dashboard');

      // Return to assessment
      await page.goto('/assessment');

      // Should offer to continue
      await expect(page.locator('button:has-text("Continue Assessment")')).toBeVisible();
      
      await page.click('button:has-text("Continue Assessment")');
      
      // Should resume from correct area
      await expect(page.locator('h2:has-text("Contribution & Impact")')).toBeVisible();
      await expect(page.locator('[data-testid="rating-4"]')).toHaveClass(/ring-2/);
    });
  });

  test.describe('Assessment Results', () => {
    test.beforeEach(async ({ page }) => {
      // Mock completed assessment
      await page.addInitScript(() => {
        const mockRatings = {
          valuesPurpose: 6,
          contribution: 5,
          location: 4,
          finances: 3,
          career: 7,
          health: 2,
          education: 5,
          relationships: 6,
          emotions: 4,
          character: 5,
          productivity: 3,
          adventure: 4
        };
        
        window.localStorage.setItem('assessment-data', JSON.stringify({
          ratings: mockRatings,
          isComplete: true,
          completedAt: new Date().toISOString()
        }));
      });

      await page.goto('/assessment/results');
    });

    test('should display assessment results', async ({ page }) => {
      // Should show results page
      await expect(page.locator('text=Assessment Results')).toBeVisible();
      
      // Should show overall average
      await expect(page.locator('[data-testid="overall-average"]')).toBeVisible();
      
      // Should show strong areas
      await expect(page.locator('text=Strong Areas')).toBeVisible();
      
      // Should show improvement areas
      await expect(page.locator('text=Areas for Improvement')).toBeVisible();
    });

    test('should show detailed breakdown', async ({ page }) => {
      // Should show all life areas with ratings
      await expect(page.locator('text=Values & Purpose')).toBeVisible();
      await expect(page.locator('text=Career & Work')).toBeVisible();
      
      // Should show rating displays
      await expect(page.locator('[data-testid="rating-display-valuesPurpose"]')).toBeVisible();
    });

    test('should allow retaking assessment', async ({ page }) => {
      await page.click('button:has-text("Retake Assessment")');
      
      // Should confirm retake
      await expect(page.locator('text=Are you sure you want to retake the assessment?')).toBeVisible();
      
      await page.click('button:has-text("Yes, Retake")');
      
      // Should redirect to assessment start
      await expect(page).toHaveURL(/.*\/assessment/);
      await expect(page.locator('button:has-text("Start Assessment")')).toBeVisible();
    });

    test('should navigate to planning session', async ({ page }) => {
      await page.click('button:has-text("Start Planning Session")');
      
      // Should redirect to planning
      await expect(page).toHaveURL(/.*\/planning\/session/);
    });
  });

  test.describe('Accessibility', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/assessment');
      await page.click('button:has-text("Start Assessment")');
    });

    test('should support keyboard navigation', async ({ page }) => {
      // Tab through rating buttons
      await page.keyboard.press('Tab');
      await expect(page.locator('[data-testid="rating-1"]')).toBeFocused();
      
      // Arrow keys should navigate between ratings
      await page.keyboard.press('ArrowRight');
      await expect(page.locator('[data-testid="rating-2"]')).toBeFocused();
      
      // Enter should select rating
      await page.keyboard.press('Enter');
      await expect(page.locator('[data-testid="rating-2"]')).toHaveClass(/ring-2/);
    });

    test('should have proper ARIA labels', async ({ page }) => {
      // Rating buttons should have aria-labels
      await expect(page.locator('[data-testid="rating-5"]')).toHaveAttribute('aria-label', 'Rate 5 - Excellent');
      
      // Progress indicator should have aria-label
      await expect(page.locator('[role="progressbar"]')).toHaveAttribute('aria-label');
    });

    test('should announce rating changes to screen readers', async ({ page }) => {
      // Click rating
      await page.click('[data-testid="rating-4"]');
      
      // Should have aria-live region with announcement
      await expect(page.locator('[aria-live="polite"]')).toContainText('Rating selected: 4 - Very Good');
    });
  });
});
