import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { Inter } from 'next/font/google';
import { locales } from '@/i18n/config';
import { Providers } from '@/components/providers';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={`${inter.className} bg-background text-foreground`}>
        <NextIntlClientProvider messages={messages}>
          <Providers>
            {children}
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}

export const metadata = {
  title: {
    template: '%s | 8,760 Hours',
    default: '8,760 Hours - Systematic Life Planning',
  },
  description: 'Transform your life with systematic annual planning using the 8,760 Hours methodology.',
  keywords: ['life planning', 'goal setting', 'personal development', 'productivity', 'life areas'],
  authors: [{ name: '8,760 Hours Team' }],
  creator: '8,760 Hours',
  publisher: '8,760 Hours',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en',
      'es-ES': '/es',
      'fr-FR': '/fr',
      'de-DE': '/de',
      'ja-JP': '/ja',
      'zh-CN': '/zh',
      'af-ZA': '/af',
      'zu-ZA': '/zu',
      'x-l33t': '/l33t',
      'x-upgoer': '/upgoer',
    },
  },
  openGraph: {
    title: '8,760 Hours - Systematic Life Planning',
    description: 'Transform your life with systematic annual planning using the 8,760 Hours methodology.',
    url: '/',
    siteName: '8,760 Hours',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '8,760 Hours - Systematic Life Planning',
    description: 'Transform your life with systematic annual planning using the 8,760 Hours methodology.',
    creator: '@8760hours',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};
