/**
 * Task Analytics API Route
 * 
 * Provides analytics and statistics for task management:
 * - GET /api/v1/tasks/analytics - Get comprehensive task analytics
 */

import { NextRequest, NextResponse } from 'next/server';
import { Task, TaskStatus, TaskPriority, TaskAnalytics } from '@/types/task';

// Mock database - In production, this would be replaced with actual database calls
const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Complete project documentation',
    description: 'Write comprehensive documentation for the new feature',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.HIGH,
    estimatedHours: 8,
    actualHours: 3.5,
    tags: ['documentation', 'project'],
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-16T14:30:00Z'),
    lifeAreaId: 'career',
  },
  {
    id: '2',
    title: 'Review code changes',
    description: 'Review pull requests from team members',
    status: TaskStatus.TODO,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 2,
    actualHours: 0,
    tags: ['review', 'code'],
    createdAt: new Date('2024-01-16T09:00:00Z'),
    updatedAt: new Date('2024-01-16T09:00:00Z'),
    lifeAreaId: 'career',
  },
  {
    id: '3',
    title: 'Update dependencies',
    description: 'Update all npm packages to latest versions',
    status: TaskStatus.DONE,
    priority: TaskPriority.LOW,
    estimatedHours: 1,
    actualHours: 1.2,
    tags: ['maintenance', 'dependencies'],
    createdAt: new Date('2024-01-14T16:00:00Z'),
    updatedAt: new Date('2024-01-15T11:00:00Z'),
    completedAt: new Date('2024-01-15T11:00:00Z'),
    lifeAreaId: 'career',
  },
  {
    id: '4',
    title: 'Morning workout',
    description: 'Complete 30-minute cardio session',
    status: TaskStatus.DONE,
    priority: TaskPriority.HIGH,
    estimatedHours: 0.5,
    actualHours: 0.5,
    tags: ['fitness', 'health'],
    createdAt: new Date('2024-01-16T06:00:00Z'),
    updatedAt: new Date('2024-01-16T06:30:00Z'),
    completedAt: new Date('2024-01-16T06:30:00Z'),
    lifeAreaId: 'health',
  },
  {
    id: '5',
    title: 'Read chapter 5',
    description: 'Read chapter 5 of "Atomic Habits"',
    status: TaskStatus.BLOCKED,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 1,
    actualHours: 0,
    tags: ['reading', 'personal-development'],
    createdAt: new Date('2024-01-15T20:00:00Z'),
    updatedAt: new Date('2024-01-16T20:00:00Z'),
    lifeAreaId: 'personal-development',
  },
];

/**
 * GET /api/v1/tasks/analytics
 * Get comprehensive task analytics and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const analytics: TaskAnalytics = calculateTaskAnalytics(mockTasks);
    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error calculating task analytics:', error);
    return NextResponse.json(
      { error: 'Failed to calculate task analytics' },
      { status: 500 }
    );
  }
}

/**
 * Calculate comprehensive task analytics
 */
function calculateTaskAnalytics(tasks: Task[]): TaskAnalytics {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Basic counts
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === TaskStatus.DONE).length;
  const inProgressTasks = tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length;
  const overdueTasks = tasks.filter(t => 
    t.dueDate && 
    new Date(t.dueDate) < now && 
    t.status !== TaskStatus.DONE
  ).length;

  // Time calculations
  const totalTimeSpent = tasks.reduce((sum, task) => sum + task.actualHours, 0);
  const completedTasksWithTime = tasks.filter(t => 
    t.status === TaskStatus.DONE && t.actualHours > 0
  );
  const averageCompletionTime = completedTasksWithTime.length > 0
    ? completedTasksWithTime.reduce((sum, task) => sum + task.actualHours, 0) / completedTasksWithTime.length
    : 0;

  // Priority distribution
  const tasksByPriority = {
    high: tasks.filter(t => t.priority === TaskPriority.HIGH).length,
    medium: tasks.filter(t => t.priority === TaskPriority.MEDIUM).length,
    low: tasks.filter(t => t.priority === TaskPriority.LOW).length,
  };

  // Status distribution
  const tasksByStatus = {
    todo: tasks.filter(t => t.status === TaskStatus.TODO).length,
    inProgress: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length,
    done: tasks.filter(t => t.status === TaskStatus.DONE).length,
    blocked: tasks.filter(t => t.status === TaskStatus.BLOCKED).length,
    deferred: tasks.filter(t => t.status === TaskStatus.DEFERRED).length,
  };

  // Life area distribution
  const lifeAreaMap = new Map<string, { taskCount: number; completedCount: number; timeSpent: number }>();
  
  tasks.forEach(task => {
    const lifeAreaId = task.lifeAreaId || 'unassigned';
    const current = lifeAreaMap.get(lifeAreaId) || { taskCount: 0, completedCount: 0, timeSpent: 0 };
    
    current.taskCount++;
    current.timeSpent += task.actualHours;
    if (task.status === TaskStatus.DONE) {
      current.completedCount++;
    }
    
    lifeAreaMap.set(lifeAreaId, current);
  });

  const tasksByLifeArea = Array.from(lifeAreaMap.entries()).map(([lifeAreaId, stats]) => ({
    lifeAreaId,
    lifeAreaName: getLifeAreaName(lifeAreaId),
    taskCount: stats.taskCount,
    completedCount: stats.completedCount,
    timeSpent: stats.timeSpent,
  }));

  // Productivity trends (last 30 days)
  const productivityTrends = generateProductivityTrends(tasks, thirtyDaysAgo, now);

  return {
    totalTasks,
    completedTasks,
    inProgressTasks,
    overdueTasks,
    totalTimeSpent,
    averageCompletionTime,
    tasksByPriority,
    tasksByStatus,
    tasksByLifeArea,
    productivityTrends,
  };
}

/**
 * Generate productivity trends for the last 30 days
 */
function generateProductivityTrends(tasks: Task[], startDate: Date, endDate: Date) {
  const trends = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    const dateStr = currentDate.toISOString().split('T')[0];
    
    // Tasks completed on this date
    const tasksCompleted = tasks.filter(task => 
      task.completedAt && 
      task.completedAt.toISOString().split('T')[0] === dateStr
    ).length;

    // Time spent on this date (approximation based on completion)
    const timeSpent = tasks
      .filter(task => 
        task.completedAt && 
        task.completedAt.toISOString().split('T')[0] === dateStr
      )
      .reduce((sum, task) => sum + task.actualHours, 0);

    trends.push({
      date: dateStr,
      tasksCompleted,
      timeSpent,
    });

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return trends;
}

/**
 * Get life area name by ID (mock implementation)
 */
function getLifeAreaName(lifeAreaId: string): string {
  const lifeAreaNames: Record<string, string> = {
    'career': 'Career & Work',
    'health': 'Health & Fitness',
    'personal-development': 'Personal Development',
    'relationships': 'Relationships',
    'finances': 'Finances',
    'recreation': 'Recreation & Hobbies',
    'unassigned': 'Unassigned',
  };

  return lifeAreaNames[lifeAreaId] || lifeAreaId;
}
