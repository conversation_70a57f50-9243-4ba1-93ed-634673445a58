/**
 * Tasks API Routes
 * 
 * RESTful API endpoints for task management:
 * - GET /api/v1/tasks - List tasks with filtering and pagination
 * - POST /api/v1/tasks - Create new task
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  Task, 
  TaskStatus, 
  TaskPriority, 
  CreateTaskRequest, 
  TaskFilters,
  TaskListResponse 
} from '@/types/task';

// Mock database - In production, this would be replaced with actual database calls
let mockTasks: Task[] = [
  {
    id: '1',
    title: 'Complete project documentation',
    description: 'Write comprehensive documentation for the new feature',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.HIGH,
    estimatedHours: 8,
    actualHours: 3.5,
    tags: ['documentation', 'project'],
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-16T14:30:00Z'),
  },
  {
    id: '2',
    title: 'Review code changes',
    description: 'Review pull requests from team members',
    status: TaskStatus.TODO,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 2,
    actualHours: 0,
    tags: ['review', 'code'],
    createdAt: new Date('2024-01-16T09:00:00Z'),
    updatedAt: new Date('2024-01-16T09:00:00Z'),
  },
  {
    id: '3',
    title: 'Update dependencies',
    description: 'Update all npm packages to latest versions',
    status: TaskStatus.DONE,
    priority: TaskPriority.LOW,
    estimatedHours: 1,
    actualHours: 1.2,
    tags: ['maintenance', 'dependencies'],
    createdAt: new Date('2024-01-14T16:00:00Z'),
    updatedAt: new Date('2024-01-15T11:00:00Z'),
    completedAt: new Date('2024-01-15T11:00:00Z'),
  },
];

/**
 * GET /api/v1/tasks
 * List tasks with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: TaskFilters = {
      status: searchParams.get('status')?.split(',') as TaskStatus[] || undefined,
      priority: searchParams.get('priority')?.split(',') as TaskPriority[] || undefined,
      goalId: searchParams.get('goalId') || undefined,
      lifeAreaId: searchParams.get('lifeAreaId') || undefined,
      search: searchParams.get('search') || undefined,
      sortBy: searchParams.get('sortBy') as any || 'createdAt',
      sortOrder: searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc',
      limit: parseInt(searchParams.get('limit') || '20'),
      offset: parseInt(searchParams.get('offset') || '0'),
    };

    // Apply filters
    let filteredTasks = [...mockTasks];

    // Status filter
    if (filters.status?.length) {
      filteredTasks = filteredTasks.filter(task => 
        filters.status!.includes(task.status)
      );
    }

    // Priority filter
    if (filters.priority?.length) {
      filteredTasks = filteredTasks.filter(task => 
        filters.priority!.includes(task.priority)
      );
    }

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredTasks = filteredTasks.filter(task => 
        task.title.toLowerCase().includes(searchLower) ||
        task.description?.toLowerCase().includes(searchLower) ||
        task.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Goal filter
    if (filters.goalId) {
      filteredTasks = filteredTasks.filter(task => task.goalId === filters.goalId);
    }

    // Life area filter
    if (filters.lifeAreaId) {
      filteredTasks = filteredTasks.filter(task => task.lifeAreaId === filters.lifeAreaId);
    }

    // Sorting
    if (filters.sortBy) {
      filteredTasks.sort((a, b) => {
        const aValue = a[filters.sortBy as keyof Task];
        const bValue = b[filters.sortBy as keyof Task];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;
        
        return filters.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    // Pagination
    const total = filteredTasks.length;
    const paginatedTasks = filteredTasks.slice(
      filters.offset || 0, 
      (filters.offset || 0) + (filters.limit || 20)
    );
    const hasMore = (filters.offset || 0) + (filters.limit || 20) < total;

    const response: TaskListResponse = {
      tasks: paginatedTasks,
      total,
      hasMore,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching tasks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/tasks
 * Create a new task
 */
export async function POST(request: NextRequest) {
  try {
    const body: CreateTaskRequest = await request.json();

    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }

    // Create new task
    const newTask: Task = {
      id: (mockTasks.length + 1).toString(),
      title: body.title,
      description: body.description,
      status: TaskStatus.TODO,
      priority: body.priority || TaskPriority.MEDIUM,
      goalId: body.goalId,
      lifeAreaId: body.lifeAreaId,
      parentTaskId: body.parentTaskId,
      estimatedHours: body.estimatedHours || 0,
      actualHours: 0,
      dueDate: body.dueDate,
      tags: body.tags || [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add to mock database
    mockTasks.unshift(newTask);

    return NextResponse.json(newTask, { status: 201 });
  } catch (error) {
    console.error('Error creating task:', error);
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    );
  }
}

/**
 * Validation helper
 */
function validateTaskData(data: Partial<Task>): string[] {
  const errors: string[] = [];

  if (data.title !== undefined && (!data.title || data.title.trim().length === 0)) {
    errors.push('Title cannot be empty');
  }

  if (data.title && data.title.length > 200) {
    errors.push('Title cannot exceed 200 characters');
  }

  if (data.estimatedHours !== undefined && data.estimatedHours < 0) {
    errors.push('Estimated hours cannot be negative');
  }

  if (data.actualHours !== undefined && data.actualHours < 0) {
    errors.push('Actual hours cannot be negative');
  }

  if (data.status && !Object.values(TaskStatus).includes(data.status)) {
    errors.push('Invalid task status');
  }

  if (data.priority && !Object.values(TaskPriority).includes(data.priority)) {
    errors.push('Invalid task priority');
  }

  return errors;
}
