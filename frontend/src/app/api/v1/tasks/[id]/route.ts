/**
 * Individual Task API Routes
 * 
 * RESTful API endpoints for individual task operations:
 * - GET /api/v1/tasks/[id] - Get specific task
 * - PUT /api/v1/tasks/[id] - Update task
 * - DELETE /api/v1/tasks/[id] - Delete task
 */

import { NextRequest, NextResponse } from 'next/server';
import { Task, TaskStatus, UpdateTaskRequest } from '@/types/task';

// Mock database - In production, this would be replaced with actual database calls
// This should be shared with the main tasks route, but for simplicity we'll duplicate
let mockTasks: Task[] = [
  {
    id: '1',
    title: 'Complete project documentation',
    description: 'Write comprehensive documentation for the new feature',
    status: TaskStatus.IN_PROGRESS,
    priority: 'high' as any,
    estimatedHours: 8,
    actualHours: 3.5,
    tags: ['documentation', 'project'],
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-16T14:30:00Z'),
  },
  {
    id: '2',
    title: 'Review code changes',
    description: 'Review pull requests from team members',
    status: TaskStatus.TODO,
    priority: 'medium' as any,
    estimatedHours: 2,
    actualHours: 0,
    tags: ['review', 'code'],
    createdAt: new Date('2024-01-16T09:00:00Z'),
    updatedAt: new Date('2024-01-16T09:00:00Z'),
  },
  {
    id: '3',
    title: 'Update dependencies',
    description: 'Update all npm packages to latest versions',
    status: TaskStatus.DONE,
    priority: 'low' as any,
    estimatedHours: 1,
    actualHours: 1.2,
    tags: ['maintenance', 'dependencies'],
    createdAt: new Date('2024-01-14T16:00:00Z'),
    updatedAt: new Date('2024-01-15T11:00:00Z'),
    completedAt: new Date('2024-01-15T11:00:00Z'),
  },
];

/**
 * GET /api/v1/tasks/[id]
 * Get a specific task by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const task = mockTasks.find(t => t.id === id);

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    return NextResponse.json(
      { error: 'Failed to fetch task' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/v1/tasks/[id]
 * Update a specific task
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body: UpdateTaskRequest = await request.json();

    const taskIndex = mockTasks.findIndex(t => t.id === id);

    if (taskIndex === -1) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // Validate update data
    const validationErrors = validateTaskUpdateData(body);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      );
    }

    // Update task
    const currentTask = mockTasks[taskIndex];
    const updatedTask: Task = {
      ...currentTask,
      ...body,
      id, // Ensure ID doesn't change
      updatedAt: new Date(),
      // Set completedAt when status changes to DONE
      completedAt: body.status === TaskStatus.DONE && currentTask.status !== TaskStatus.DONE
        ? new Date()
        : body.status !== TaskStatus.DONE
        ? undefined
        : currentTask.completedAt,
    };

    mockTasks[taskIndex] = updatedTask;

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error('Error updating task:', error);
    return NextResponse.json(
      { error: 'Failed to update task' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/tasks/[id]
 * Delete a specific task
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const taskIndex = mockTasks.findIndex(t => t.id === id);

    if (taskIndex === -1) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // Remove task from mock database
    const deletedTask = mockTasks.splice(taskIndex, 1)[0];

    return NextResponse.json(
      { message: 'Task deleted successfully', task: deletedTask },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting task:', error);
    return NextResponse.json(
      { error: 'Failed to delete task' },
      { status: 500 }
    );
  }
}

/**
 * Validation helper for task updates
 */
function validateTaskUpdateData(data: UpdateTaskRequest): string[] {
  const errors: string[] = [];

  if (data.title !== undefined) {
    if (!data.title || data.title.trim().length === 0) {
      errors.push('Title cannot be empty');
    }
    if (data.title.length > 200) {
      errors.push('Title cannot exceed 200 characters');
    }
  }

  if (data.description !== undefined && data.description && data.description.length > 2000) {
    errors.push('Description cannot exceed 2000 characters');
  }

  if (data.estimatedHours !== undefined && data.estimatedHours < 0) {
    errors.push('Estimated hours cannot be negative');
  }

  if (data.actualHours !== undefined && data.actualHours < 0) {
    errors.push('Actual hours cannot be negative');
  }

  if (data.status && !Object.values(TaskStatus).includes(data.status)) {
    errors.push('Invalid task status');
  }

  if (data.tags && data.tags.length > 10) {
    errors.push('Cannot have more than 10 tags');
  }

  if (data.tags && data.tags.some(tag => tag.length > 50)) {
    errors.push('Tag length cannot exceed 50 characters');
  }

  return errors;
}
