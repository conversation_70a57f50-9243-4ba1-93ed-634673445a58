import { NextRequest, NextResponse } from 'next/server';

/**
 * Liveness Check API Endpoint
 * 
 * Kubernetes-style liveness probe that indicates if the service
 * is alive and functioning. Used to determine if a container should be restarted.
 */

export async function GET(request: NextRequest) {
  try {
    // Simple liveness check - if we can respond, we're alive
    return NextResponse.json(
      { 
        status: 'alive',
        timestamp: new Date().toISOString(),
        message: '8,760 Hours platform is alive and responding'
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Liveness check failed:', error);
    return NextResponse.json(
      { 
        status: 'dead',
        timestamp: new Date().toISOString(),
        message: 'Liveness check failed'
      },
      { status: 503 }
    );
  }
}
