/**
 * API Endpoint Tests for Task Management
 * 
 * Comprehensive tests for all task-related API endpoints:
 * - GET /api/v1/tasks - List tasks
 * - POST /api/v1/tasks - Create task
 * - GET /api/v1/tasks/[id] - Get specific task
 * - PUT /api/v1/tasks/[id] - Update task
 * - DELETE /api/v1/tasks/[id] - Delete task
 * - GET /api/v1/tasks/analytics - Get analytics
 */

import { TaskStatus, TaskPriority, CreateTaskRequest, UpdateTaskRequest } from '@/types/task';

// Mock Next.js server components
jest.mock('next/server', () => ({
  NextRequest: jest.fn(),
  NextResponse: {
    json: jest.fn((data, init) => ({
      json: jest.fn().mockResolvedValue(data),
      status: init?.status || 200,
    })),
  },
}));

// Mock the API route handlers
const mockGetTasks = jest.fn();
const mockCreateTask = jest.fn();
const mockGetTask = jest.fn();
const mockUpdateTask = jest.fn();
const mockDeleteTask = jest.fn();
const mockGetAnalytics = jest.fn();

jest.mock('@/app/api/v1/tasks/route', () => ({
  GET: mockGetTasks,
  POST: mockCreateTask,
}));

jest.mock('@/app/api/v1/tasks/[id]/route', () => ({
  GET: mockGetTask,
  PUT: mockUpdateTask,
  DELETE: mockDeleteTask,
}));

jest.mock('@/app/api/v1/tasks/analytics/route', () => ({
  GET: mockGetAnalytics,
}));

// Mock NextRequest helper
function createMockRequest(url: string, options: RequestInit = {}): any {
  return {
    url,
    method: options.method || 'GET',
    json: jest.fn().mockResolvedValue(options.body ? JSON.parse(options.body as string) : {}),
    headers: new Map(Object.entries(options.headers || {})),
  };
}

describe('Tasks API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/tasks', () => {
    it('should return list of tasks', async () => {
      const mockResponse = {
        tasks: [
          {
            id: '1',
            title: 'Test Task',
            status: TaskStatus.TODO,
            priority: TaskPriority.MEDIUM,
            estimatedHours: 2,
            actualHours: 0,
            tags: [],
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        total: 1,
        hasMore: false,
      };

      mockGetTasks.mockResolvedValue({
        json: jest.fn().mockResolvedValue(mockResponse),
        status: 200,
      });

      const request = createMockRequest('http://localhost:3000/api/v1/tasks');
      const response = await mockGetTasks(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('tasks');
      expect(data).toHaveProperty('total');
      expect(data).toHaveProperty('hasMore');
      expect(Array.isArray(data.tasks)).toBe(true);
    });

    it('should filter tasks by status', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks?status=todo,in_progress');
      const response = await getTasks(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.tasks.every((task: any) => 
        task.status === TaskStatus.TODO || task.status === TaskStatus.IN_PROGRESS
      )).toBe(true);
    });

    it('should filter tasks by priority', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks?priority=high');
      const response = await getTasks(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.tasks.every((task: any) => task.priority === TaskPriority.HIGH)).toBe(true);
    });

    it('should search tasks by title', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks?search=documentation');
      const response = await getTasks(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.tasks.some((task: any) => 
        task.title.toLowerCase().includes('documentation')
      )).toBe(true);
    });

    it('should apply pagination', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks?limit=1&offset=0');
      const response = await getTasks(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.tasks.length).toBeLessThanOrEqual(1);
    });

    it('should sort tasks', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks?sortBy=title&sortOrder=asc');
      const response = await getTasks(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      if (data.tasks.length > 1) {
        expect(data.tasks[0].title <= data.tasks[1].title).toBe(true);
      }
    });
  });

  describe('POST /api/v1/tasks', () => {
    it('should create a new task', async () => {
      const taskData: CreateTaskRequest = {
        title: 'Test Task',
        description: 'A test task',
        priority: TaskPriority.HIGH,
        estimatedHours: 2,
        tags: ['test'],
      };

      const request = createMockRequest('http://localhost:3000/api/v1/tasks', {
        method: 'POST',
        body: JSON.stringify(taskData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createTask(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toHaveProperty('id');
      expect(data.title).toBe(taskData.title);
      expect(data.description).toBe(taskData.description);
      expect(data.priority).toBe(taskData.priority);
      expect(data.status).toBe(TaskStatus.TODO);
      expect(data.estimatedHours).toBe(taskData.estimatedHours);
      expect(data.actualHours).toBe(0);
      expect(data.tags).toEqual(taskData.tags);
    });

    it('should require title field', async () => {
      const taskData = {
        description: 'A test task without title',
      };

      const request = createMockRequest('http://localhost:3000/api/v1/tasks', {
        method: 'POST',
        body: JSON.stringify(taskData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createTask(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('Title is required');
    });

    it('should set default values', async () => {
      const taskData: CreateTaskRequest = {
        title: 'Minimal Task',
      };

      const request = createMockRequest('http://localhost:3000/api/v1/tasks', {
        method: 'POST',
        body: JSON.stringify(taskData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createTask(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.priority).toBe(TaskPriority.MEDIUM);
      expect(data.status).toBe(TaskStatus.TODO);
      expect(data.estimatedHours).toBe(0);
      expect(data.actualHours).toBe(0);
      expect(data.tags).toEqual([]);
    });
  });

  describe('GET /api/v1/tasks/[id]', () => {
    it('should return specific task', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks/1');
      const response = await getTask(request, { params: { id: '1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('id', '1');
      expect(data).toHaveProperty('title');
      expect(data).toHaveProperty('status');
      expect(data).toHaveProperty('priority');
    });

    it('should return 404 for non-existent task', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks/999');
      const response = await getTask(request, { params: { id: '999' } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('Task not found');
    });
  });

  describe('PUT /api/v1/tasks/[id]', () => {
    it('should update task', async () => {
      const updateData: UpdateTaskRequest = {
        title: 'Updated Task Title',
        status: TaskStatus.IN_PROGRESS,
        priority: TaskPriority.HIGH,
        actualHours: 2.5,
      };

      const request = createMockRequest('http://localhost:3000/api/v1/tasks/1', {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await updateTask(request, { params: { id: '1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe('1');
      expect(data.title).toBe(updateData.title);
      expect(data.status).toBe(updateData.status);
      expect(data.priority).toBe(updateData.priority);
      expect(data.actualHours).toBe(updateData.actualHours);
      expect(new Date(data.updatedAt)).toBeInstanceOf(Date);
    });

    it('should set completedAt when status changes to DONE', async () => {
      const updateData: UpdateTaskRequest = {
        status: TaskStatus.DONE,
      };

      const request = createMockRequest('http://localhost:3000/api/v1/tasks/2', {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await updateTask(request, { params: { id: '2' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.status).toBe(TaskStatus.DONE);
      expect(data.completedAt).toBeDefined();
      expect(new Date(data.completedAt)).toBeInstanceOf(Date);
    });

    it('should validate update data', async () => {
      const updateData = {
        title: '', // Empty title should fail
        estimatedHours: -1, // Negative hours should fail
      };

      const request = createMockRequest('http://localhost:3000/api/v1/tasks/1', {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await updateTask(request, { params: { id: '1' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('Validation failed');
      expect(data).toHaveProperty('details');
      expect(Array.isArray(data.details)).toBe(true);
    });

    it('should return 404 for non-existent task', async () => {
      const updateData: UpdateTaskRequest = {
        title: 'Updated Title',
      };

      const request = createMockRequest('http://localhost:3000/api/v1/tasks/999', {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await updateTask(request, { params: { id: '999' } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('Task not found');
    });
  });

  describe('DELETE /api/v1/tasks/[id]', () => {
    it('should delete task', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks/3');
      const response = await deleteTask(request, { params: { id: '3' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('message');
      expect(data.message).toContain('Task deleted successfully');
      expect(data).toHaveProperty('task');
      expect(data.task.id).toBe('3');
    });

    it('should return 404 for non-existent task', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks/999');
      const response = await deleteTask(request, { params: { id: '999' } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('Task not found');
    });
  });

  describe('GET /api/v1/tasks/analytics', () => {
    it('should return task analytics', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks/analytics');
      const response = await getAnalytics(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('totalTasks');
      expect(data).toHaveProperty('completedTasks');
      expect(data).toHaveProperty('inProgressTasks');
      expect(data).toHaveProperty('overdueTasks');
      expect(data).toHaveProperty('totalTimeSpent');
      expect(data).toHaveProperty('averageCompletionTime');
      expect(data).toHaveProperty('tasksByPriority');
      expect(data).toHaveProperty('tasksByStatus');
      expect(data).toHaveProperty('tasksByLifeArea');
      expect(data).toHaveProperty('productivityTrends');

      // Validate structure
      expect(typeof data.totalTasks).toBe('number');
      expect(typeof data.completedTasks).toBe('number');
      expect(typeof data.totalTimeSpent).toBe('number');
      expect(Array.isArray(data.tasksByLifeArea)).toBe(true);
      expect(Array.isArray(data.productivityTrends)).toBe(true);
    });

    it('should have correct priority distribution', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks/analytics');
      const response = await getAnalytics(request);
      const data = await response.json();

      expect(data.tasksByPriority).toHaveProperty('high');
      expect(data.tasksByPriority).toHaveProperty('medium');
      expect(data.tasksByPriority).toHaveProperty('low');
      expect(typeof data.tasksByPriority.high).toBe('number');
      expect(typeof data.tasksByPriority.medium).toBe('number');
      expect(typeof data.tasksByPriority.low).toBe('number');
    });

    it('should have correct status distribution', async () => {
      const request = createMockRequest('http://localhost:3000/api/v1/tasks/analytics');
      const response = await getAnalytics(request);
      const data = await response.json();

      expect(data.tasksByStatus).toHaveProperty('todo');
      expect(data.tasksByStatus).toHaveProperty('inProgress');
      expect(data.tasksByStatus).toHaveProperty('done');
      expect(data.tasksByStatus).toHaveProperty('blocked');
      expect(data.tasksByStatus).toHaveProperty('deferred');
    });
  });
});
