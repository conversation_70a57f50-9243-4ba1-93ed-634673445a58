/**
 * Unit tests for TaskFilters component.
 * 
 * Tests cover:
 * - Filter rendering and interactions
 * - Status and priority filtering
 * - Date range filtering
 * - Filter application and clearing
 * - Active filter count
 * - Accessibility features
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskFilters } from '@/components/tasks/task-filters';
import { TaskStatus, TaskPriority } from '@/types/task';

const mockOnFiltersChange = jest.fn();
const mockOnClose = jest.fn();

describe('TaskFilters', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders filter panel with all sections', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      expect(screen.getByText('Filters')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.getByText('Priority')).toBeInTheDocument();
      expect(screen.getByText('Due Date')).toBeInTheDocument();
      expect(screen.getByTestId('close-filters-button')).toBeInTheDocument();
    });

    it('shows active filters count badge', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Initially no active filters, so no badge should be visible
      expect(screen.queryByText('1')).not.toBeInTheDocument();
    });

    it('applies custom className', () => {
      const { container } = render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
          className="custom-class"
        />
      );
      
      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('Status Filters', () => {
    it('renders all status options', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      expect(screen.getByText('To Do')).toBeInTheDocument();
      expect(screen.getByText('In Progress')).toBeInTheDocument();
      expect(screen.getByText('Done')).toBeInTheDocument();
      expect(screen.getByText('Blocked')).toBeInTheDocument();
      expect(screen.getByText('Deferred')).toBeInTheDocument();
    });

    it('toggles status filter when checkbox clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      await user.click(todoCheckbox);
      
      expect(todoCheckbox).toBeChecked();
    });

    it('unchecks status filter when clicked again', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      
      // Check then uncheck
      await user.click(todoCheckbox);
      expect(todoCheckbox).toBeChecked();
      
      await user.click(todoCheckbox);
      expect(todoCheckbox).not.toBeChecked();
    });

    it('allows multiple status selections', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      const inProgressCheckbox = screen.getByTestId('status-filter-in_progress');
      
      await user.click(todoCheckbox);
      await user.click(inProgressCheckbox);
      
      expect(todoCheckbox).toBeChecked();
      expect(inProgressCheckbox).toBeChecked();
    });
  });

  describe('Priority Filters', () => {
    it('renders all priority options', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      expect(screen.getByText('High')).toBeInTheDocument();
      expect(screen.getByText('Medium')).toBeInTheDocument();
      expect(screen.getByText('Low')).toBeInTheDocument();
    });

    it('toggles priority filter when checkbox clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const highCheckbox = screen.getByTestId('priority-filter-high');
      await user.click(highCheckbox);
      
      expect(highCheckbox).toBeChecked();
    });

    it('allows multiple priority selections', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const highCheckbox = screen.getByTestId('priority-filter-high');
      const mediumCheckbox = screen.getByTestId('priority-filter-medium');
      
      await user.click(highCheckbox);
      await user.click(mediumCheckbox);
      
      expect(highCheckbox).toBeChecked();
      expect(mediumCheckbox).toBeChecked();
    });
  });

  describe('Date Filters', () => {
    it('renders due date filter options', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      expect(screen.getByText('Due before')).toBeInTheDocument();
      expect(screen.getByText('Due after')).toBeInTheDocument();
      expect(screen.getByTestId('due-before-button')).toBeInTheDocument();
      expect(screen.getByTestId('due-after-button')).toBeInTheDocument();
    });

    it('opens calendar when due before button clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const dueBeforeButton = screen.getByTestId('due-before-button');
      await user.click(dueBeforeButton);
      
      // Calendar should be visible
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });

    it('opens calendar when due after button clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const dueAfterButton = screen.getByTestId('due-after-button');
      await user.click(dueAfterButton);
      
      // Calendar should be visible
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });
  });

  describe('Filter Actions', () => {
    it('applies filters when Apply Filters button clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Select some filters
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      const highCheckbox = screen.getByTestId('priority-filter-high');
      
      await user.click(todoCheckbox);
      await user.click(highCheckbox);
      
      // Apply filters
      const applyButton = screen.getByTestId('apply-filters-button');
      await user.click(applyButton);
      
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        status: [TaskStatus.TODO],
        priority: [TaskPriority.HIGH],
        tags: [],
        dueBefore: undefined,
        dueAfter: undefined,
      });
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('clears all filters when Clear All button clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Select some filters first
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      await user.click(todoCheckbox);
      
      // Clear filters
      const clearButton = screen.getByTestId('clear-filters-button');
      await user.click(clearButton);
      
      expect(todoCheckbox).not.toBeChecked();
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        status: [],
        priority: [],
        tags: [],
        dueBefore: undefined,
        dueAfter: undefined,
      });
    });

    it('disables Clear All button when no filters active', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const clearButton = screen.getByTestId('clear-filters-button');
      expect(clearButton).toBeDisabled();
    });

    it('enables Clear All button when filters are active', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Select a filter
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      await user.click(todoCheckbox);
      
      const clearButton = screen.getByTestId('clear-filters-button');
      expect(clearButton).not.toBeDisabled();
    });

    it('closes filter panel when close button clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      const closeButton = screen.getByTestId('close-filters-button');
      await user.click(closeButton);
      
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Active Filter Count', () => {
    it('shows correct count when filters are active', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Select multiple filter types
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      const highCheckbox = screen.getByTestId('priority-filter-high');
      
      await user.click(todoCheckbox);
      await user.click(highCheckbox);
      
      // Should show count of 2 (status + priority)
      expect(screen.getByText('2')).toBeInTheDocument();
    });

    it('updates count when filters are cleared', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Select a filter
      const todoCheckbox = screen.getByTestId('status-filter-todo');
      await user.click(todoCheckbox);
      
      expect(screen.getByText('1')).toBeInTheDocument();
      
      // Clear filters
      const clearButton = screen.getByTestId('clear-filters-button');
      await user.click(clearButton);
      
      expect(screen.queryByText('1')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper labels for checkboxes', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Check that labels are associated with checkboxes
      expect(screen.getByLabelText('To Do')).toBeInTheDocument();
      expect(screen.getByLabelText('High')).toBeInTheDocument();
      expect(screen.getByLabelText('Medium')).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Tab to first checkbox
      await user.tab();
      expect(screen.getByTestId('status-filter-todo')).toHaveFocus();
      
      // Space to toggle
      await user.keyboard(' ');
      expect(screen.getByTestId('status-filter-todo')).toBeChecked();
    });

    it('has proper ARIA attributes', () => {
      render(
        <TaskFilters
          onFiltersChange={mockOnFiltersChange}
          onClose={mockOnClose}
        />
      );
      
      // Check for proper roles and attributes
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);
      
      checkboxes.forEach(checkbox => {
        expect(checkbox).toHaveAttribute('type', 'checkbox');
      });
    });
  });
});
