/**
 * Unit tests for TaskList component.
 * 
 * Tests cover:
 * - Rendering with different props
 * - Task loading and error states
 * - Search functionality
 * - Filtering and sorting
 * - Task creation flow
 * - Accessibility features
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskList } from '@/components/tasks/task-list';
import { useTaskStore } from '@/stores/task-store';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Mock the task store
jest.mock('@/stores/task-store');
const mockUseTaskStore = useTaskStore as jest.MockedFunction<typeof useTaskStore>;

// Mock child components
jest.mock('@/components/tasks/task-card', () => ({
  TaskCard: ({ task, ...props }: any) => (
    <div data-testid={`task-item-${task.id}`} {...props}>
      <h3>{task.title}</h3>
      <span>{task.status}</span>
    </div>
  ),
}));

jest.mock('@/components/tasks/task-form', () => ({
  TaskForm: ({ onSuccess, onCancel, ...props }: any) => (
    <div data-testid="task-form" {...props}>
      <button onClick={onSuccess}>Save</button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  ),
}));

jest.mock('@/components/tasks/task-filters', () => ({
  TaskFilters: ({ onFiltersChange, onClose }: any) => (
    <div data-testid="task-filters">
      <button onClick={() => onFiltersChange({ status: [TaskStatus.TODO] })}>
        Apply Filter
      </button>
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Test Task 1',
    description: 'First test task',
    status: TaskStatus.TODO,
    priority: TaskPriority.HIGH,
    estimatedHours: 2,
    actualHours: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['test', 'important'],
  },
  {
    id: '2',
    title: 'Test Task 2',
    description: 'Second test task',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    estimatedHours: 4,
    actualHours: 1.5,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    tags: ['test'],
  },
];

const defaultMockStore = {
  tasks: mockTasks,
  currentTask: null,
  isLoading: false,
  error: null,
  filters: {},
  pagination: { page: 1, limit: 20, total: 2, hasMore: false },
  loadTasks: jest.fn(),
  setFilters: jest.fn(),
  getFilteredTasks: jest.fn(() => mockTasks),
  createTask: jest.fn(),
  updateTask: jest.fn(),
  deleteTask: jest.fn(),
  updateTaskStatus: jest.fn(),
  clearFilters: jest.fn(),
  getTaskStatistics: jest.fn(),
  getTaskAnalytics: jest.fn(),
  setTasks: jest.fn(),
  setCurrentTask: jest.fn(),
  setError: jest.fn(),
  reset: jest.fn(),
};

describe('TaskList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseTaskStore.mockReturnValue(defaultMockStore);
  });

  describe('Rendering', () => {
    it('renders task list with default props', () => {
      render(<TaskList />);
      
      expect(screen.getByText('Tasks')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Task count badge
      expect(screen.getByTestId('create-task-button')).toBeInTheDocument();
      expect(screen.getByTestId('task-search-input')).toBeInTheDocument();
    });

    it('renders without create button when showCreateButton is false', () => {
      render(<TaskList showCreateButton={false} />);
      
      expect(screen.queryByTestId('create-task-button')).not.toBeInTheDocument();
    });

    it('renders without search when showSearch is false', () => {
      render(<TaskList showSearch={false} />);
      
      expect(screen.queryByTestId('task-search-input')).not.toBeInTheDocument();
    });

    it('renders without filters when showFilters is false', () => {
      render(<TaskList showFilters={false} />);
      
      expect(screen.queryByTestId('filters-toggle-button')).not.toBeInTheDocument();
    });

    it('applies custom className', () => {
      const { container } = render(<TaskList className="custom-class" />);
      
      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('Task Loading', () => {
    it('shows loading state', () => {
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        isLoading: true,
        tasks: [],
      });

      render(<TaskList />);
      
      expect(screen.getAllByRole('generic')).toHaveLength(3); // Loading skeletons
    });

    it('calls loadTasks on mount', () => {
      const mockLoadTasks = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        loadTasks: mockLoadTasks,
      });

      render(<TaskList />);
      
      expect(mockLoadTasks).toHaveBeenCalledTimes(1);
    });

    it('calls loadTasks with goal filter when goalId provided', () => {
      const mockSetFilters = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        setFilters: mockSetFilters,
      });

      render(<TaskList goalId="goal-123" />);
      
      expect(mockSetFilters).toHaveBeenCalledWith({ goalId: 'goal-123' });
    });

    it('calls loadTasks with life area filter when lifeAreaId provided', () => {
      const mockSetFilters = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        setFilters: mockSetFilters,
      });

      render(<TaskList lifeAreaId="life-area-456" />);
      
      expect(mockSetFilters).toHaveBeenCalledWith({ lifeAreaId: 'life-area-456' });
    });
  });

  describe('Error Handling', () => {
    it('shows error state with retry button', () => {
      const mockLoadTasks = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        error: 'Failed to load tasks',
        loadTasks: mockLoadTasks,
      });

      render(<TaskList />);
      
      expect(screen.getByText('Error loading tasks')).toBeInTheDocument();
      expect(screen.getByText('Failed to load tasks')).toBeInTheDocument();
      
      const retryButton = screen.getByText('Try Again');
      fireEvent.click(retryButton);
      
      expect(mockLoadTasks).toHaveBeenCalledTimes(2); // Once on mount, once on retry
    });
  });

  describe('Task Display', () => {
    it('renders task cards for each task', () => {
      render(<TaskList />);
      
      expect(screen.getByTestId('task-item-1')).toBeInTheDocument();
      expect(screen.getByTestId('task-item-2')).toBeInTheDocument();
      expect(screen.getByText('Test Task 1')).toBeInTheDocument();
      expect(screen.getByText('Test Task 2')).toBeInTheDocument();
    });

    it('shows empty state when no tasks', () => {
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        tasks: [],
        getFilteredTasks: jest.fn(() => []),
      });

      render(<TaskList />);
      
      expect(screen.getByText('No tasks found')).toBeInTheDocument();
      expect(screen.getByText('Create your first task to get started')).toBeInTheDocument();
    });

    it('shows filtered empty state when search/filters applied', () => {
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        tasks: mockTasks,
        getFilteredTasks: jest.fn(() => []),
        filters: { search: 'nonexistent' },
      });

      render(<TaskList />);
      
      expect(screen.getByText('No tasks found')).toBeInTheDocument();
      expect(screen.getByText('Try adjusting your search or filters')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('updates search filter when typing in search input', async () => {
      const user = userEvent.setup();
      const mockSetFilters = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        setFilters: mockSetFilters,
      });

      render(<TaskList />);
      
      const searchInput = screen.getByTestId('task-search-input');
      await user.type(searchInput, 'test query');
      
      await waitFor(() => {
        expect(mockSetFilters).toHaveBeenCalledWith({ search: 'test query' });
      });
    });

    it('clears search filter when input is empty', async () => {
      const user = userEvent.setup();
      const mockSetFilters = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        setFilters: mockSetFilters,
      });

      render(<TaskList />);
      
      const searchInput = screen.getByTestId('task-search-input');
      await user.type(searchInput, 'test');
      await user.clear(searchInput);
      
      await waitFor(() => {
        expect(mockSetFilters).toHaveBeenCalledWith({ search: undefined });
      });
    });
  });

  describe('Sorting', () => {
    it('updates sort filters when sort options change', async () => {
      const user = userEvent.setup();
      const mockSetFilters = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        setFilters: mockSetFilters,
      });

      render(<TaskList />);
      
      // Toggle sort order
      const sortButton = screen.getByTestId('sort-order-button');
      await user.click(sortButton);
      
      expect(mockSetFilters).toHaveBeenCalledWith({ sortBy: 'createdAt', sortOrder: 'asc' });
    });
  });

  describe('Filters', () => {
    it('shows filters panel when filters button clicked', async () => {
      const user = userEvent.setup();
      render(<TaskList />);
      
      const filtersButton = screen.getByTestId('filters-toggle-button');
      await user.click(filtersButton);
      
      expect(screen.getByTestId('task-filters')).toBeInTheDocument();
    });

    it('applies filters when filter component calls onFiltersChange', async () => {
      const user = userEvent.setup();
      const mockSetFilters = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        setFilters: mockSetFilters,
      });

      render(<TaskList />);
      
      // Open filters panel
      const filtersButton = screen.getByTestId('filters-toggle-button');
      await user.click(filtersButton);
      
      // Apply filter
      const applyFilterButton = screen.getByText('Apply Filter');
      await user.click(applyFilterButton);
      
      expect(mockSetFilters).toHaveBeenCalledWith({ status: [TaskStatus.TODO] });
    });
  });

  describe('Task Creation', () => {
    it('opens create form when create button clicked', async () => {
      const user = userEvent.setup();
      render(<TaskList />);
      
      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);
      
      expect(screen.getByTestId('task-form')).toBeInTheDocument();
    });

    it('closes create form and refreshes tasks on success', async () => {
      const user = userEvent.setup();
      const mockLoadTasks = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        loadTasks: mockLoadTasks,
      });

      render(<TaskList />);
      
      // Open create form
      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);
      
      // Save task
      const saveButton = screen.getByText('Save');
      await user.click(saveButton);
      
      expect(screen.queryByTestId('task-form')).not.toBeInTheDocument();
      expect(mockLoadTasks).toHaveBeenCalledTimes(2); // Once on mount, once after save
    });

    it('closes create form on cancel', async () => {
      const user = userEvent.setup();
      render(<TaskList />);
      
      // Open create form
      const createButton = screen.getByTestId('create-task-button');
      await user.click(createButton);
      
      // Cancel
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);
      
      expect(screen.queryByTestId('task-form')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<TaskList />);
      
      expect(screen.getByTestId('task-search-input')).toHaveAttribute('placeholder', 'Search tasks...');
      expect(screen.getByTestId('create-task-button')).toBeInTheDocument();
      expect(screen.getByTestId('task-list')).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<TaskList />);
      
      // Tab to search input
      await user.tab();
      expect(screen.getByTestId('task-search-input')).toHaveFocus();
      
      // Tab through elements to create button
      for (let i = 0; i < 4; i++) {
        await user.tab();
      }
      expect(screen.getByTestId('create-task-button')).toHaveFocus();
    });
  });
});
