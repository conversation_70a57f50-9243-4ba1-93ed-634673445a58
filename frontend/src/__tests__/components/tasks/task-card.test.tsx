/**
 * Unit tests for TaskCard component.
 * 
 * Tests cover:
 * - Task display with different statuses and priorities
 * - Status toggle functionality
 * - Action buttons (edit, delete, timer)
 * - Progress display
 * - Overdue task indication
 * - Compact mode
 * - Accessibility features
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskCard } from '@/components/tasks/task-card';
import { useTaskStore } from '@/stores/task-store';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Mock the task store
jest.mock('@/stores/task-store');
const mockUseTaskStore = useTaskStore as jest.MockedFunction<typeof useTaskStore>;

// Mock child components
jest.mock('@/components/tasks/task-form', () => ({
  TaskForm: ({ task, onSuccess, onCancel }: any) => (
    <div data-testid="task-form">
      <span>Editing: {task?.title}</span>
      <button onClick={onSuccess}>Save</button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  ),
}));

jest.mock('@/components/tasks/task-timer', () => ({
  TaskTimer: ({ task, onClose }: any) => (
    <div data-testid="task-timer">
      <span>Timer for: {task.title}</span>
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

// Mock window.confirm
const mockConfirm = jest.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true,
});

const mockTask: Task = {
  id: 'task-1',
  title: 'Test Task',
  description: 'This is a test task description',
  status: TaskStatus.TODO,
  priority: TaskPriority.HIGH,
  estimatedHours: 4,
  actualHours: 1.5,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  tags: ['test', 'important'],
  goal: {
    id: 'goal-1',
    title: 'Test Goal',
    lifeAreaId: 'life-area-1',
  },
  lifeArea: {
    id: 'life-area-1',
    name: 'Career',
  },
  subtasks: [
    {
      id: 'subtask-1',
      title: 'Subtask 1',
      status: TaskStatus.DONE,
      priority: TaskPriority.LOW,
      estimatedHours: 1,
      actualHours: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      tags: [],
    },
    {
      id: 'subtask-2',
      title: 'Subtask 2',
      status: TaskStatus.TODO,
      priority: TaskPriority.LOW,
      estimatedHours: 1,
      actualHours: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      tags: [],
    },
  ],
};

const defaultMockStore = {
  updateTaskStatus: jest.fn(),
  deleteTask: jest.fn(),
  tasks: [],
  currentTask: null,
  isLoading: false,
  error: null,
  filters: {},
  pagination: { page: 1, limit: 20, total: 0, hasMore: false },
  loadTasks: jest.fn(),
  setFilters: jest.fn(),
  getFilteredTasks: jest.fn(),
  createTask: jest.fn(),
  updateTask: jest.fn(),
  clearFilters: jest.fn(),
  getTaskStatistics: jest.fn(),
  getTaskAnalytics: jest.fn(),
  setTasks: jest.fn(),
  setCurrentTask: jest.fn(),
  setError: jest.fn(),
  reset: jest.fn(),
};

describe('TaskCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseTaskStore.mockReturnValue(defaultMockStore);
    mockConfirm.mockReturnValue(true);
  });

  describe('Task Display', () => {
    it('renders task information correctly', () => {
      render(<TaskCard task={mockTask} />);
      
      expect(screen.getByText('Test Task')).toBeInTheDocument();
      expect(screen.getByText('This is a test task description')).toBeInTheDocument();
      expect(screen.getByText('To Do')).toBeInTheDocument();
      expect(screen.getByText('High')).toBeInTheDocument();
      expect(screen.getByText('1.5h / 4h')).toBeInTheDocument();
      expect(screen.getByText('test, important')).toBeInTheDocument();
    });

    it('renders task with different status', () => {
      const completedTask = { ...mockTask, status: TaskStatus.DONE };
      render(<TaskCard task={completedTask} />);
      
      expect(screen.getByText('Done')).toBeInTheDocument();
      // Task title should have line-through style when completed
      const title = screen.getByText('Test Task');
      expect(title).toHaveClass('line-through');
    });

    it('renders task with different priority', () => {
      const lowPriorityTask = { ...mockTask, priority: TaskPriority.LOW };
      render(<TaskCard task={lowPriorityTask} />);
      
      expect(screen.getByText('Low')).toBeInTheDocument();
    });

    it('renders task without description', () => {
      const taskWithoutDescription = { ...mockTask, description: undefined };
      render(<TaskCard task={taskWithoutDescription} />);
      
      expect(screen.getByText('Test Task')).toBeInTheDocument();
      expect(screen.queryByText('This is a test task description')).not.toBeInTheDocument();
    });

    it('renders task with due date', () => {
      const taskWithDueDate = { 
        ...mockTask, 
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
      };
      render(<TaskCard task={taskWithDueDate} />);
      
      expect(screen.getByText(/Due in/)).toBeInTheDocument();
    });

    it('shows overdue indication for past due tasks', () => {
      const overdueTask = { 
        ...mockTask, 
        dueDate: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
      };
      render(<TaskCard task={overdueTask} />);
      
      expect(screen.getByText(/Overdue/)).toBeInTheDocument();
    });
  });

  describe('Compact Mode', () => {
    it('hides description and progress in compact mode', () => {
      render(<TaskCard task={mockTask} compact />);
      
      expect(screen.getByText('Test Task')).toBeInTheDocument();
      expect(screen.queryByText('This is a test task description')).not.toBeInTheDocument();
      // Progress bar should not be visible in compact mode
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    it('shows essential information in compact mode', () => {
      render(<TaskCard task={mockTask} compact />);
      
      expect(screen.getByText('Test Task')).toBeInTheDocument();
      expect(screen.getByText('To Do')).toBeInTheDocument();
      expect(screen.getByText('High')).toBeInTheDocument();
      expect(screen.getByText('1.5h / 4h')).toBeInTheDocument();
    });
  });

  describe('Status Toggle', () => {
    it('toggles task status from TODO to DONE', async () => {
      const user = userEvent.setup();
      const mockUpdateTaskStatus = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskCard task={mockTask} />);
      
      const statusButton = screen.getByTestId('task-status-toggle');
      await user.click(statusButton);
      
      expect(mockUpdateTaskStatus).toHaveBeenCalledWith('task-1', TaskStatus.DONE);
    });

    it('toggles task status from DONE to TODO', async () => {
      const user = userEvent.setup();
      const mockUpdateTaskStatus = jest.fn();
      const completedTask = { ...mockTask, status: TaskStatus.DONE };
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskCard task={completedTask} />);
      
      const statusButton = screen.getByTestId('task-status-toggle');
      await user.click(statusButton);
      
      expect(mockUpdateTaskStatus).toHaveBeenCalledWith('task-1', TaskStatus.TODO);
    });

    it('disables status toggle when updating', async () => {
      const user = userEvent.setup();
      const mockUpdateTaskStatus = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskCard task={mockTask} />);
      
      const statusButton = screen.getByTestId('task-status-toggle');
      await user.click(statusButton);
      
      expect(statusButton).toBeDisabled();
    });
  });

  describe('Action Buttons', () => {
    it('shows all action buttons by default', () => {
      render(<TaskCard task={mockTask} />);
      
      expect(screen.getByTestId('task-timer-button')).toBeInTheDocument();
      expect(screen.getByTestId('task-edit-button')).toBeInTheDocument();
      expect(screen.getByTestId('task-delete-button')).toBeInTheDocument();
    });

    it('hides action buttons when showActions is false', () => {
      render(<TaskCard task={mockTask} showActions={false} />);
      
      expect(screen.queryByTestId('task-timer-button')).not.toBeInTheDocument();
      expect(screen.queryByTestId('task-edit-button')).not.toBeInTheDocument();
      expect(screen.queryByTestId('task-delete-button')).not.toBeInTheDocument();
    });

    it('hides timer button when showTimer is false', () => {
      render(<TaskCard task={mockTask} showTimer={false} />);
      
      expect(screen.queryByTestId('task-timer-button')).not.toBeInTheDocument();
      expect(screen.getByTestId('task-edit-button')).toBeInTheDocument();
      expect(screen.getByTestId('task-delete-button')).toBeInTheDocument();
    });

    it('hides timer button for completed tasks', () => {
      const completedTask = { ...mockTask, status: TaskStatus.DONE };
      render(<TaskCard task={completedTask} />);
      
      expect(screen.queryByTestId('task-timer-button')).not.toBeInTheDocument();
    });
  });

  describe('Edit Functionality', () => {
    it('opens edit form when edit button clicked', async () => {
      const user = userEvent.setup();
      render(<TaskCard task={mockTask} />);
      
      const editButton = screen.getByTestId('task-edit-button');
      await user.click(editButton);
      
      expect(screen.getByTestId('task-form')).toBeInTheDocument();
      expect(screen.getByText('Editing: Test Task')).toBeInTheDocument();
    });

    it('closes edit form on success', async () => {
      const user = userEvent.setup();
      render(<TaskCard task={mockTask} />);
      
      // Open edit form
      const editButton = screen.getByTestId('task-edit-button');
      await user.click(editButton);
      
      // Save changes
      const saveButton = screen.getByText('Save');
      await user.click(saveButton);
      
      expect(screen.queryByTestId('task-form')).not.toBeInTheDocument();
    });

    it('closes edit form on cancel', async () => {
      const user = userEvent.setup();
      render(<TaskCard task={mockTask} />);
      
      // Open edit form
      const editButton = screen.getByTestId('task-edit-button');
      await user.click(editButton);
      
      // Cancel
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);
      
      expect(screen.queryByTestId('task-form')).not.toBeInTheDocument();
    });
  });

  describe('Delete Functionality', () => {
    it('deletes task when confirmed', async () => {
      const user = userEvent.setup();
      const mockDeleteTask = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        deleteTask: mockDeleteTask,
      });
      mockConfirm.mockReturnValue(true);

      render(<TaskCard task={mockTask} />);
      
      const deleteButton = screen.getByTestId('task-delete-button');
      await user.click(deleteButton);
      
      expect(mockConfirm).toHaveBeenCalledWith('Are you sure you want to delete this task?');
      expect(mockDeleteTask).toHaveBeenCalledWith('task-1');
    });

    it('does not delete task when not confirmed', async () => {
      const user = userEvent.setup();
      const mockDeleteTask = jest.fn();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        deleteTask: mockDeleteTask,
      });
      mockConfirm.mockReturnValue(false);

      render(<TaskCard task={mockTask} />);
      
      const deleteButton = screen.getByTestId('task-delete-button');
      await user.click(deleteButton);
      
      expect(mockConfirm).toHaveBeenCalled();
      expect(mockDeleteTask).not.toHaveBeenCalled();
    });
  });

  describe('Timer Functionality', () => {
    it('opens timer modal when timer button clicked', async () => {
      const user = userEvent.setup();
      render(<TaskCard task={mockTask} />);
      
      const timerButton = screen.getByTestId('task-timer-button');
      await user.click(timerButton);
      
      expect(screen.getByTestId('task-timer')).toBeInTheDocument();
      expect(screen.getByText('Timer for: Test Task')).toBeInTheDocument();
    });

    it('closes timer modal', async () => {
      const user = userEvent.setup();
      render(<TaskCard task={mockTask} />);
      
      // Open timer modal
      const timerButton = screen.getByTestId('task-timer-button');
      await user.click(timerButton);
      
      // Close timer
      const closeButton = screen.getByText('Close');
      await user.click(closeButton);
      
      expect(screen.queryByTestId('task-timer')).not.toBeInTheDocument();
    });
  });

  describe('Progress Display', () => {
    it('shows progress bar when estimated hours > 0', () => {
      render(<TaskCard task={mockTask} />);
      
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('hides progress bar when estimated hours = 0', () => {
      const taskWithoutEstimate = { ...mockTask, estimatedHours: 0 };
      render(<TaskCard task={taskWithoutEstimate} />);
      
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    it('calculates progress percentage correctly', () => {
      // 1.5h actual / 4h estimated = 37.5%
      render(<TaskCard task={mockTask} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '37.5');
    });
  });

  describe('Additional Information', () => {
    it('shows goal and life area information', () => {
      render(<TaskCard task={mockTask} />);
      
      expect(screen.getByText('Goal: Test Goal')).toBeInTheDocument();
      expect(screen.getByText('Life Area: Career')).toBeInTheDocument();
    });

    it('shows subtask completion count', () => {
      render(<TaskCard task={mockTask} />);
      
      expect(screen.getByText('Subtasks: 1 / 2 completed')).toBeInTheDocument();
    });

    it('hides additional info in compact mode', () => {
      render(<TaskCard task={mockTask} compact />);
      
      expect(screen.queryByText('Goal: Test Goal')).not.toBeInTheDocument();
      expect(screen.queryByText('Life Area: Career')).not.toBeInTheDocument();
      expect(screen.queryByText('Subtasks: 1 / 2 completed')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<TaskCard task={mockTask} />);
      
      expect(screen.getByTestId('task-status-toggle')).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<TaskCard task={mockTask} />);
      
      // Tab to status toggle
      await user.tab();
      expect(screen.getByTestId('task-status-toggle')).toHaveFocus();
      
      // Tab to timer button
      await user.tab();
      expect(screen.getByTestId('task-timer-button')).toHaveFocus();
    });
  });
});
