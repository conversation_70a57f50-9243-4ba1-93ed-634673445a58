/**
 * Unit tests for TaskForm component.
 * 
 * Tests cover:
 * - Form rendering in create and edit modes
 * - Form validation
 * - Field interactions
 * - Tag management
 * - Date selection
 * - Form submission
 * - Error handling
 * - Accessibility features
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskForm } from '@/components/tasks/task-form';
import { useTaskStore } from '@/stores/task-store';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Mock the task store
jest.mock('@/stores/task-store');
const mockUseTaskStore = useTaskStore as jest.MockedFunction<typeof useTaskStore>;

const mockTask: Task = {
  id: 'task-1',
  title: 'Existing Task',
  description: 'Existing task description',
  status: TaskStatus.IN_PROGRESS,
  priority: TaskPriority.HIGH,
  estimatedHours: 3,
  actualHours: 1,
  dueDate: new Date('2024-12-31'),
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  tags: ['existing', 'test'],
  goalId: 'goal-1',
  lifeAreaId: 'life-area-1',
};

const defaultMockStore = {
  createTask: jest.fn(),
  updateTask: jest.fn(),
  isLoading: false,
  tasks: [],
  currentTask: null,
  error: null,
  filters: {},
  pagination: { page: 1, limit: 20, total: 0, hasMore: false },
  loadTasks: jest.fn(),
  setFilters: jest.fn(),
  getFilteredTasks: jest.fn(),
  deleteTask: jest.fn(),
  updateTaskStatus: jest.fn(),
  clearFilters: jest.fn(),
  getTaskStatistics: jest.fn(),
  getTaskAnalytics: jest.fn(),
  setTasks: jest.fn(),
  setCurrentTask: jest.fn(),
  setError: jest.fn(),
  reset: jest.fn(),
};

const mockOnSuccess = jest.fn();
const mockOnCancel = jest.fn();

describe('TaskForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseTaskStore.mockReturnValue(defaultMockStore);
  });

  describe('Create Mode', () => {
    it('renders create form with default values', () => {
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByText('Create New Task')).toBeInTheDocument();
      expect(screen.getByTestId('task-title-input')).toHaveValue('');
      expect(screen.getByTestId('task-description-input')).toHaveValue('');
      expect(screen.getByTestId('task-estimated-hours-input')).toHaveValue(0);
      expect(screen.getByText('Create Task')).toBeInTheDocument();
    });

    it('pre-fills goalId when provided', () => {
      render(
        <TaskForm
          goalId="goal-123"
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      // Form should be rendered (goalId is internal state)
      expect(screen.getByText('Create New Task')).toBeInTheDocument();
    });

    it('pre-fills lifeAreaId when provided', () => {
      render(
        <TaskForm
          lifeAreaId="life-area-456"
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByText('Create New Task')).toBeInTheDocument();
    });

    it('pre-fills parentTaskId when provided', () => {
      render(
        <TaskForm
          parentTaskId="parent-789"
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByText('Create New Task')).toBeInTheDocument();
    });
  });

  describe('Edit Mode', () => {
    it('renders edit form with existing task data', () => {
      render(
        <TaskForm
          task={mockTask}
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByText('Edit Task')).toBeInTheDocument();
      expect(screen.getByTestId('task-title-input')).toHaveValue('Existing Task');
      expect(screen.getByTestId('task-description-input')).toHaveValue('Existing task description');
      expect(screen.getByTestId('task-estimated-hours-input')).toHaveValue(3);
      expect(screen.getByText('Update Task')).toBeInTheDocument();
    });

    it('shows existing tags', () => {
      render(
        <TaskForm
          task={mockTask}
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByText('existing')).toBeInTheDocument();
      expect(screen.getByText('test')).toBeInTheDocument();
    });

    it('shows existing due date', () => {
      render(
        <TaskForm
          task={mockTask}
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByText('December 31, 2024')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('shows error when title is empty', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const saveButton = screen.getByTestId('save-task-button');
      await user.click(saveButton);
      
      expect(screen.getByText('Title is required')).toBeInTheDocument();
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('shows error when estimated hours is negative', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const titleInput = screen.getByTestId('task-title-input');
      const hoursInput = screen.getByTestId('task-estimated-hours-input');
      const saveButton = screen.getByTestId('save-task-button');
      
      await user.type(titleInput, 'Valid Title');
      await user.clear(hoursInput);
      await user.type(hoursInput, '-1');
      await user.click(saveButton);
      
      expect(screen.getByText('Estimated hours cannot be negative')).toBeInTheDocument();
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('passes validation with valid data', async () => {
      const user = userEvent.setup();
      const mockCreateTask = jest.fn().mockResolvedValue({ id: 'new-task' });
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        createTask: mockCreateTask,
      });

      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const titleInput = screen.getByTestId('task-title-input');
      const saveButton = screen.getByTestId('save-task-button');
      
      await user.type(titleInput, 'Valid Task Title');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(mockCreateTask).toHaveBeenCalledWith({
          title: 'Valid Task Title',
          description: undefined,
          status: TaskStatus.TODO,
          priority: TaskPriority.MEDIUM,
          estimatedHours: 0,
          dueDate: undefined,
          tags: [],
          goalId: undefined,
          lifeAreaId: undefined,
          parentTaskId: undefined,
        });
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });
  });

  describe('Field Interactions', () => {
    it('updates title field', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const titleInput = screen.getByTestId('task-title-input');
      await user.type(titleInput, 'New Task Title');
      
      expect(titleInput).toHaveValue('New Task Title');
    });

    it('updates description field', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const descriptionInput = screen.getByTestId('task-description-input');
      await user.type(descriptionInput, 'Task description');
      
      expect(descriptionInput).toHaveValue('Task description');
    });

    it('updates estimated hours field', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const hoursInput = screen.getByTestId('task-estimated-hours-input');
      await user.clear(hoursInput);
      await user.type(hoursInput, '2.5');
      
      expect(hoursInput).toHaveValue(2.5);
    });

    it('updates status field', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const statusSelect = screen.getByTestId('task-status-select');
      await user.click(statusSelect);
      
      const inProgressOption = screen.getByText('In Progress');
      await user.click(inProgressOption);
      
      // Status should be updated (internal state)
      expect(screen.getByText('In Progress')).toBeInTheDocument();
    });

    it('updates priority field', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const prioritySelect = screen.getByTestId('task-priority-select');
      await user.click(prioritySelect);
      
      const highOption = screen.getByText('High');
      await user.click(highOption);
      
      expect(screen.getByText('High')).toBeInTheDocument();
    });
  });

  describe('Tag Management', () => {
    it('adds new tag', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const tagInput = screen.getByTestId('task-tag-input');
      const addButton = screen.getByTestId('add-tag-button');
      
      await user.type(tagInput, 'newtag');
      await user.click(addButton);
      
      expect(screen.getByText('newtag')).toBeInTheDocument();
      expect(tagInput).toHaveValue('');
    });

    it('adds tag on Enter key press', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const tagInput = screen.getByTestId('task-tag-input');
      
      await user.type(tagInput, 'entertag');
      await user.keyboard('{Enter}');
      
      expect(screen.getByText('entertag')).toBeInTheDocument();
      expect(tagInput).toHaveValue('');
    });

    it('removes existing tag', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          task={mockTask}
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByText('existing')).toBeInTheDocument();
      
      // Find and click the X button for the 'existing' tag
      const existingTag = screen.getByText('existing').closest('div');
      const removeButton = existingTag?.querySelector('button');
      
      if (removeButton) {
        await user.click(removeButton);
      }
      
      expect(screen.queryByText('existing')).not.toBeInTheDocument();
    });

    it('prevents duplicate tags', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          task={mockTask}
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const tagInput = screen.getByTestId('task-tag-input');
      const addButton = screen.getByTestId('add-tag-button');
      
      await user.type(tagInput, 'existing');
      await user.click(addButton);
      
      // Should still only have one 'existing' tag
      const existingTags = screen.getAllByText('existing');
      expect(existingTags).toHaveLength(1);
    });

    it('disables add button when tag input is empty', () => {
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const addButton = screen.getByTestId('add-tag-button');
      expect(addButton).toBeDisabled();
    });
  });

  describe('Date Selection', () => {
    it('opens calendar when due date button clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const dueDateButton = screen.getByTestId('task-due-date-button');
      await user.click(dueDateButton);
      
      // Calendar should be visible (we can check for calendar-specific elements)
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });
  });

  describe('Form Submission', () => {
    it('creates new task successfully', async () => {
      const user = userEvent.setup();
      const mockCreateTask = jest.fn().mockResolvedValue({ id: 'new-task' });
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        createTask: mockCreateTask,
      });

      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const titleInput = screen.getByTestId('task-title-input');
      const descriptionInput = screen.getByTestId('task-description-input');
      const saveButton = screen.getByTestId('save-task-button');
      
      await user.type(titleInput, 'New Task');
      await user.type(descriptionInput, 'Task description');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(mockCreateTask).toHaveBeenCalledWith({
          title: 'New Task',
          description: 'Task description',
          status: TaskStatus.TODO,
          priority: TaskPriority.MEDIUM,
          estimatedHours: 0,
          dueDate: undefined,
          tags: [],
          goalId: undefined,
          lifeAreaId: undefined,
          parentTaskId: undefined,
        });
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('updates existing task successfully', async () => {
      const user = userEvent.setup();
      const mockUpdateTask = jest.fn().mockResolvedValue(mockTask);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTask: mockUpdateTask,
      });

      render(
        <TaskForm
          task={mockTask}
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const titleInput = screen.getByTestId('task-title-input');
      const saveButton = screen.getByTestId('save-task-button');
      
      await user.clear(titleInput);
      await user.type(titleInput, 'Updated Task');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(mockUpdateTask).toHaveBeenCalledWith('task-1', expect.objectContaining({
          title: 'Updated Task',
        }));
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('handles submission error', async () => {
      const user = userEvent.setup();
      const mockCreateTask = jest.fn().mockRejectedValue(new Error('API Error'));
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        createTask: mockCreateTask,
      });

      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const titleInput = screen.getByTestId('task-title-input');
      const saveButton = screen.getByTestId('save-task-button');
      
      await user.type(titleInput, 'New Task');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to save task. Please try again.')).toBeInTheDocument();
        expect(mockOnSuccess).not.toHaveBeenCalled();
      });
    });

    it('shows loading state during submission', async () => {
      const user = userEvent.setup();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        isLoading: true,
      });

      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const saveButton = screen.getByTestId('save-task-button');
      expect(saveButton).toHaveTextContent('Saving...');
      expect(saveButton).toBeDisabled();
    });
  });

  describe('Cancel Functionality', () => {
    it('calls onCancel when cancel button clicked', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('calls onCancel when dialog is closed', () => {
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      // Dialog should call onCancel when closed
      // This is handled by the Dialog component's onOpenChange
      expect(mockOnCancel).toBeDefined();
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels', () => {
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      expect(screen.getByLabelText('Title *')).toBeInTheDocument();
      expect(screen.getByLabelText('Description')).toBeInTheDocument();
      expect(screen.getByLabelText('Status')).toBeInTheDocument();
      expect(screen.getByLabelText('Priority')).toBeInTheDocument();
      expect(screen.getByLabelText('Estimated Hours')).toBeInTheDocument();
      expect(screen.getByLabelText('Tags')).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(
        <TaskForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );
      
      // Tab through form elements
      await user.tab();
      expect(screen.getByTestId('task-title-input')).toHaveFocus();

      await user.tab();
      expect(screen.getByTestId('task-description-input')).toHaveFocus();
    });
  });
});
