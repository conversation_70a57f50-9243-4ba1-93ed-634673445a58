/**
 * Unit tests for TaskTimer component.
 * 
 * Tests cover:
 * - Timer display and controls
 * - Start, pause, and stop functionality
 * - Time tracking and progress calculation
 * - Notes input
 * - Task status updates
 * - Accessibility features
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskTimer } from '@/components/tasks/task-timer';
import { useTaskStore } from '@/stores/task-store';
import { Task, TaskStatus, TaskPriority } from '@/types/task';

// Mock the task store
jest.mock('@/stores/task-store');
const mockUseTaskStore = useTaskStore as jest.MockedFunction<typeof useTaskStore>;

// Mock timers
jest.useFakeTimers();

const mockTask: Task = {
  id: 'task-1',
  title: 'Test Task',
  description: 'Test task description',
  status: TaskStatus.TODO,
  priority: TaskPriority.MEDIUM,
  estimatedHours: 4,
  actualHours: 1.5,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  tags: ['test'],
};

const defaultMockStore = {
  updateTaskStatus: jest.fn(),
  tasks: [],
  currentTask: null,
  isLoading: false,
  error: null,
  filters: {},
  pagination: { page: 1, limit: 20, total: 0, hasMore: false },
  loadTasks: jest.fn(),
  setFilters: jest.fn(),
  getFilteredTasks: jest.fn(),
  createTask: jest.fn(),
  updateTask: jest.fn(),
  deleteTask: jest.fn(),
  clearFilters: jest.fn(),
  getTaskStatistics: jest.fn(),
  getTaskAnalytics: jest.fn(),
  setTasks: jest.fn(),
  setCurrentTask: jest.fn(),
  setError: jest.fn(),
  reset: jest.fn(),
};

const mockOnClose = jest.fn();

describe('TaskTimer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseTaskStore.mockReturnValue(defaultMockStore);
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  describe('Rendering', () => {
    it('renders timer modal with task information', () => {
      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      expect(screen.getByText('Task Timer')).toBeInTheDocument();
      expect(screen.getByText('Test Task')).toBeInTheDocument();
      expect(screen.getByText('Test task description')).toBeInTheDocument();
      expect(screen.getByText('00:00:00')).toBeInTheDocument();
      expect(screen.getByTestId('start-timer-button')).toBeInTheDocument();
    });

    it('shows task progress information', () => {
      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      expect(screen.getByText('1.5h')).toBeInTheDocument(); // Time logged today
      expect(screen.getByText('4h')).toBeInTheDocument(); // Estimated hours
      expect(screen.getByText('38%')).toBeInTheDocument(); // Progress percentage
    });

    it('hides progress info when no estimated hours', () => {
      const taskWithoutEstimate = { ...mockTask, estimatedHours: 0 };
      render(<TaskTimer task={taskWithoutEstimate} onClose={mockOnClose} />);
      
      expect(screen.queryByText('Estimated:')).not.toBeInTheDocument();
      expect(screen.queryByText('Progress:')).not.toBeInTheDocument();
    });

    it('shows timer status indicator', () => {
      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      expect(screen.getByText('Timer stopped')).toBeInTheDocument();
    });
  });

  describe('Timer Controls', () => {
    it('starts timer when start button clicked', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(mockUpdateTaskStatus).toHaveBeenCalledWith('task-1', TaskStatus.IN_PROGRESS);
        expect(screen.getByTestId('pause-timer-button')).toBeInTheDocument();
        expect(screen.getByTestId('stop-timer-button')).toBeInTheDocument();
        expect(screen.getByText('Timer running')).toBeInTheDocument();
      });
    });

    it('does not update status if task is already in progress', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn();
      const inProgressTask = { ...mockTask, status: TaskStatus.IN_PROGRESS };
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={inProgressTask} onClose={mockOnClose} />);
      
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(mockUpdateTaskStatus).not.toHaveBeenCalled();
      });
    });

    it('pauses timer when pause button clicked', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('pause-timer-button')).toBeInTheDocument();
      });
      
      // Pause timer
      const pauseButton = screen.getByTestId('pause-timer-button');
      await user.click(pauseButton);
      
      expect(screen.getByTestId('start-timer-button')).toBeInTheDocument();
      expect(screen.getByText('Timer stopped')).toBeInTheDocument();
    });

    it('stops timer and closes modal when stop button clicked', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('stop-timer-button')).toBeInTheDocument();
      });
      
      // Stop timer
      const stopButton = screen.getByTestId('stop-timer-button');
      await user.click(stopButton);
      
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled();
      });
    });
  });

  describe('Time Tracking', () => {
    it('updates elapsed time when timer is running', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('pause-timer-button')).toBeInTheDocument();
      });
      
      // Advance time by 5 seconds
      act(() => {
        jest.advanceTimersByTime(5000);
      });
      
      expect(screen.getByText('00:00:05')).toBeInTheDocument();
    });

    it('formats time correctly', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('pause-timer-button')).toBeInTheDocument();
      });
      
      // Advance time by 1 hour, 2 minutes, 3 seconds
      act(() => {
        jest.advanceTimersByTime(3723000);
      });
      
      expect(screen.getByText('01:02:03')).toBeInTheDocument();
    });

    it('updates progress with current session time', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('pause-timer-button')).toBeInTheDocument();
      });
      
      // Advance time by 30 minutes (0.5 hours)
      act(() => {
        jest.advanceTimersByTime(1800000);
      });
      
      // Progress should update: (1.5 + 0.5) / 4 = 50%
      expect(screen.getByText('2.0h')).toBeInTheDocument(); // Time logged today
      expect(screen.getByText('50%')).toBeInTheDocument(); // Progress percentage
    });
  });

  describe('Notes Input', () => {
    it('allows entering session notes', async () => {
      const user = userEvent.setup();
      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      const notesInput = screen.getByTestId('timer-notes-input');
      await user.type(notesInput, 'Worked on feature implementation');
      
      expect(notesInput).toHaveValue('Worked on feature implementation');
    });

    it('includes notes in time entry when stopping timer', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      // Mock console.log to capture the time entry data
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Add notes
      const notesInput = screen.getByTestId('timer-notes-input');
      await user.type(notesInput, 'Test notes');
      
      // Start and stop timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('stop-timer-button')).toBeInTheDocument();
      });
      
      const stopButton = screen.getByTestId('stop-timer-button');
      await user.click(stopButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          'Time entry saved:',
          expect.objectContaining({
            taskId: 'task-1',
            notes: 'Test notes',
          })
        );
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Loading States', () => {
    it('disables buttons when loading', async () => {
      const user = userEvent.setup();
      const mockUpdateTaskStatus = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      expect(startButton).toBeDisabled();
    });

    it('prevents closing when timer is running', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText('Stop timer first')).toBeInTheDocument();
      });
      
      const closeButton = screen.getByText('Stop timer first');
      expect(closeButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('handles start timer error gracefully', async () => {
      const user = userEvent.setup();
      const mockUpdateTaskStatus = jest.fn().mockRejectedValue(new Error('API Error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Failed to start timer:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it('handles stop timer error gracefully', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('stop-timer-button')).toBeInTheDocument();
      });
      
      // Mock Promise.resolve to reject for the stop action
      const originalPromiseResolve = Promise.resolve;
      Promise.resolve = jest.fn().mockRejectedValue(new Error('Save Error'));
      
      const stopButton = screen.getByTestId('stop-timer-button');
      await user.click(stopButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Failed to save time entry:', expect.any(Error));
      });

      Promise.resolve = originalPromiseResolve;
      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByLabelText('Session Notes')).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      // Tab to start button
      await user.tab();
      expect(screen.getByTestId('start-timer-button')).toHaveFocus();
      
      // Tab to notes input
      await user.tab();
      expect(screen.getByTestId('timer-notes-input')).toHaveFocus();
    });

    it('announces timer state changes', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const mockUpdateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockUseTaskStore.mockReturnValue({
        ...defaultMockStore,
        updateTaskStatus: mockUpdateTaskStatus,
      });

      render(<TaskTimer task={mockTask} onClose={mockOnClose} />);
      
      expect(screen.getByText('Timer stopped')).toBeInTheDocument();
      
      // Start timer
      const startButton = screen.getByTestId('start-timer-button');
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText('Timer running')).toBeInTheDocument();
      });
    });
  });
});
