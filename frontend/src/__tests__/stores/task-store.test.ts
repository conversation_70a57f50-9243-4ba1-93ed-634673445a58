/**
 * Tests for task management store.
 * 
 * This module contains comprehensive unit tests for the task store,
 * following TDD principles to ensure robust task management functionality.
 */

import { renderHook, act } from '@testing-library/react';
import { useTaskStore } from '@/stores/task-store';
import { TaskStatus, TaskPriority, Task, CreateTaskRequest, UpdateTaskRequest } from '@/types/task';
import * as apiClient from '@/lib/api-client';

// Mock the API client
jest.mock('@/lib/api-client');

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('TaskStore', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Reset the store state
    const { result } = renderHook(() => useTaskStore());
    act(() => {
      result.current.reset();
    });
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useTaskStore());
      
      expect(result.current.tasks).toEqual([]);
      expect(result.current.currentTask).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.filters).toEqual({});
      expect(result.current.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 0,
        hasMore: false,
      });
    });
  });

  describe('Task Loading', () => {
    const mockTasks: Task[] = [
      {
        id: '1',
        title: 'Test Task 1',
        description: 'First test task',
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        estimatedHours: 2,
        actualHours: 0,
        tags: ['test'],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
      {
        id: '2',
        title: 'Test Task 2',
        description: 'Second test task',
        status: TaskStatus.IN_PROGRESS,
        priority: TaskPriority.MEDIUM,
        estimatedHours: 4,
        actualHours: 1.5,
        tags: ['test', 'important'],
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
      },
    ];

    it('should load tasks successfully', async () => {
      mockApiClient.get.mockResolvedValueOnce({
        data: {
          tasks: mockTasks,
          total: 2,
          hasMore: false,
        },
        success: true,
      });

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.loadTasks();
      });

      expect(result.current.tasks).toEqual(mockTasks);
      expect(result.current.pagination.total).toBe(2);
      expect(result.current.pagination.hasMore).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle loading error', async () => {
      const errorMessage = 'Failed to load tasks';
      mockApiClient.get.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.loadTasks();
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should set loading state during API call', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockApiClient.get.mockReturnValueOnce(promise);

      const { result } = renderHook(() => useTaskStore());

      act(() => {
        result.current.loadTasks();
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolvePromise!({
          data: {
            tasks: mockTasks,
            total: 2,
            hasMore: false,
          },
        });
        await promise;
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('should apply filters when loading tasks', async () => {
      const filters = {
        status: [TaskStatus.TODO],
        priority: [TaskPriority.HIGH],
        search: 'test',
      };

      mockApiClient.get.mockResolvedValueOnce({
        data: {
          tasks: [mockTasks[0]],
          total: 1,
          hasMore: false,
        },
      });

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        result.current.setFilters(filters);
        await result.current.loadTasks();
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/tasks', {
        params: expect.objectContaining({
          status: 'todo',
          priority: 'high',
          search: 'test',
          limit: 20,
          offset: 0,
        }),
      });
    });
  });

  describe('Task Creation', () => {
    const newTaskRequest: CreateTaskRequest = {
      title: 'New Task',
      description: 'A new task to create',
      priority: TaskPriority.MEDIUM,
      estimatedHours: 3,
      tags: ['new'],
    };

    const createdTask: Task = {
      id: '3',
      title: newTaskRequest.title,
      description: newTaskRequest.description,
      status: TaskStatus.TODO,
      priority: newTaskRequest.priority!,
      estimatedHours: newTaskRequest.estimatedHours!,
      actualHours: 0,
      tags: newTaskRequest.tags!,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should create task successfully', async () => {
      mockApiClient.post.mockResolvedValueOnce({
        data: createdTask,
      });

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.createTask(newTaskRequest);
      });

      expect(mockApiClient.post).toHaveBeenCalledWith('/api/v1/tasks', newTaskRequest);
      expect(result.current.tasks).toContain(createdTask);
      expect(result.current.error).toBeNull();
    });

    it('should handle creation error', async () => {
      const errorMessage = 'Failed to create task';
      mockApiClient.post.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.createTask(newTaskRequest);
      });

      expect(result.current.error).toBe(errorMessage);
      expect(result.current.tasks).not.toContain(createdTask);
    });
  });

  describe('Task Updates', () => {
    const existingTask: Task = {
      id: '1',
      title: 'Existing Task',
      description: 'An existing task',
      status: TaskStatus.TODO,
      priority: TaskPriority.LOW,
      estimatedHours: 2,
      actualHours: 0,
      tags: ['existing'],
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    };

    const updateRequest: UpdateTaskRequest = {
      title: 'Updated Task',
      status: TaskStatus.IN_PROGRESS,
      priority: TaskPriority.HIGH,
    };

    const updatedTask: Task = {
      ...existingTask,
      ...updateRequest,
      updatedAt: new Date(),
    };

    beforeEach(() => {
      const { result } = renderHook(() => useTaskStore());
      act(() => {
        result.current.setTasks([existingTask]);
      });
    });

    it('should update task successfully', async () => {
      mockApiClient.put.mockResolvedValueOnce({
        data: updatedTask,
      });

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.updateTask(existingTask.id, updateRequest);
      });

      expect(mockApiClient.put).toHaveBeenCalledWith(`/api/v1/tasks/${existingTask.id}`, updateRequest);
      
      const taskInStore = result.current.tasks.find(t => t.id === existingTask.id);
      expect(taskInStore).toEqual(updatedTask);
      expect(result.current.error).toBeNull();
    });

    it('should handle update error', async () => {
      const errorMessage = 'Failed to update task';
      mockApiClient.put.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.updateTask(existingTask.id, updateRequest);
      });

      expect(result.current.error).toBe(errorMessage);
      
      // Task should remain unchanged
      const taskInStore = result.current.tasks.find(t => t.id === existingTask.id);
      expect(taskInStore).toEqual(existingTask);
    });

    it('should update task status optimistically', async () => {
      // Simulate slow API response
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockApiClient.put.mockReturnValueOnce(promise);

      const { result } = renderHook(() => useTaskStore());

      act(() => {
        result.current.updateTaskStatus(existingTask.id, TaskStatus.DONE);
      });

      // Task should be updated optimistically
      const taskInStore = result.current.tasks.find(t => t.id === existingTask.id);
      expect(taskInStore?.status).toBe(TaskStatus.DONE);

      await act(async () => {
        resolvePromise!({ data: { ...existingTask, status: TaskStatus.DONE } });
        await promise;
      });
    });
  });

  describe('Task Deletion', () => {
    const taskToDelete: Task = {
      id: '1',
      title: 'Task to Delete',
      description: 'This task will be deleted',
      status: TaskStatus.TODO,
      priority: TaskPriority.LOW,
      estimatedHours: 1,
      actualHours: 0,
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    beforeEach(() => {
      const { result } = renderHook(() => useTaskStore());
      act(() => {
        result.current.setTasks([taskToDelete]);
      });
    });

    it('should delete task successfully', async () => {
      mockApiClient.delete.mockResolvedValueOnce({});

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.deleteTask(taskToDelete.id);
      });

      expect(mockApiClient.delete).toHaveBeenCalledWith(`/api/v1/tasks/${taskToDelete.id}`);
      expect(result.current.tasks).not.toContain(taskToDelete);
      expect(result.current.error).toBeNull();
    });

    it('should handle deletion error', async () => {
      const errorMessage = 'Failed to delete task';
      mockApiClient.delete.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useTaskStore());

      await act(async () => {
        await result.current.deleteTask(taskToDelete.id);
      });

      expect(result.current.error).toBe(errorMessage);
      expect(result.current.tasks).toContain(taskToDelete);
    });
  });

  describe('Filtering and Sorting', () => {
    const tasks: Task[] = [
      {
        id: '1',
        title: 'High Priority Task',
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        estimatedHours: 2,
        actualHours: 0,
        tags: ['urgent'],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
      {
        id: '2',
        title: 'Low Priority Task',
        status: TaskStatus.DONE,
        priority: TaskPriority.LOW,
        estimatedHours: 1,
        actualHours: 1,
        tags: ['easy'],
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
      },
    ];

    beforeEach(() => {
      const { result } = renderHook(() => useTaskStore());
      act(() => {
        result.current.setTasks(tasks);
      });
    });

    it('should filter tasks by status', () => {
      const { result } = renderHook(() => useTaskStore());

      act(() => {
        result.current.setFilters({ status: [TaskStatus.TODO] });
      });

      const filteredTasks = result.current.getFilteredTasks();
      expect(filteredTasks).toHaveLength(1);
      expect(filteredTasks[0].status).toBe(TaskStatus.TODO);
    });

    it('should filter tasks by priority', () => {
      const { result } = renderHook(() => useTaskStore());

      act(() => {
        result.current.setFilters({ priority: [TaskPriority.HIGH] });
      });

      const filteredTasks = result.current.getFilteredTasks();
      expect(filteredTasks).toHaveLength(1);
      expect(filteredTasks[0].priority).toBe(TaskPriority.HIGH);
    });

    it('should search tasks by title', () => {
      const { result } = renderHook(() => useTaskStore());

      act(() => {
        result.current.setFilters({ search: 'High' });
      });

      const filteredTasks = result.current.getFilteredTasks();
      expect(filteredTasks).toHaveLength(1);
      expect(filteredTasks[0].title).toContain('High');
    });

    it('should sort tasks by priority', () => {
      const { result } = renderHook(() => useTaskStore());

      act(() => {
        result.current.setFilters({ 
          sortBy: 'priority',
          sortOrder: 'desc'
        });
      });

      const sortedTasks = result.current.getFilteredTasks();
      expect(sortedTasks[0].priority).toBe(TaskPriority.HIGH);
      expect(sortedTasks[1].priority).toBe(TaskPriority.LOW);
    });
  });

  describe('Task Analytics', () => {
    const tasks: Task[] = [
      {
        id: '1',
        title: 'Completed Task',
        status: TaskStatus.DONE,
        priority: TaskPriority.HIGH,
        estimatedHours: 2,
        actualHours: 2.5,
        tags: [],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        completedAt: new Date('2024-01-01'),
      },
      {
        id: '2',
        title: 'In Progress Task',
        status: TaskStatus.IN_PROGRESS,
        priority: TaskPriority.MEDIUM,
        estimatedHours: 4,
        actualHours: 1,
        tags: [],
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
      },
      {
        id: '3',
        title: 'Todo Task',
        status: TaskStatus.TODO,
        priority: TaskPriority.LOW,
        estimatedHours: 1,
        actualHours: 0,
        tags: [],
        createdAt: new Date('2024-01-03'),
        updatedAt: new Date('2024-01-03'),
      },
    ];

    beforeEach(() => {
      const { result } = renderHook(() => useTaskStore());
      act(() => {
        result.current.setTasks(tasks);
      });
    });

    it('should calculate task statistics correctly', () => {
      const { result } = renderHook(() => useTaskStore());

      const stats = result.current.getTaskStatistics();

      expect(stats.totalTasks).toBe(3);
      expect(stats.completedTasks).toBe(1);
      expect(stats.inProgressTasks).toBe(1);
      expect(stats.todoTasks).toBe(1);
      expect(stats.totalTimeSpent).toBe(3.5); // 2.5 + 1 + 0
      expect(stats.completionRate).toBe(33.33); // 1/3 * 100
    });

    it('should calculate priority distribution', () => {
      const { result } = renderHook(() => useTaskStore());

      const stats = result.current.getTaskStatistics();

      expect(stats.tasksByPriority.high).toBe(1);
      expect(stats.tasksByPriority.medium).toBe(1);
      expect(stats.tasksByPriority.low).toBe(1);
    });
  });
});
