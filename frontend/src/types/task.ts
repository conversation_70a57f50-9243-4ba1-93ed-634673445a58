/**
 * Task management types for 8760 Hours application.
 * 
 * This module defines the core task management data types including:
 * - Task entity with status, priority, and relationships
 * - Time tracking entries
 * - Task dependencies
 * - Integration with existing goal and life area types
 */

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  DONE = 'done',
  BLOCKED = 'blocked',
  DEFERRED = 'deferred',
}

export enum TaskPriority {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
}

export enum DependencyType {
  DEPENDS_ON = 'depends_on',  // Task A depends on task B to be completed first
  REQUIRES = 'requires',      // Task A requires a component from task B
  ENABLES = 'enables',        // Task A enables task B to be started
}

export interface TaskDependency {
  id: string;
  sourceTaskId: string;
  targetTaskId: string;
  dependencyType: DependencyType;
  description?: string;
  createdAt: Date;
}

export interface TimeEntry {
  id: string;
  taskId: string;
  startTime: Date;
  endTime?: Date;
  durationMinutes: number;
  notes?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  
  // Relationships
  goalId?: string;
  lifeAreaId?: string;
  parentTaskId?: string;
  
  // Time tracking
  estimatedHours: number;
  actualHours: number;
  
  // Dates
  dueDate?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Related data (populated by API)
  goal?: {
    id: string;
    title: string;
    lifeAreaId: string;
  };
  lifeArea?: {
    id: string;
    name: string;
  };
  parentTask?: {
    id: string;
    title: string;
  };
  subtasks?: Task[];
  timeEntries?: TimeEntry[];
  dependencies?: TaskDependency[];
  dependents?: TaskDependency[];
  tags: string[];
}

export interface CreateTaskRequest {
  title: string;
  description?: string;
  priority?: TaskPriority;
  goalId?: string;
  lifeAreaId?: string;
  parentTaskId?: string;
  estimatedHours?: number;
  dueDate?: Date;
  tags?: string[];
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  goalId?: string;
  lifeAreaId?: string;
  parentTaskId?: string;
  estimatedHours?: number;
  actualHours?: number;
  dueDate?: Date;
  tags?: string[];
}

export interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  goalId?: string;
  lifeAreaId?: string;
  parentTaskId?: string;
  tags?: string[];
  dueBefore?: Date;
  dueAfter?: Date;
  search?: string;
  sortBy?: 'title' | 'status' | 'priority' | 'dueDate' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface TaskListResponse {
  tasks: Task[];
  total: number;
  hasMore: boolean;
}

export interface CreateTimeEntryRequest {
  taskId: string;
  startTime?: Date;
  notes?: string;
  tags?: string[];
}

export interface UpdateTimeEntryRequest {
  endTime?: Date;
  durationMinutes?: number;
  notes?: string;
  tags?: string[];
}

export interface TimeEntryFilters {
  taskId?: string;
  startAfter?: Date;
  startBefore?: Date;
  tags?: string[];
  sortBy?: 'startTime' | 'durationMinutes' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface TimeEntryListResponse {
  timeEntries: TimeEntry[];
  total: number;
  hasMore: boolean;
}

export interface CreateDependencyRequest {
  sourceTaskId: string;
  targetTaskId: string;
  dependencyType: DependencyType;
  description?: string;
}

export interface TaskAnalytics {
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks: number;
  totalTimeSpent: number; // in hours
  averageCompletionTime: number; // in hours
  tasksByPriority: {
    high: number;
    medium: number;
    low: number;
  };
  tasksByStatus: {
    todo: number;
    inProgress: number;
    done: number;
    blocked: number;
    deferred: number;
  };
  tasksByLifeArea: Array<{
    lifeAreaId: string;
    lifeAreaName: string;
    taskCount: number;
    completedCount: number;
    timeSpent: number;
  }>;
  productivityTrends: Array<{
    date: string;
    tasksCompleted: number;
    timeSpent: number;
  }>;
}

export interface TaskValidationError {
  field: string;
  message: string;
}

export interface TaskApiError {
  message: string;
  code: string;
  validationErrors?: TaskValidationError[];
}

// Utility types for task operations
export type TaskWithProgress = Task & {
  progressPercentage: number;
  isOverdue: boolean;
  canStart: boolean; // based on dependencies
  estimatedCompletionDate?: Date;
};

export type TaskSummary = Pick<Task, 'id' | 'title' | 'status' | 'priority' | 'dueDate'> & {
  progressPercentage: number;
  timeSpent: number;
};

export type TaskTreeNode = Task & {
  children: TaskTreeNode[];
  depth: number;
  hasChildren: boolean;
};

// Constants
export const TASK_STATUS_LABELS: Record<TaskStatus, string> = {
  [TaskStatus.TODO]: 'To Do',
  [TaskStatus.IN_PROGRESS]: 'In Progress',
  [TaskStatus.DONE]: 'Done',
  [TaskStatus.BLOCKED]: 'Blocked',
  [TaskStatus.DEFERRED]: 'Deferred',
};

export const TASK_PRIORITY_LABELS: Record<TaskPriority, string> = {
  [TaskPriority.HIGH]: 'High',
  [TaskPriority.MEDIUM]: 'Medium',
  [TaskPriority.LOW]: 'Low',
};

export const DEPENDENCY_TYPE_LABELS: Record<DependencyType, string> = {
  [DependencyType.DEPENDS_ON]: 'Depends On',
  [DependencyType.REQUIRES]: 'Requires',
  [DependencyType.ENABLES]: 'Enables',
};

// Helper functions
export const isTaskCompleted = (task: Task): boolean => {
  return task.status === TaskStatus.DONE;
};

export const isTaskOverdue = (task: Task): boolean => {
  if (!task.dueDate || isTaskCompleted(task)) {
    return false;
  }
  return new Date() > task.dueDate;
};

export const calculateTaskProgress = (task: Task): number => {
  if (isTaskCompleted(task)) {
    return 100;
  }
  
  if (!task.subtasks || task.subtasks.length === 0) {
    return task.status === TaskStatus.IN_PROGRESS ? 50 : 0;
  }
  
  const completedSubtasks = task.subtasks.filter(isTaskCompleted).length;
  return (completedSubtasks / task.subtasks.length) * 100;
};

export const calculateActualHours = (timeEntries: TimeEntry[]): number => {
  return timeEntries.reduce((total, entry) => total + (entry.durationMinutes / 60), 0);
};

export const getTaskStatusColor = (status: TaskStatus): string => {
  switch (status) {
    case TaskStatus.TODO:
      return 'gray';
    case TaskStatus.IN_PROGRESS:
      return 'blue';
    case TaskStatus.DONE:
      return 'green';
    case TaskStatus.BLOCKED:
      return 'red';
    case TaskStatus.DEFERRED:
      return 'yellow';
    default:
      return 'gray';
  }
};

export const getTaskPriorityColor = (priority: TaskPriority): string => {
  switch (priority) {
    case TaskPriority.HIGH:
      return 'red';
    case TaskPriority.MEDIUM:
      return 'yellow';
    case TaskPriority.LOW:
      return 'green';
    default:
      return 'gray';
  }
};
