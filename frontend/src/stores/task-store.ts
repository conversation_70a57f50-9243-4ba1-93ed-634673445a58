/**
 * Task management store using Zustand.
 * 
 * This store manages the state for task management functionality including:
 * - Task CRUD operations
 * - Filtering and sorting
 * - Time tracking
 * - Task analytics
 * - Optimistic updates
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  Task, 
  TaskStatus, 
  TaskPriority,
  CreateTaskRequest, 
  UpdateTaskRequest,
  TaskFilters,
  TaskListResponse,
  TaskAnalytics
} from '@/types/task';
import { apiClient } from '@/lib/api-client';

interface TaskStatistics {
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  todoTasks: number;
  blockedTasks: number;
  deferredTasks: number;
  totalTimeSpent: number;
  completionRate: number;
  tasksByPriority: {
    high: number;
    medium: number;
    low: number;
  };
}

interface TaskPagination {
  page: number;
  limit: number;
  total: number;
  hasMore: boolean;
}

interface TaskStore {
  // State
  tasks: Task[];
  currentTask: Task | null;
  isLoading: boolean;
  error: string | null;
  filters: TaskFilters;
  pagination: TaskPagination;

  // Actions
  loadTasks: (append?: boolean) => Promise<void>;
  loadTask: (id: string) => Promise<void>;
  createTask: (task: CreateTaskRequest) => Promise<Task | null>;
  updateTask: (id: string, updates: UpdateTaskRequest) => Promise<Task | null>;
  updateTaskStatus: (id: string, status: TaskStatus) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  
  // Filtering and sorting
  setFilters: (filters: Partial<TaskFilters>) => void;
  clearFilters: () => void;
  getFilteredTasks: () => Task[];
  
  // Analytics
  getTaskStatistics: () => TaskStatistics;
  getTaskAnalytics: () => Promise<TaskAnalytics | null>;
  
  // Utility
  setTasks: (tasks: Task[]) => void;
  setCurrentTask: (task: Task | null) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

const initialState = {
  tasks: [],
  currentTask: null,
  isLoading: false,
  error: null,
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
  },
};

export const useTaskStore = create<TaskStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      loadTasks: async (append = false) => {
        set({ isLoading: true, error: null });
        
        try {
          const { filters, pagination } = get();
          const offset = append ? (pagination.page - 1) * pagination.limit : 0;
          
          // Convert filters to API parameters
          const params: Record<string, any> = {
            limit: pagination.limit,
            offset,
          };
          
          if (filters.status?.length) {
            params.status = filters.status.join(',');
          }
          if (filters.priority?.length) {
            params.priority = filters.priority.join(',');
          }
          if (filters.goalId) {
            params.goalId = filters.goalId;
          }
          if (filters.lifeAreaId) {
            params.lifeAreaId = filters.lifeAreaId;
          }
          if (filters.search) {
            params.search = filters.search;
          }
          if (filters.sortBy) {
            params.sortBy = filters.sortBy;
            params.sortOrder = filters.sortOrder || 'asc';
          }

          const response = await apiClient.get<TaskListResponse>('tasks', 'list', params);
          const { tasks, total, hasMore } = response.data;

          set((state) => ({
            tasks: append ? [...state.tasks, ...tasks] : tasks,
            pagination: {
              ...state.pagination,
              total,
              hasMore,
              page: append ? state.pagination.page + 1 : 1,
            },
            isLoading: false,
          }));
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to load tasks' 
          });
        }
      },

      loadTask: async (id: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.get<Task>('tasks', 'get', { id });
          set({ currentTask: response.data, isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to load task' 
          });
        }
      },

      createTask: async (taskData: CreateTaskRequest) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.post<Task>('tasks', 'create', taskData);
          const newTask = response.data;
          
          set((state) => ({
            tasks: [newTask, ...state.tasks],
            pagination: {
              ...state.pagination,
              total: state.pagination.total + 1,
            },
            isLoading: false,
          }));
          
          return newTask;
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to create task' 
          });
          return null;
        }
      },

      updateTask: async (id: string, updates: UpdateTaskRequest) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.put<Task>('tasks', 'update', updates, { id });
          const updatedTask = response.data;
          
          set((state) => ({
            tasks: state.tasks.map(task => 
              task.id === id ? updatedTask : task
            ),
            currentTask: state.currentTask?.id === id ? updatedTask : state.currentTask,
            isLoading: false,
          }));
          
          return updatedTask;
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to update task' 
          });
          return null;
        }
      },

      updateTaskStatus: async (id: string, status: TaskStatus) => {
        // Optimistic update
        set((state) => ({
          tasks: state.tasks.map(task => 
            task.id === id 
              ? { 
                  ...task, 
                  status, 
                  completedAt: status === TaskStatus.DONE ? new Date() : task.completedAt,
                  updatedAt: new Date()
                } 
              : task
          ),
        }));

        try {
          const updates: UpdateTaskRequest = { 
            status,
            ...(status === TaskStatus.DONE && { completedAt: new Date() })
          };
          
          await apiClient.put('tasks', 'update', updates, { id });
        } catch (error) {
          // Revert optimistic update on error
          const { tasks } = get();
          const originalTask = tasks.find(t => t.id === id);
          if (originalTask) {
            set((state) => ({
              tasks: state.tasks.map(task => 
                task.id === id ? originalTask : task
              ),
              error: error instanceof Error ? error.message : 'Failed to update task status'
            }));
          }
        }
      },

      deleteTask: async (id: string) => {
        set({ isLoading: true, error: null });
        
        try {
          await apiClient.delete('tasks', 'delete', { id });
          
          set((state) => ({
            tasks: state.tasks.filter(task => task.id !== id),
            currentTask: state.currentTask?.id === id ? null : state.currentTask,
            pagination: {
              ...state.pagination,
              total: Math.max(0, state.pagination.total - 1),
            },
            isLoading: false,
          }));
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to delete task' 
          });
        }
      },

      setFilters: (newFilters: Partial<TaskFilters>) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
          pagination: { ...state.pagination, page: 1 }, // Reset pagination
        }));
      },

      clearFilters: () => {
        set({ 
          filters: {},
          pagination: { ...get().pagination, page: 1 }
        });
      },

      getFilteredTasks: () => {
        const { tasks, filters } = get();
        let filteredTasks = [...tasks];

        // Apply status filter
        if (filters.status?.length) {
          filteredTasks = filteredTasks.filter(task => 
            filters.status!.includes(task.status)
          );
        }

        // Apply priority filter
        if (filters.priority?.length) {
          filteredTasks = filteredTasks.filter(task => 
            filters.priority!.includes(task.priority)
          );
        }

        // Apply search filter
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filteredTasks = filteredTasks.filter(task => 
            task.title.toLowerCase().includes(searchLower) ||
            task.description?.toLowerCase().includes(searchLower) ||
            task.tags.some(tag => tag.toLowerCase().includes(searchLower))
          );
        }

        // Apply goal filter
        if (filters.goalId) {
          filteredTasks = filteredTasks.filter(task => task.goalId === filters.goalId);
        }

        // Apply life area filter
        if (filters.lifeAreaId) {
          filteredTasks = filteredTasks.filter(task => task.lifeAreaId === filters.lifeAreaId);
        }

        // Apply sorting
        if (filters.sortBy) {
          filteredTasks.sort((a, b) => {
            const aValue = a[filters.sortBy as keyof Task];
            const bValue = b[filters.sortBy as keyof Task];
            
            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            if (aValue > bValue) comparison = 1;
            
            return filters.sortOrder === 'desc' ? -comparison : comparison;
          });
        }

        return filteredTasks;
      },

      getTaskStatistics: () => {
        const { tasks } = get();
        
        const stats: TaskStatistics = {
          totalTasks: tasks.length,
          completedTasks: tasks.filter(t => t.status === TaskStatus.DONE).length,
          inProgressTasks: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length,
          todoTasks: tasks.filter(t => t.status === TaskStatus.TODO).length,
          blockedTasks: tasks.filter(t => t.status === TaskStatus.BLOCKED).length,
          deferredTasks: tasks.filter(t => t.status === TaskStatus.DEFERRED).length,
          totalTimeSpent: tasks.reduce((total, task) => total + task.actualHours, 0),
          completionRate: tasks.length > 0 
            ? Math.round((tasks.filter(t => t.status === TaskStatus.DONE).length / tasks.length) * 10000) / 100
            : 0,
          tasksByPriority: {
            high: tasks.filter(t => t.priority === TaskPriority.HIGH).length,
            medium: tasks.filter(t => t.priority === TaskPriority.MEDIUM).length,
            low: tasks.filter(t => t.priority === TaskPriority.LOW).length,
          },
        };
        
        return stats;
      },

      getTaskAnalytics: async () => {
        try {
          const response = await apiClient.get<TaskAnalytics>('tasks', 'analytics');
          return response.data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to load analytics' });
          return null;
        }
      },

      setTasks: (tasks: Task[]) => {
        set({ tasks });
      },

      setCurrentTask: (task: Task | null) => {
        set({ currentTask: task });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'task-store',
    }
  )
);
