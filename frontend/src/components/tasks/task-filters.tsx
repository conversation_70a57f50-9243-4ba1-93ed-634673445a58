'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, X, Filter } from 'lucide-react';
import { TaskStatus, TaskPriority, TaskFilters as TaskFiltersType, TASK_STATUS_LABELS, TASK_PRIORITY_LABELS } from '@/types/task';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

interface TaskFiltersProps {
  onFiltersChange: (filters: Partial<TaskFiltersType>) => void;
  onClose: () => void;
  className?: string;
}

export function TaskFilters({
  onFiltersChange,
  onClose,
  className,
}: TaskFiltersProps) {
  const [filters, setFilters] = useState<Partial<TaskFiltersType>>({
    status: [],
    priority: [],
    tags: [],
  });

  const [showDueBefore, setShowDueBefore] = useState(false);
  const [showDueAfter, setShowDueAfter] = useState(false);

  const handleStatusChange = (status: TaskStatus, checked: boolean) => {
    setFilters(prev => {
      const currentStatus = prev.status || [];
      const newStatus = checked
        ? [...currentStatus, status]
        : currentStatus.filter(s => s !== status);
      
      return { ...prev, status: newStatus };
    });
  };

  const handlePriorityChange = (priority: TaskPriority, checked: boolean) => {
    setFilters(prev => {
      const currentPriority = prev.priority || [];
      const newPriority = checked
        ? [...currentPriority, priority]
        : currentPriority.filter(p => p !== priority);
      
      return { ...prev, priority: newPriority };
    });
  };

  const handleDateChange = (field: 'dueBefore' | 'dueAfter', date: Date | undefined) => {
    setFilters(prev => ({ ...prev, [field]: date }));
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      status: [],
      priority: [],
      tags: [],
      dueBefore: undefined,
      dueAfter: undefined,
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const handleApplyFilters = () => {
    onFiltersChange(filters);
    onClose();
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.status && filters.status.length > 0) count++;
    if (filters.priority && filters.priority.length > 0) count++;
    if (filters.tags && filters.tags.length > 0) count++;
    if (filters.dueBefore) count++;
    if (filters.dueAfter) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="text-lg">Filters</CardTitle>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            data-testid="close-filters-button"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Status filters */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Status</h4>
          <div className="space-y-2">
            {Object.values(TaskStatus).map((status) => (
              <div key={status} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status}`}
                  checked={filters.status?.includes(status) || false}
                  onCheckedChange={(checked) => handleStatusChange(status, checked as boolean)}
                  data-testid={`status-filter-${status}`}
                />
                <label
                  htmlFor={`status-${status}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {TASK_STATUS_LABELS[status]}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Priority filters */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Priority</h4>
          <div className="space-y-2">
            {Object.values(TaskPriority).map((priority) => (
              <div key={priority} className="flex items-center space-x-2">
                <Checkbox
                  id={`priority-${priority}`}
                  checked={filters.priority?.includes(priority) || false}
                  onCheckedChange={(checked) => handlePriorityChange(priority, checked as boolean)}
                  data-testid={`priority-filter-${priority}`}
                />
                <label
                  htmlFor={`priority-${priority}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {TASK_PRIORITY_LABELS[priority]}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Due date filters */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Due Date</h4>
          <div className="space-y-3">
            <div className="space-y-2">
              <label className="text-sm text-muted-foreground">Due before</label>
              <Popover open={showDueBefore} onOpenChange={setShowDueBefore}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !filters.dueBefore && 'text-muted-foreground'
                    )}
                    data-testid="due-before-button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dueBefore ? format(filters.dueBefore, 'PPP') : 'Pick a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={filters.dueBefore}
                    onSelect={(date) => {
                      handleDateChange('dueBefore', date);
                      setShowDueBefore(false);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <label className="text-sm text-muted-foreground">Due after</label>
              <Popover open={showDueAfter} onOpenChange={setShowDueAfter}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !filters.dueAfter && 'text-muted-foreground'
                    )}
                    data-testid="due-after-button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dueAfter ? format(filters.dueAfter, 'PPP') : 'Pick a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={filters.dueAfter}
                    onSelect={(date) => {
                      handleDateChange('dueAfter', date);
                      setShowDueAfter(false);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center space-x-2 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearFilters}
            disabled={activeFiltersCount === 0}
            data-testid="clear-filters-button"
          >
            Clear All
          </Button>
          <Button
            size="sm"
            onClick={handleApplyFilters}
            data-testid="apply-filters-button"
          >
            Apply Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
