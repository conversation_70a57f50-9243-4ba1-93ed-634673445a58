'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Search, Filter, SortAsc, SortDesc } from 'lucide-react';
import { Task, TaskStatus, TaskPriority, TaskFilters } from '@/types/task';
import { TaskCard } from './task-card';
import { TaskForm } from './task-form';
import { TaskFilters as TaskFiltersComponent } from './task-filters';
import { useTaskStore } from '@/stores/task-store';
import { cn } from '@/lib/utils';

interface TaskListProps {
  className?: string;
  showCreateButton?: boolean;
  showFilters?: boolean;
  showSearch?: boolean;
  compact?: boolean;
  goalId?: string;
  lifeAreaId?: string;
}

export function TaskList({
  className,
  showCreateButton = true,
  showFilters = true,
  showSearch = true,
  compact = false,
  goalId,
  lifeAreaId,
}: TaskListProps) {
  const {
    tasks,
    isLoading,
    error,
    filters,
    loadTasks,
    setFilters,
    getFilteredTasks,
  } = useTaskStore();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'title' | 'status' | 'priority' | 'dueDate' | 'createdAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Load tasks on component mount
  useEffect(() => {
    const initialFilters: Partial<TaskFilters> = {};
    if (goalId) initialFilters.goalId = goalId;
    if (lifeAreaId) initialFilters.lifeAreaId = lifeAreaId;
    
    setFilters(initialFilters);
    loadTasks();
  }, [goalId, lifeAreaId, loadTasks, setFilters]);

  // Update search filter
  useEffect(() => {
    setFilters({ search: searchQuery || undefined });
  }, [searchQuery, setFilters]);

  // Update sort filters
  useEffect(() => {
    setFilters({ sortBy, sortOrder });
  }, [sortBy, sortOrder, setFilters]);

  const filteredTasks = getFilteredTasks();

  const handleCreateTask = () => {
    setShowCreateForm(true);
  };

  const handleTaskCreated = () => {
    setShowCreateForm(false);
    loadTasks(); // Refresh task list
  };

  const toggleSort = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  if (error) {
    return (
      <Card className={cn('border-red-200 dark:border-red-800', className)}>
        <CardContent className="p-6">
          <div className="text-center text-red-600 dark:text-red-400">
            <p className="font-medium">Error loading tasks</p>
            <p className="text-sm mt-1">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-3"
              onClick={() => loadTasks()}
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header with actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h2 className="text-xl font-semibold">Tasks</h2>
          <Badge variant="secondary" className="text-xs">
            {filteredTasks.length}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          {showCreateButton && (
            <Button 
              size="sm" 
              onClick={handleCreateTask}
              data-testid="create-task-button"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Task
            </Button>
          )}
        </div>
      </div>

      {/* Search and filters */}
      {(showSearch || showFilters) && (
        <div className="flex items-center space-x-2">
          {showSearch && (
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                data-testid="task-search-input"
              />
            </div>
          )}
          
          <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="title">Title</SelectItem>
              <SelectItem value="status">Status</SelectItem>
              <SelectItem value="priority">Priority</SelectItem>
              <SelectItem value="dueDate">Due Date</SelectItem>
              <SelectItem value="createdAt">Created</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={toggleSort}
            data-testid="sort-order-button"
          >
            {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
          </Button>
          
          {showFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              data-testid="filters-toggle-button"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          )}
        </div>
      )}

      {/* Filters panel */}
      {showFiltersPanel && (
        <TaskFiltersComponent
          onFiltersChange={setFilters}
          onClose={() => setShowFiltersPanel(false)}
        />
      )}

      {/* Task list */}
      <div className="space-y-3" data-testid="task-list">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredTasks.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-muted-foreground">
                <p className="font-medium">No tasks found</p>
                <p className="text-sm mt-1">
                  {searchQuery || Object.keys(filters).length > 0
                    ? 'Try adjusting your search or filters'
                    : 'Create your first task to get started'
                  }
                </p>
                {showCreateButton && !searchQuery && Object.keys(filters).length === 0 && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-3"
                    onClick={handleCreateTask}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Task
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredTasks.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              compact={compact}
              data-testid={`task-item-${task.id}`}
            />
          ))
        )}
      </div>

      {/* Create task form modal */}
      {showCreateForm && (
        <TaskForm
          goalId={goalId}
          lifeAreaId={lifeAreaId}
          onSuccess={handleTaskCreated}
          onCancel={() => setShowCreateForm(false)}
        />
      )}
    </div>
  );
}
