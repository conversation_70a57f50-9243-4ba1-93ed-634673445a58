'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  Calendar, 
  Tag, 
  Play, 
  Pause, 
  Square, 
  Edit, 
  Trash2, 
  CheckCircle,
  Circle,
  AlertTriangle,
  Timer
} from 'lucide-react';
import { Task, TaskStatus, TaskPriority, TASK_STATUS_LABELS, TASK_PRIORITY_LABELS } from '@/types/task';
import { useTaskStore } from '@/stores/task-store';
import { TaskForm } from './task-form';
import { TaskTimer } from './task-timer';
import { cn } from '@/lib/utils';
import { formatDistanceToNow, format } from 'date-fns';

interface TaskCardProps {
  task: Task;
  compact?: boolean;
  showTimer?: boolean;
  showActions?: boolean;
  className?: string;
}

export function TaskCard({
  task,
  compact = false,
  showTimer = true,
  showActions = true,
  className,
}: TaskCardProps) {
  const { updateTaskStatus, deleteTask } = useTaskStore();
  const [showEditForm, setShowEditForm] = useState(false);
  const [showTimerModal, setShowTimerModal] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.TODO:
        return <Circle className="h-4 w-4" />;
      case TaskStatus.IN_PROGRESS:
        return <Play className="h-4 w-4 text-blue-500" />;
      case TaskStatus.DONE:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case TaskStatus.BLOCKED:
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case TaskStatus.DEFERRED:
        return <Pause className="h-4 w-4 text-yellow-500" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.TODO:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case TaskStatus.DONE:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case TaskStatus.BLOCKED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case TaskStatus.DEFERRED:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case TaskPriority.HIGH:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case TaskPriority.MEDIUM:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const handleStatusChange = async (newStatus: TaskStatus) => {
    if (isUpdating) return;
    
    setIsUpdating(true);
    try {
      await updateTaskStatus(task.id, newStatus);
    } catch (error) {
      console.error('Failed to update task status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      try {
        await deleteTask(task.id);
      } catch (error) {
        console.error('Failed to delete task:', error);
      }
    }
  };

  const handleEditSuccess = () => {
    setShowEditForm(false);
  };

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== TaskStatus.DONE;
  const progressPercentage = task.estimatedHours > 0 ? (task.actualHours / task.estimatedHours) * 100 : 0;

  return (
    <>
      <Card className={cn(
        'transition-all duration-200 hover:shadow-md',
        task.status === TaskStatus.DONE && 'opacity-75',
        isOverdue && 'border-red-200 dark:border-red-800',
        className
      )}>
        <CardHeader className={cn('pb-3', compact && 'pb-2')}>
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1 min-w-0">
              {/* Status toggle button */}
              <button
                onClick={() => handleStatusChange(
                  task.status === TaskStatus.DONE ? TaskStatus.TODO : TaskStatus.DONE
                )}
                disabled={isUpdating}
                className={cn(
                  'mt-1 p-1 rounded-full transition-colors',
                  'hover:bg-muted focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1',
                  isUpdating && 'opacity-50 cursor-not-allowed'
                )}
                data-testid="task-status-toggle"
              >
                {getStatusIcon(task.status)}
              </button>

              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className={cn(
                    'font-medium text-sm truncate',
                    task.status === TaskStatus.DONE && 'line-through text-muted-foreground'
                  )}>
                    {task.title}
                  </h3>
                  
                  <Badge className={cn('text-xs', getStatusColor(task.status))}>
                    {TASK_STATUS_LABELS[task.status]}
                  </Badge>
                  
                  <Badge className={cn('text-xs', getPriorityColor(task.priority))}>
                    {TASK_PRIORITY_LABELS[task.priority]}
                  </Badge>
                </div>

                {task.description && !compact && (
                  <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                    {task.description}
                  </p>
                )}

                {/* Task metadata */}
                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                  {task.estimatedHours > 0 && (
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>{task.actualHours}h / {task.estimatedHours}h</span>
                    </div>
                  )}
                  
                  {task.dueDate && (
                    <div className={cn(
                      'flex items-center space-x-1',
                      isOverdue && 'text-red-500'
                    )}>
                      <Calendar className="h-3 w-3" />
                      <span>
                        {isOverdue ? 'Overdue' : 'Due'} {formatDistanceToNow(new Date(task.dueDate), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                  
                  {task.tags.length > 0 && (
                    <div className="flex items-center space-x-1">
                      <Tag className="h-3 w-3" />
                      <span>{task.tags.slice(0, 2).join(', ')}{task.tags.length > 2 && '...'}</span>
                    </div>
                  )}
                </div>

                {/* Progress bar */}
                {task.estimatedHours > 0 && !compact && (
                  <div className="mt-2">
                    <Progress 
                      value={Math.min(progressPercentage, 100)} 
                      className="h-2"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            {showActions && (
              <div className="flex items-center space-x-1 ml-2">
                {showTimer && task.status !== TaskStatus.DONE && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowTimerModal(true)}
                    data-testid="task-timer-button"
                  >
                    <Timer className="h-4 w-4" />
                  </Button>
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowEditForm(true)}
                  data-testid="task-edit-button"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDelete}
                  className="text-red-500 hover:text-red-700"
                  data-testid="task-delete-button"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </CardHeader>

        {/* Additional info for non-compact view */}
        {!compact && (task.goal || task.lifeArea || task.subtasks?.length) && (
          <CardContent className="pt-0">
            <div className="space-y-2 text-xs text-muted-foreground">
              {task.goal && (
                <div>Goal: {task.goal.title}</div>
              )}
              
              {task.lifeArea && (
                <div>Life Area: {task.lifeArea.name}</div>
              )}
              
              {task.subtasks && task.subtasks.length > 0 && (
                <div>Subtasks: {task.subtasks.filter(st => st.status === TaskStatus.DONE).length} / {task.subtasks.length} completed</div>
              )}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Edit form modal */}
      {showEditForm && (
        <TaskForm
          task={task}
          onSuccess={handleEditSuccess}
          onCancel={() => setShowEditForm(false)}
        />
      )}

      {/* Timer modal */}
      {showTimerModal && (
        <TaskTimer
          task={task}
          onClose={() => setShowTimerModal(false)}
        />
      )}
    </>
  );
}
